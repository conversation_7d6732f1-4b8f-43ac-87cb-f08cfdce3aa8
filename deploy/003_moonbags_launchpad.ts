import { DeployFunction } from "hardhat-deploy/types";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { uniswapV3Addresses } from "../config/uniswapV3Addresses";

const func: DeployFunction = async function (hre: HardhatRuntimeEnvironment) {
    const { deployments, getNamedAccounts, network } = hre;
    const { deploy, log, get } = deployments;
    const { deployer } = await getNamedAccounts();

    log(`\nDeploying Moonbags Launchpad with Uniswap V3 integration...`);

    const networkConfig = uniswapV3Addresses[network.name];

    if (networkConfig === undefined) {
        log(`No configuration found for network ${network.name}.`);
        return;
    }

    const defaultPoolFee = 3000; // 0.3% fee tier

    try {
        // Get deployed dependencies
        const platformToken = await get("PlatformToken");
        const moonbagsStake = await get("MoonbagsStake");
        const tokenLock = await get("TokenLock");

        log(`Using deployed dependencies:`);
        log(`   Platform Token: ${platformToken.address}`);
        log(`   MoonbagsStake: ${moonbagsStake.address}`);
        log(`   TokenLock: ${tokenLock.address}`);

        const launchpadDeployment = await deploy("MoonbagsLaunchpad", {
            from: deployer,
            log: true,
            proxy: {
                proxyContract: "OpenZeppelinTransparentProxy",
                execute: {
                    init: {
                        methodName: "initialize",
                        args: [
                            networkConfig.positionManager,
                            networkConfig.weth9,
                            defaultPoolFee,
                            platformToken.address,
                            moonbagsStake.address,
                            tokenLock.address,
                        ],
                    },
                },
            },
        });

        log(`✅ MoonbagsLaunchpad deployed at: ${launchpadDeployment.address}`);

        if (networkConfig) {
            log(`✅ HyperEVM configuration set during deployment:`);
            log(`   Position Manager: ${networkConfig.positionManager}`);
            log(`   WETH9: ${networkConfig.weth9}`);
            log(`   Default Pool Fee: ${defaultPoolFee / 100}%`);
            log(`   Platform Token: ${platformToken.address}`);
            log(`   MoonbagsStake: ${moonbagsStake.address}`);
            log(`   TokenLock: ${tokenLock.address}`);
        }
    } catch (error) {
        log(`❌ Error deploying MoonbagsLaunchpad: ${error}`);
        throw error;
    }
};

func.tags = ["MoonbagsLaunchpad"];
func.dependencies = ["PlatformToken", "MoonbagsStake", "TokenLock"];

export default func;

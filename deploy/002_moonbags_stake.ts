import { DeployFunction } from "hardhat-deploy/types";
import { HardhatRuntimeEnvironment } from "hardhat/types";

const func: DeployFunction = async function (hre: HardhatRuntimeEnvironment) {
    const { deployments, getNamedAccounts } = hre;
    const { deploy, log } = deployments;
    const { deployer } = await getNamedAccounts();

    log(`\nDeploying MoonbagsStake contract...`);

    try {
        const moonbagsStakeDeployment = await deploy("MoonbagsStake", {
            from: deployer,
            log: true,
            proxy: {
                proxyContract: "OpenZeppelinTransparentProxy",
                execute: {
                    init: {
                        methodName: "initialize",
                        args: [],
                    },
                },
            },
        });

        log(`✅ MoonbagsStake deployed at: ${moonbagsStakeDeployment.address}`);
    } catch (error) {
        log(`❌ Error deploying MoonbagsStake: ${error}`);
        throw error;
    }
};

func.tags = ["MoonbagsStake"];
func.dependencies = [];

export default func;

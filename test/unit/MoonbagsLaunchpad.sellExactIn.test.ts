import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { expect } from "chai";
import { ethers } from "hardhat";

import {
    INonfungiblePositionManager,
    IWETH9,
    LaunchpadToken,
    MoonbagsLaunchpad,
    MoonbagsStake,
    TokenLock,
} from "../../types";
import { externalFixture } from "./external.fixtures";

describe("MoonbagsLaunchpad - sellExactIn", function () {
    let launchpad: MoonbagsLaunchpad;
    let tokenLock: TokenLock;
    let moonbagsStake: MoonbagsStake;
    let weth9: IWETH9;
    let nonfungiblePositionManager: INonfungiblePositionManager;
    let owner: Signer<PERSON>ithAddress;
    let creator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
    let seller: Signer<PERSON>ithAddress;
    let buyer: SignerWithAddress;
    let treasury: SignerWithAddress;
    let platformToken: LaunchpadToken;
    let testToken: LaunchpadToken;
    let testTokenAddress: string;

    // Token parameters
    const tokenName = "Test Token";
    const tokenSymbol = "TEST";
    const tokenUri = "https://example.com/token";
    const tokenDescription = "A test token for bonding curve";
    const twitter = "https://twitter.com/test";
    const telegram = "https://t.me/test";
    const website = "https://test.com";
    const customThreshold = ethers.parseEther("0.03");

    // Constants from contract
    const DEFAULT_FEE_TIER = 3000;
    const POOL_CREATION_FEE = ethers.parseEther("0.001");

    beforeEach(async function () {
        [owner, creator, seller, buyer, treasury] = await ethers.getSigners();

        const { weth9: _weth9, nonfungiblePositionManager: _nonfungiblePositionManager } =
            await externalFixture();
        weth9 = _weth9;
        nonfungiblePositionManager = _nonfungiblePositionManager;

        // Deploy platform token
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        platformToken = await LaunchpadTokenFactory.deploy(
            "Platform Token",
            "PLAT",
            18,
            "https://platform.com"
        );
        await platformToken.waitForDeployment();

        // Deploy MoonbagsStake
        const MoonbagsStakeFactory = await ethers.getContractFactory("MoonbagsStake");
        moonbagsStake = await MoonbagsStakeFactory.deploy();
        await moonbagsStake.waitForDeployment();
        await moonbagsStake.initialize();

        // Deploy TokenLock
        const TokenLockFactory = await ethers.getContractFactory("TokenLock");
        tokenLock = await TokenLockFactory.deploy();
        await tokenLock.waitForDeployment();
        await tokenLock.initialize();

        // Deploy MoonbagsLaunchpad
        const MoonbagsLaunchpadFactory = await ethers.getContractFactory("MoonbagsLaunchpad");
        launchpad = await MoonbagsLaunchpadFactory.deploy();
        await launchpad.waitForDeployment();

        // Initialize the launchpad
        await launchpad.initialize(
            await nonfungiblePositionManager.getAddress(),
            await weth9.getAddress(),
            DEFAULT_FEE_TIER,
            await platformToken.getAddress(),
            await moonbagsStake.getAddress(),
            await tokenLock.getAddress()
        );

        await launchpad.updateFeeRecipients(treasury.address, treasury.address);

        // Create a test token
        const tx = await launchpad
            .connect(creator)
            .createPool(
                tokenName,
                tokenSymbol,
                tokenUri,
                tokenDescription,
                twitter,
                telegram,
                website,
                customThreshold,
                { value: POOL_CREATION_FEE }
            );

        const receipt = await tx.wait();
        const tokenCreatedEvent = receipt?.logs.find((log) => {
            try {
                const parsed = launchpad.interface.parseLog({
                    topics: log.topics as string[],
                    data: log.data,
                });
                return parsed?.name === "TokenCreated";
            } catch {
                return false;
            }
        });

        const parsedEvent = launchpad.interface.parseLog({
            topics: tokenCreatedEvent!.topics as string[],
            data: tokenCreatedEvent!.data,
        });

        testTokenAddress = parsedEvent?.args[0];
        testToken = await ethers.getContractAt("LaunchpadToken", testTokenAddress);

        // Buy some tokens first so we have tokens to sell (use sufficient amount for bonding curve)
        const buyAmount = ethers.parseEther("0.012"); // Sufficient amount for bonding curve
        const sufficientAmount = ethers.parseEther("0.015"); // Add buffer for fees
        await launchpad
            .connect(seller)
            .buyExactIn(testTokenAddress, buyAmount, 0, { value: sufficientAmount });
    });

    describe("sellExactIn", function () {
        let tokenAmountIn: bigint;
        const amountOutMin = 0; // No minimum ETH expected

        beforeEach(async function () {
            // Get actual token balance and use a portion of it
            const sellerBalance = await testToken.balanceOf(seller.address);
            tokenAmountIn = sellerBalance / 2n; // Use half of the tokens
        });

        it("should sell tokens for exact ETH amount", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Sell Test Token",
                    "SELL",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens first with sufficient amount for bonding curve
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                // Skip this test if pool is already completed
                return;
            }

            // Get seller's initial token and ETH balances
            const initialTokenBalance = await freshToken.balanceOf(seller.address);
            const initialEthBalance = await ethers.provider.getBalance(seller.address);
            const sellAmount = initialTokenBalance / 2n; // Sell half

            if (sellAmount === 0n) {
                // Skip if no tokens to sell
                return;
            }

            const tx = await launchpad
                .connect(seller)
                .sellExactIn(freshTokenAddress, sellAmount, amountOutMin);

            const receipt = await tx.wait();
            const gasUsed = receipt!.gasUsed * receipt!.gasPrice;

            // Check Trade event was emitted
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            expect(tradeEvent).to.not.be.undefined;

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            // Verify trade event parameters
            expect(parsedTradeEvent?.args[0]).to.equal(freshTokenAddress); // token
            expect(parsedTradeEvent?.args[1]).to.equal(seller.address); // user
            expect(parsedTradeEvent?.args[2]).to.be.false; // isBuy (false for sell)
            expect(parsedTradeEvent?.args[3]).to.be.gt(0); // netAmount (ETH received)
            expect(parsedTradeEvent?.args[4]).to.equal(sellAmount); // tokenAmount (tokens sold)
            expect(parsedTradeEvent?.args[9]).to.be.gt(0); // fee

            const grossAmountReceived = parsedTradeEvent?.args[3];
            const fee = parsedTradeEvent?.args[9];

            // Verify seller's token balance decreased
            const finalTokenBalance = await freshToken.balanceOf(seller.address);
            expect(initialTokenBalance - finalTokenBalance).to.equal(sellAmount);

            // Verify seller received ETH (allowing for gas costs and fees)
            const finalEthBalance = await ethers.provider.getBalance(seller.address);
            const actualEthChange = finalEthBalance - initialEthBalance + gasUsed;
            expect(actualEthChange).to.equal(grossAmountReceived - fee);

            // Verify fee was accumulated in pool
            const pool = await launchpad.getPool(freshTokenAddress);
            expect(pool.feeRecipient).to.be.gte(fee);
        });

        it("should burn tokens when selling", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Burn Test Token",
                    "BURN",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens first (use sufficient amount for bonding curve)
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                // Skip this test if pool is already completed
                return;
            }

            const initialTotalSupply = await freshToken.totalSupply();
            const initialTokenBalance = await freshToken.balanceOf(seller.address);
            const sellAmount = initialTokenBalance / 2n;

            if (sellAmount > 0) {
                await launchpad
                    .connect(seller)
                    .sellExactIn(freshTokenAddress, sellAmount, amountOutMin);

                // Verify tokens were burned (total supply decreased)
                const finalTotalSupply = await freshToken.totalSupply();
                expect(initialTotalSupply - finalTotalSupply).to.equal(sellAmount);

                // Verify seller's balance decreased
                const finalTokenBalance = await freshToken.balanceOf(seller.address);
                expect(initialTokenBalance - finalTokenBalance).to.equal(sellAmount);
            }
        });

        it("should update pool state correctly", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Pool State Token",
                    "POOL",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];

            // Buy tokens first
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                // Skip this test if pool is already completed
                return;
            }

            const poolBefore = await launchpad.getPool(freshTokenAddress);
            const sellerBalance = await ethers
                .getContractAt("LaunchpadToken", freshTokenAddress)
                .then((token) => token.balanceOf(seller.address));
            const sellAmount = sellerBalance / 2n;

            if (sellAmount === 0n) {
                // Skip if no tokens to sell
                return;
            }

            await launchpad
                .connect(seller)
                .sellExactIn(freshTokenAddress, sellAmount, amountOutMin);

            const poolAfter = await launchpad.getPool(freshTokenAddress);

            // Virtual token reserves should increase (tokens added to pool)
            expect(poolAfter.virtualTokenReserves).to.be.gt(poolBefore.virtualTokenReserves);

            // Virtual hype reserves should decrease (ETH removed from pool)
            expect(poolAfter.virtualHypeReserves).to.be.lt(poolBefore.virtualHypeReserves);

            // Real token reserves should increase
            expect(poolAfter.realTokenReserves).to.be.gt(poolBefore.realTokenReserves);

            // Real hype reserves should decrease
            expect(poolAfter.realHypeReserves).to.be.lt(poolBefore.realHypeReserves);

            // Fee recipient should increase
            expect(poolAfter.feeRecipient).to.be.gt(poolBefore.feeRecipient);
        });

        it("should handle slippage protection with amountOutMin", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Slippage Test Token",
                    "SLIP",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens first
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                return; // Skip if pool completed
            }

            const sellerBalance = await freshToken.balanceOf(seller.address);
            const sellAmount = sellerBalance / 2n;

            if (sellAmount === 0n) {
                return; // Skip if no tokens to sell
            }

            // First, estimate how much ETH we'll get for selling tokens
            const estimatedEth = await launchpad.estimateSellTokens(freshTokenAddress, sellAmount);

            // Set a reasonable minimum (90% of estimated)
            const reasonableMin = (estimatedEth * 90n) / 100n;

            // This should succeed
            await expect(
                launchpad.connect(seller).sellExactIn(freshTokenAddress, sellAmount, reasonableMin)
            ).to.not.be.reverted;
        });

        it("should revert when amountOut is less than amountOutMin", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Min Amount Test Token",
                    "MIN",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens first
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                return; // Skip if pool completed
            }

            const sellerBalance = await freshToken.balanceOf(seller.address);
            const sellAmount = sellerBalance / 2n;

            if (sellAmount === 0n) {
                return; // Skip if no tokens to sell
            }

            // Set an unreasonably high minimum
            const highAmountOutMin = ethers.parseEther("1000"); // Very high minimum

            await expect(
                launchpad
                    .connect(seller)
                    .sellExactIn(freshTokenAddress, sellAmount, highAmountOutMin)
            ).to.be.revertedWithCustomError(launchpad, "InsufficientAmount");
        });

        it("should revert with non-existent token", async function () {
            const fakeTokenAddress = ethers.Wallet.createRandom().address;

            await expect(
                launchpad.connect(seller).sellExactIn(fakeTokenAddress, tokenAmountIn, amountOutMin)
            ).to.be.revertedWithCustomError(launchpad, "TokenNotExists");
        });

        it("should revert with zero tokenAmountIn", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Zero Amount Test Token",
                    "ZERO",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];

            // Test zero amount validation
            await expect(
                launchpad.connect(seller).sellExactIn(freshTokenAddress, 0, amountOutMin)
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert when seller has insufficient tokens", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Insufficient Test Token",
                    "INSUF",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Don't buy any tokens, so seller has 0 balance
            const sellerBalance = await freshToken.balanceOf(seller.address);
            expect(sellerBalance).to.equal(0);

            // Try to sell tokens when seller has none
            const excessiveAmount = ethers.parseEther("100");

            await expect(
                launchpad
                    .connect(seller)
                    .sellExactIn(freshTokenAddress, excessiveAmount, amountOutMin)
            ).to.be.revertedWithCustomError(freshToken, "ERC20InsufficientBalance");
        });

        it("should handle bonding curve math correctly", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Bonding Curve Test Token",
                    "CURVE",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens first
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                return; // Skip if pool completed
            }

            const sellerBalance = await freshToken.balanceOf(seller.address);
            const sellAmount = sellerBalance / 2n;

            if (sellAmount === 0n) {
                return; // Skip if no tokens to sell
            }

            const poolBefore = await launchpad.getPool(freshTokenAddress);

            const tx = await launchpad
                .connect(seller)
                .sellExactIn(freshTokenAddress, sellAmount, amountOutMin);

            const receipt = await tx.wait();
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            const netAmountReceived = parsedTradeEvent?.args[3];
            const fee = parsedTradeEvent?.args[9];

            // Verify bonding curve calculations
            expect(netAmountReceived).to.be.gt(0);
            expect(fee).to.be.gt(0);

            // Net amount should be less than gross amount (due to fees)
            const grossAmount = netAmountReceived + fee;
            expect(netAmountReceived).to.be.lt(grossAmount);

            // Verify pool state reflects the bonding curve
            const poolAfter = await launchpad.getPool(freshTokenAddress);
            expect(poolAfter.virtualHypeReserves).to.be.lt(poolBefore.virtualHypeReserves);
            expect(poolAfter.virtualTokenReserves).to.be.gt(poolBefore.virtualTokenReserves);
        });

        it("should handle multiple sequential sells correctly", async function () {
            // Create a fresh token and buy more tokens for multiple sells
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Sequential Sell Token",
                    "SEQSELL",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens for both seller and buyer (use sufficient amounts)
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active after first buy
            const poolStateAfterFirst = await launchpad.getPool(freshTokenAddress);
            if (poolStateAfterFirst.isCompleted) {
                return; // Skip if pool completed
            }

            await launchpad
                .connect(buyer)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active after second buy
            const poolStateAfterSecond = await launchpad.getPool(freshTokenAddress);
            if (poolStateAfterSecond.isCompleted) {
                return; // Skip if pool completed
            }

            const sellerBalance = await freshToken.balanceOf(seller.address);
            const buyerBalance = await freshToken.balanceOf(buyer.address);

            const sellAmount = sellerBalance / 2n; // Use half of seller's tokens
            const buyerSellAmount = buyerBalance / 2n; // Use half of buyer's tokens

            if (sellAmount === 0n || buyerSellAmount === 0n) {
                return; // Skip if no tokens to sell
            }

            // First sell
            const tx1 = await launchpad
                .connect(seller)
                .sellExactIn(freshTokenAddress, sellAmount, 0);

            const receipt1 = await tx1.wait();
            const tradeEvent1 = receipt1?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            const parsedTradeEvent1 = launchpad.interface.parseLog({
                topics: tradeEvent1!.topics as string[],
                data: tradeEvent1!.data,
            });

            const firstSellEth = parsedTradeEvent1?.args[3];

            // Second sell by different user
            const tx2 = await launchpad
                .connect(buyer)
                .sellExactIn(freshTokenAddress, buyerSellAmount, 0);

            const receipt2 = await tx2.wait();
            const tradeEvent2 = receipt2?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            const parsedTradeEvent2 = launchpad.interface.parseLog({
                topics: tradeEvent2!.topics as string[],
                data: tradeEvent2!.data,
            });

            const secondSellEth = parsedTradeEvent2?.args[3];

            // Verify both sells worked
            expect(firstSellEth).to.be.gt(0);
            expect(secondSellEth).to.be.gt(0);

            // Due to bonding curve, second sell should get less ETH for same tokens (if amounts are similar)
            // Note: We're not enforcing this strictly since amounts might be different
            expect(secondSellEth).to.be.gt(0);
        });

        it("should emit correct event parameters", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Event Test Token",
                    "EVENT",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens first
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                return; // Skip if pool completed
            }

            const sellerBalance = await freshToken.balanceOf(seller.address);
            const sellAmount = sellerBalance / 2n;

            if (sellAmount === 0n) {
                return; // Skip if no tokens to sell
            }

            const tx = await launchpad
                .connect(seller)
                .sellExactIn(freshTokenAddress, sellAmount, amountOutMin);

            const receipt = await tx.wait();

            // Check Trade event
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            expect(tradeEvent).to.not.be.undefined;

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            // Verify all event parameters
            expect(parsedTradeEvent?.args[0]).to.equal(freshTokenAddress); // token
            expect(parsedTradeEvent?.args[1]).to.equal(seller.address); // user
            expect(parsedTradeEvent?.args[2]).to.be.false; // isBuy (false for sell)
            expect(parsedTradeEvent?.args[3]).to.be.gt(0); // hypeAmount (ETH received)
            expect(parsedTradeEvent?.args[4]).to.equal(sellAmount); // tokenAmount (tokens sold)
            expect(parsedTradeEvent?.args[5]).to.be.gt(0); // virtualHypeReserves
            expect(parsedTradeEvent?.args[6]).to.be.gt(0); // virtualTokenReserves
            expect(parsedTradeEvent?.args[7]).to.be.gt(0); // realHypeReserves
            expect(parsedTradeEvent?.args[8]).to.be.gte(0); // realTokenReserves
            expect(parsedTradeEvent?.args[9]).to.be.gt(0); // fee
            expect(parsedTradeEvent?.args[10]).to.be.gt(0); // timestamp
        });

        it("should revert when pool is completed", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Completed Pool Token",
                    "COMP",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            const largeAmount = ethers.parseEther("100");
            const amountOutMin = ethers.parseEther("0");

            await launchpad
                .connect(buyer)
                .buyExactIn(freshTokenAddress, largeAmount, amountOutMin, { value: largeAmount });

            const pool = await launchpad.getPool(freshTokenAddress);

            console.log("Pool state before completion:", pool);

            // If pool is completed, selling should revert
            const sellerBalance = await freshToken.balanceOf(seller.address);
            if (sellerBalance > 0) {
                await expect(
                    launchpad.connect(seller).sellExactIn(freshTokenAddress, sellerBalance, 0)
                ).to.be.revertedWithCustomError(launchpad, "PoolAlreadyCompleted");
            }
        });

        it("should handle edge case with very small token amounts", async function () {
            const smallAmount = ethers.parseEther("0.001"); // Very small amount
            const sellerBalance = await testToken.balanceOf(seller.address);

            if (sellerBalance >= smallAmount) {
                const tx = await launchpad
                    .connect(seller)
                    .sellExactIn(testTokenAddress, smallAmount, 0);

                const receipt = await tx.wait();

                // Should still emit trade event
                const tradeEvent = receipt?.logs.find((log) => {
                    try {
                        const parsed = launchpad.interface.parseLog({
                            topics: log.topics as string[],
                            data: log.data,
                        });
                        return parsed?.name === "Trade";
                    } catch {
                        return false;
                    }
                });

                expect(tradeEvent).to.not.be.undefined;
            }
        });

        it("should handle real hype reserves limit correctly", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Reserves Test Token",
                    "RESERVES",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens first
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                return; // Skip if pool completed
            }

            // This test verifies that selling is limited by real hype reserves
            const poolBefore = await launchpad.getPool(freshTokenAddress);
            const realHypeReserves = poolBefore.realHypeReserves;

            // Try to sell tokens
            const sellerBalance = await freshToken.balanceOf(seller.address);

            if (sellerBalance > 0) {
                const sellAmount = sellerBalance / 2n;

                const tx = await launchpad
                    .connect(seller)
                    .sellExactIn(freshTokenAddress, sellAmount, 0);

                const receipt = await tx.wait();
                const tradeEvent = receipt?.logs.find((log) => {
                    try {
                        const parsed = launchpad.interface.parseLog({
                            topics: log.topics as string[],
                            data: log.data,
                        });
                        return parsed?.name === "Trade";
                    } catch {
                        return false;
                    }
                });

                const parsedTradeEvent = launchpad.interface.parseLog({
                    topics: tradeEvent!.topics as string[],
                    data: tradeEvent!.data,
                });

                const netAmountReceived = parsedTradeEvent?.args[3];
                const fee = parsedTradeEvent?.args[9];
                const totalHypeOut = netAmountReceived + fee;

                // Total hype out should not exceed the real reserves that were available
                expect(totalHypeOut).to.be.lte(realHypeReserves);
            }
        });
    });

    describe("Integration Tests", function () {
        it("should work correctly with buy-sell cycle", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Cycle Test Token",
                    "CYCLE",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            const initialEthBalance = await ethers.provider.getBalance(seller.address);

            // Step 1: Buy tokens
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            const buyTx = await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            const buyReceipt = await buyTx.wait();
            const buyGasUsed = buyReceipt!.gasUsed * buyReceipt!.gasPrice;

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                return; // Skip if pool completed
            }

            const tokenBalance = await freshToken.balanceOf(seller.address);
            expect(tokenBalance).to.be.gt(0);

            // Step 2: Sell half the tokens
            const sellAmount = tokenBalance / 2n;

            if (sellAmount === 0n) {
                return; // Skip if no tokens to sell
            }

            const sellTx = await launchpad
                .connect(seller)
                .sellExactIn(freshTokenAddress, sellAmount, 0);

            const sellReceipt = await sellTx.wait();
            const sellGasUsed = sellReceipt!.gasUsed * sellReceipt!.gasPrice;

            // Step 3: Verify final state
            const finalTokenBalance = await freshToken.balanceOf(seller.address);
            const finalEthBalance = await ethers.provider.getBalance(seller.address);

            // Should have half the tokens left
            expect(finalTokenBalance).to.equal(tokenBalance - sellAmount);

            // Should have received some ETH back (less than spent due to fees and bonding curve)
            const totalGasUsed = buyGasUsed + sellGasUsed;
            const netEthChange = finalEthBalance - initialEthBalance + totalGasUsed;

            // Net change should be negative (lost money due to fees and bonding curve slippage)
            expect(netEthChange).to.be.lt(0);

            // But should have received some ETH from the sell
            expect(finalEthBalance).to.be.gt(initialEthBalance - buyAmount - totalGasUsed);
        });

        it("should handle fee accumulation and distribution correctly", async function () {
            // Create a fresh token for this test
            const freshTokenTx = await launchpad
                .connect(creator)
                .createPool(
                    "Fee Accumulation Test Token",
                    "FEEACC",
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    { value: POOL_CREATION_FEE }
                );

            const freshTokenReceipt = await freshTokenTx.wait();
            const freshTokenCreatedEvent = freshTokenReceipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const freshTokenParsedEvent = launchpad.interface.parseLog({
                topics: freshTokenCreatedEvent!.topics as string[],
                data: freshTokenCreatedEvent!.data,
            });

            const freshTokenAddress = freshTokenParsedEvent?.args[0];
            const freshToken = await ethers.getContractAt("LaunchpadToken", freshTokenAddress);

            // Buy tokens first
            const buyAmount = ethers.parseEther("0.012"); // Amount to buy
            const sufficientAmount = ethers.parseEther("0.015"); // Sufficient for bonding curve + fees
            await launchpad
                .connect(seller)
                .buyExactIn(freshTokenAddress, buyAmount, 0, { value: sufficientAmount });

            // Check if pool is still active
            const poolState = await launchpad.getPool(freshTokenAddress);
            if (poolState.isCompleted) {
                return; // Skip if pool completed
            }

            const poolBefore = await launchpad.getPool(freshTokenAddress);
            const initialFeeRecipient = poolBefore.feeRecipient;

            // Perform a sell to accumulate fees
            const sellerBalance = await freshToken.balanceOf(seller.address);
            const sellAmount = sellerBalance / 2n;

            if (sellAmount === 0n) {
                return; // Skip if no tokens to sell
            }

            await launchpad.connect(seller).sellExactIn(freshTokenAddress, sellAmount, 0);

            const poolAfter = await launchpad.getPool(freshTokenAddress);
            const finalFeeRecipient = poolAfter.feeRecipient;

            // Fees should have increased
            expect(finalFeeRecipient).to.be.gt(initialFeeRecipient);

            // The increase should be reasonable (positive but not excessive)
            const feeIncrease = finalFeeRecipient - initialFeeRecipient;
            expect(feeIncrease).to.be.gt(0);
            expect(feeIncrease).to.be.lt(ethers.parseEther("1")); // Should be less than 1 ETH
        });
    });
});

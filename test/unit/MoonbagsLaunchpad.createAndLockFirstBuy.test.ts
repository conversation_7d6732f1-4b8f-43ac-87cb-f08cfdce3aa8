import { Signer<PERSON>ithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { expect } from "chai";
import { ethers } from "hardhat";

import {
    INonfungiblePositionManager,
    IWETH9,
    LaunchpadToken,
    MoonbagsLaunchpad,
    MoonbagsStake,
    TokenLock,
} from "../../types";
import { externalFixture } from "./external.fixtures";

describe("MoonbagsLaunchpad - createAndLockFirstBuy", function () {
    let launchpad: MoonbagsLaunchpad;
    let tokenLock: TokenLock;
    let moonbagsStake: MoonbagsStake;
    let weth9: IWETH9;
    let nonfungiblePositionManager: INonfungiblePositionManager;
    let owner: <PERSON>er<PERSON><PERSON><PERSON>dd<PERSON>;
    let creator: <PERSON><PERSON><PERSON><PERSON><PERSON>dd<PERSON>;
    let buyer: Signer<PERSON>ithAddress;
    let treasury: SignerWithAddress;
    let platformToken: LaunchpadToken;

    // Token parameters
    const tokenName = "Test Token";
    const tokenSymbol = "TEST";
    const tokenUri = "https://example.com/token";
    const tokenDescription = "A test token for bonding curve";
    const twitter = "https://twitter.com/test";
    const telegram = "https://t.me/test";
    const website = "https://test.com";
    const customThreshold = ethers.parseEther("0.03");

    // Constants from contract
    const DEFAULT_FEE_TIER = 3000;
    const POOL_CREATION_FEE = ethers.parseEther("0.001");
    const MIN_LOCK_DURATION = 3600; // 1 hour in seconds

    beforeEach(async function () {
        [owner, creator, buyer, treasury] = await ethers.getSigners();

        const { weth9: _weth9, nonfungiblePositionManager: _nonfungiblePositionManager } =
            await externalFixture();
        weth9 = _weth9;
        nonfungiblePositionManager = _nonfungiblePositionManager;

        // Deploy platform token
        const LaunchpadTokenFactory = await ethers.getContractFactory("LaunchpadToken");
        platformToken = await LaunchpadTokenFactory.deploy(
            "Platform Token",
            "PLAT",
            18,
            "https://platform.com"
        );
        await platformToken.waitForDeployment();

        // Deploy MoonbagsStake
        const MoonbagsStakeFactory = await ethers.getContractFactory("MoonbagsStake");
        moonbagsStake = await MoonbagsStakeFactory.deploy();
        await moonbagsStake.waitForDeployment();
        await moonbagsStake.initialize();

        // Deploy TokenLock
        const TokenLockFactory = await ethers.getContractFactory("TokenLock");
        tokenLock = await TokenLockFactory.deploy();
        await tokenLock.waitForDeployment();
        await tokenLock.initialize();

        // Deploy MoonbagsLaunchpad
        const MoonbagsLaunchpadFactory = await ethers.getContractFactory("MoonbagsLaunchpad");
        launchpad = await MoonbagsLaunchpadFactory.deploy();
        await launchpad.waitForDeployment();

        // Initialize the launchpad
        await launchpad.initialize(
            await nonfungiblePositionManager.getAddress(),
            await weth9.getAddress(),
            DEFAULT_FEE_TIER,
            await platformToken.getAddress(),
            await moonbagsStake.getAddress(),
            await tokenLock.getAddress()
        );

        await launchpad.updateFeeRecipients(treasury.address, treasury.address);
    });

    describe("createAndLockFirstBuy", function () {
        const lockDuration = MIN_LOCK_DURATION; // 1 hour
        const amountOut = ethers.parseUnits("8000000", 6); // 50 tokens to buy and lock (6 decimals to match contract)
        const buyAmount = ethers.parseEther("0.2"); // ETH to spend on buying tokens

        it("should create token and lock first buy with valid parameters", async function () {
            const totalValue = POOL_CREATION_FEE + buyAmount;

            const tx = await launchpad
                .connect(creator)
                .createAndLockFirstBuy(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    amountOut,
                    lockDuration,
                    { value: totalValue }
                );

            const receipt = await tx.wait();

            // Check TokenCreated event
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            expect(tokenCreatedEvent).to.not.be.undefined;

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const tokenAddress = parsedEvent?.args[0];
            expect(tokenAddress).to.not.equal(ethers.ZeroAddress);

            // Verify token is registered
            expect(await launchpad.isToken(tokenAddress)).to.be.true;
            console.log("Pool: ", await launchpad.getPool(tokenAddress));
            // Check Trade event was emitted (indicating tokens were bought)
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            expect(tradeEvent).to.not.be.undefined;

            // Verify tokens were locked
            const lockCount = await tokenLock.getLockCount();
            expect(lockCount).to.be.gt(0);

            // Get the lock details
            const lockDetails = await tokenLock.getLock(lockCount);
            expect(lockDetails.token).to.equal(tokenAddress);
            expect(lockDetails.recipient).to.equal(creator.address);
            expect(lockDetails.locker).to.equal(await launchpad.getAddress());
            expect(lockDetails.closed).to.be.false;
            expect(lockDetails.endTime).to.be.gt(lockDetails.startTime);
            expect(lockDetails.endTime - lockDetails.startTime).to.equal(lockDuration);

            // Verify creator doesn't have tokens directly (they're locked)
            const token = await ethers.getContractAt("LaunchpadToken", tokenAddress);
            const creatorBalance = await token.balanceOf(creator.address);
            expect(creatorBalance).to.equal(0);

            // Verify tokens are in the lock contract
            const lockBalance = await token.balanceOf(await tokenLock.getAddress());
            expect(lockBalance).to.be.equal(amountOut);
        });

        it("should create token without buying when amountOut is 0", async function () {
            const tx = await launchpad.connect(creator).createAndLockFirstBuy(
                tokenName,
                tokenSymbol,
                tokenUri,
                tokenDescription,
                twitter,
                telegram,
                website,
                customThreshold,
                0, // No tokens to buy
                lockDuration,
                { value: POOL_CREATION_FEE }
            );

            const receipt = await tx.wait();

            // Check TokenCreated event
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            expect(tokenCreatedEvent).to.not.be.undefined;

            // Check that no Trade event was emitted (no tokens bought)
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });
            expect(tradeEvent).to.be.undefined;

            // Verify no locks were created
            const lockCount = await tokenLock.getLockCount();
            expect(lockCount).to.equal(0);
        });

        it("should create token without buying when amountIn is 0", async function () {
            const tx = await launchpad.connect(creator).createAndLockFirstBuy(
                tokenName,
                tokenSymbol,
                tokenUri,
                tokenDescription,
                twitter,
                telegram,
                website,
                customThreshold,
                amountOut,
                lockDuration,
                { value: POOL_CREATION_FEE } // Only pool creation fee, no extra for buying
            );

            const receipt = await tx.wait();

            // Check TokenCreated event
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            expect(tokenCreatedEvent).to.not.be.undefined;

            // Check that no Trade event was emitted (no tokens bought)
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });
            expect(tradeEvent).to.be.undefined;
        });

        it("should revert with lock duration below minimum", async function () {
            const shortDuration = MIN_LOCK_DURATION - 1;

            await expect(
                launchpad
                    .connect(creator)
                    .createAndLockFirstBuy(
                        tokenName,
                        tokenSymbol,
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        amountOut,
                        shortDuration,
                        { value: POOL_CREATION_FEE + buyAmount }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should revert with insufficient pool creation fee", async function () {
            await expect(
                launchpad
                    .connect(creator)
                    .createAndLockFirstBuy(
                        tokenName,
                        tokenSymbol,
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        amountOut,
                        lockDuration,
                        { value: POOL_CREATION_FEE - 1n }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InvalidInput");
        });

        it("should handle insufficient funds for buying tokens gracefully", async function () {
            const largeAmountOut = ethers.parseUnits("1000000", 6); // Very large amount (6 decimals)
            const smallBuyAmount = ethers.parseEther("0.001"); // Small ETH amount

            await expect(
                launchpad
                    .connect(creator)
                    .createAndLockFirstBuy(
                        tokenName,
                        tokenSymbol,
                        tokenUri,
                        tokenDescription,
                        twitter,
                        telegram,
                        website,
                        customThreshold,
                        largeAmountOut,
                        lockDuration,
                        { value: POOL_CREATION_FEE + smallBuyAmount }
                    )
            ).to.be.revertedWithCustomError(launchpad, "InsufficientAmount");
        });

        it("should correctly calculate and handle bonding curve math", async function () {
            const totalValue = POOL_CREATION_FEE + buyAmount;

            const tx = await launchpad
                .connect(creator)
                .createAndLockFirstBuy(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    amountOut,
                    lockDuration,
                    { value: totalValue }
                );

            const receipt = await tx.wait();

            // Get the token address
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const tokenAddress = parsedEvent?.args[0];

            // Verify pool state after the buy
            const pool = await launchpad.getPool(tokenAddress);

            if (!pool.isCompleted) {
                expect(pool.realHypeReserves).to.be.gt(0); // Should have received ETH
                expect(pool.virtualTokenReserves).to.be.gt(0); // Should have virtual token reserves
                expect(pool.feeRecipient).to.be.gt(0); // Should have accumulated fees
            } else {
                // Pool was completed and migrated, so reserves are reset
                expect(pool.isCompleted).to.be.true;
                expect(pool.feeRecipient).to.be.gt(0); // Should still have accumulated fees
            }
        });

        it("should emit correct events with proper parameters", async function () {
            const totalValue = POOL_CREATION_FEE + buyAmount;

            const tx = await launchpad
                .connect(creator)
                .createAndLockFirstBuy(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    amountOut,
                    lockDuration,
                    { value: totalValue }
                );

            const receipt = await tx.wait();

            // Verify TokenCreated event parameters
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            expect(tokenCreatedEvent).to.not.be.undefined;

            const parsedTokenEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            // Verify event parameters
            expect(parsedTokenEvent?.args[1]).to.equal(creator.address); // creator
            expect(parsedTokenEvent?.args[2]).to.equal(tokenName); // name
            expect(parsedTokenEvent?.args[3]).to.equal(tokenSymbol); // symbol
            expect(parsedTokenEvent?.args[4]).to.equal(tokenUri); // uri
            expect(parsedTokenEvent?.args[5]).to.equal(tokenDescription); // description
            expect(parsedTokenEvent?.args[6]).to.equal(twitter); // twitter
            expect(parsedTokenEvent?.args[7]).to.equal(telegram); // telegram
            expect(parsedTokenEvent?.args[8]).to.equal(website); // website

            // Verify Trade event parameters
            const tradeEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "Trade";
                } catch {
                    return false;
                }
            });

            expect(tradeEvent).to.not.be.undefined;

            const parsedTradeEvent = launchpad.interface.parseLog({
                topics: tradeEvent!.topics as string[],
                data: tradeEvent!.data,
            });

            expect(parsedTradeEvent?.args[1]).to.equal(creator.address); // user
            expect(parsedTradeEvent?.args[2]).to.be.true; // isBuy
            expect(parsedTradeEvent?.args[3]).to.be.gt(0); // hypeAmount
            expect(parsedTradeEvent?.args[4]).to.be.gt(0); // tokenAmount
            expect(parsedTradeEvent?.args[9]).to.be.gt(0); // fee
        });

        it("should handle maximum lock duration correctly", async function () {
            const maxLockDuration = 365 * 24 * 3600; // 1 year
            const totalValue = POOL_CREATION_FEE + buyAmount;

            const tx = await launchpad
                .connect(creator)
                .createAndLockFirstBuy(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    amountOut,
                    maxLockDuration,
                    { value: totalValue }
                );

            await tx.wait();

            // Verify lock was created with correct duration
            const lockCount = await tokenLock.getLockCount();
            const lockDetails = await tokenLock.getLock(lockCount);
            expect(lockDetails.endTime - lockDetails.startTime).to.equal(maxLockDuration);
        });
    });

    describe("Integration Tests", function () {
        const integrationBuyAmount = ethers.parseEther("0.02"); // Define buyAmount for integration tests (reduced to avoid pool completion)
        const integrationAmountOut = ethers.parseUnits("50", 6); // 50 tokens with 6 decimals (matching contract)

        it("should allow withdrawal after lock period expires", async function () {
            const shortLockDuration = MIN_LOCK_DURATION; // 1 hour
            const totalValue = POOL_CREATION_FEE + integrationBuyAmount;

            // Create token and lock first buy
            const tx = await launchpad
                .connect(creator)
                .createAndLockFirstBuy(
                    tokenName,
                    tokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    integrationAmountOut,
                    shortLockDuration,
                    { value: totalValue }
                );

            const receipt = await tx.wait();

            // Get token address
            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics as string[],
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            const parsedEvent = launchpad.interface.parseLog({
                topics: tokenCreatedEvent!.topics as string[],
                data: tokenCreatedEvent!.data,
            });

            const tokenAddress = parsedEvent?.args[0];
            const token = await ethers.getContractAt("LaunchpadToken", tokenAddress);

            // Verify tokens are locked
            const lockCount = await tokenLock.getLockCount();
            const lockDetails = await tokenLock.getLock(lockCount);
            expect(lockDetails.amount).to.be.gt(0);

            // Fast forward time to after lock expiry
            await ethers.provider.send("evm_increaseTime", [shortLockDuration + 1]);
            await ethers.provider.send("evm_mine", []);

            // Verify lock is withdrawable
            const isWithdrawable = await tokenLock.isWithdrawable(lockCount);
            expect(isWithdrawable).to.be.true;

            // Withdraw tokens
            const initialBalance = await token.balanceOf(creator.address);
            await tokenLock.connect(creator).withdraw(lockCount);
            const finalBalance = await token.balanceOf(creator.address);

            // Verify tokens were transferred to creator
            expect(finalBalance - initialBalance).to.equal(lockDetails.amount);

            // Verify lock is now closed
            const updatedLockDetails = await tokenLock.getLock(lockCount);
            expect(updatedLockDetails.closed).to.be.true;
        });

        it("should prevent withdrawal before lock period expires", async function () {
            const longLockDuration = 7 * 24 * 3600; // 1 week
            const totalValue = POOL_CREATION_FEE + integrationBuyAmount;

            // Use unique token name to avoid state interference
            const uniqueTokenName = `${tokenName} Long Lock`;
            const uniqueTokenSymbol = `${tokenSymbol}L`;

            // Create token and lock first buy
            await launchpad
                .connect(creator)
                .createAndLockFirstBuy(
                    uniqueTokenName,
                    uniqueTokenSymbol,
                    tokenUri,
                    tokenDescription,
                    twitter,
                    telegram,
                    website,
                    customThreshold,
                    integrationAmountOut,
                    longLockDuration,
                    { value: totalValue }
                );

            const lockCount = await tokenLock.getLockCount();

            // Verify lock is not withdrawable yet
            const isWithdrawable = await tokenLock.isWithdrawable(lockCount);
            expect(isWithdrawable).to.be.false;

            // Try to withdraw - should fail
            await expect(
                tokenLock.connect(creator).withdraw(lockCount)
            ).to.be.revertedWithCustomError(tokenLock, "Unauthorized");
        });
    });
});

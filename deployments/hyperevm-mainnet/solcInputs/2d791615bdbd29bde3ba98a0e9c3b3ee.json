{"language": "Solidity", "sources": {"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\n\npragma solidity ^0.8.20;\n\nimport {ContextUpgradeable} from \"../utils/ContextUpgradeable.sol\";\nimport {Initializable} from \"../proxy/utils/Initializable.sol\";\n\n/**\n * @dev Contract module which provides a basic access control mechanism, where\n * there is an account (an owner) that can be granted exclusive access to\n * specific functions.\n *\n * The initial owner is set to the address provided by the deployer. This can\n * later be changed with {transferOwnership}.\n *\n * This module is used through inheritance. It will make available the modifier\n * `onlyOwner`, which can be applied to your functions to restrict their use to\n * the owner.\n */\nabstract contract OwnableUpgradeable is Initializable, ContextUpgradeable {\n    /// @custom:storage-location erc7201:openzeppelin.storage.Ownable\n    struct OwnableStorage {\n        address _owner;\n    }\n\n    // keccak256(abi.encode(uint256(keccak256(\"openzeppelin.storage.Ownable\")) - 1)) & ~bytes32(uint256(0xff))\n    bytes32 private constant OwnableStorageLocation = 0x9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300;\n\n    function _getOwnableStorage() private pure returns (OwnableStorage storage $) {\n        assembly {\n            $.slot := OwnableStorageLocation\n        }\n    }\n\n    /**\n     * @dev The caller account is not authorized to perform an operation.\n     */\n    error OwnableUnauthorizedAccount(address account);\n\n    /**\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\n     */\n    error OwnableInvalidOwner(address owner);\n\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\n\n    /**\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\n     */\n    function __Ownable_init(address initialOwner) internal onlyInitializing {\n        __Ownable_init_unchained(initialOwner);\n    }\n\n    function __Ownable_init_unchained(address initialOwner) internal onlyInitializing {\n        if (initialOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(initialOwner);\n    }\n\n    /**\n     * @dev Throws if called by any account other than the owner.\n     */\n    modifier onlyOwner() {\n        _checkOwner();\n        _;\n    }\n\n    /**\n     * @dev Returns the address of the current owner.\n     */\n    function owner() public view virtual returns (address) {\n        OwnableStorage storage $ = _getOwnableStorage();\n        return $._owner;\n    }\n\n    /**\n     * @dev Throws if the sender is not the owner.\n     */\n    function _checkOwner() internal view virtual {\n        if (owner() != _msgSender()) {\n            revert OwnableUnauthorizedAccount(_msgSender());\n        }\n    }\n\n    /**\n     * @dev Leaves the contract without owner. It will not be possible to call\n     * `onlyOwner` functions. Can only be called by the current owner.\n     *\n     * NOTE: Renouncing ownership will leave the contract without an owner,\n     * thereby disabling any functionality that is only available to the owner.\n     */\n    function renounceOwnership() public virtual onlyOwner {\n        _transferOwnership(address(0));\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Can only be called by the current owner.\n     */\n    function transferOwnership(address newOwner) public virtual onlyOwner {\n        if (newOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(newOwner);\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Internal function without access restriction.\n     */\n    function _transferOwnership(address newOwner) internal virtual {\n        OwnableStorage storage $ = _getOwnableStorage();\n        address oldOwner = $._owner;\n        $._owner = newOwner;\n        emit OwnershipTransferred(oldOwner, newOwner);\n    }\n}\n"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (proxy/utils/Initializable.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\n * behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\n * external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\n * function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\n *\n * The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\n * reused. This mechanism prevents re-execution of each \"step\" but allows the creation of new initialization steps in\n * case an upgrade adds a module that needs to be initialized.\n *\n * For example:\n *\n * [.hljs-theme-light.nopadding]\n * ```solidity\n * contract MyToken is ERC20Upgradeable {\n *     function initialize() initializer public {\n *         __ERC20_init(\"MyToken\", \"MTK\");\n *     }\n * }\n *\n * contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\n *     function initializeV2() reinitializer(2) public {\n *         __ERC20Permit_init(\"MyToken\");\n *     }\n * }\n * ```\n *\n * TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\n * possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\n *\n * CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\n * that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\n *\n * [CAUTION]\n * ====\n * Avoid leaving a contract uninitialized.\n *\n * An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\n * contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\n * the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\n *\n * [.hljs-theme-light.nopadding]\n * ```\n * /// @custom:oz-upgrades-unsafe-allow constructor\n * constructor() {\n *     _disableInitializers();\n * }\n * ```\n * ====\n */\nabstract contract Initializable {\n    /**\n     * @dev Storage of the initializable contract.\n     *\n     * It's implemented on a custom ERC-7201 namespace to reduce the risk of storage collisions\n     * when using with upgradeable contracts.\n     *\n     * @custom:storage-location erc7201:openzeppelin.storage.Initializable\n     */\n    struct InitializableStorage {\n        /**\n         * @dev Indicates that the contract has been initialized.\n         */\n        uint64 _initialized;\n        /**\n         * @dev Indicates that the contract is in the process of being initialized.\n         */\n        bool _initializing;\n    }\n\n    // keccak256(abi.encode(uint256(keccak256(\"openzeppelin.storage.Initializable\")) - 1)) & ~bytes32(uint256(0xff))\n    bytes32 private constant INITIALIZABLE_STORAGE = 0xf0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00;\n\n    /**\n     * @dev The contract is already initialized.\n     */\n    error InvalidInitialization();\n\n    /**\n     * @dev The contract is not initializing.\n     */\n    error NotInitializing();\n\n    /**\n     * @dev Triggered when the contract has been initialized or reinitialized.\n     */\n    event Initialized(uint64 version);\n\n    /**\n     * @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\n     * `onlyInitializing` functions can be used to initialize parent contracts.\n     *\n     * Similar to `reinitializer(1)`, except that in the context of a constructor an `initializer` may be invoked any\n     * number of times. This behavior in the constructor can be useful during testing and is not expected to be used in\n     * production.\n     *\n     * Emits an {Initialized} event.\n     */\n    modifier initializer() {\n        // solhint-disable-next-line var-name-mixedcase\n        InitializableStorage storage $ = _getInitializableStorage();\n\n        // Cache values to avoid duplicated sloads\n        bool isTopLevelCall = !$._initializing;\n        uint64 initialized = $._initialized;\n\n        // Allowed calls:\n        // - initialSetup: the contract is not in the initializing state and no previous version was\n        //                 initialized\n        // - construction: the contract is initialized at version 1 (no reinitialization) and the\n        //                 current contract is just being deployed\n        bool initialSetup = initialized == 0 && isTopLevelCall;\n        bool construction = initialized == 1 && address(this).code.length == 0;\n\n        if (!initialSetup && !construction) {\n            revert InvalidInitialization();\n        }\n        $._initialized = 1;\n        if (isTopLevelCall) {\n            $._initializing = true;\n        }\n        _;\n        if (isTopLevelCall) {\n            $._initializing = false;\n            emit Initialized(1);\n        }\n    }\n\n    /**\n     * @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\n     * contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\n     * used to initialize parent contracts.\n     *\n     * A reinitializer may be used after the original initialization step. This is essential to configure modules that\n     * are added through upgrades and that require initialization.\n     *\n     * When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\n     * cannot be nested. If one is invoked in the context of another, execution will revert.\n     *\n     * Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\n     * a contract, executing them in the right order is up to the developer or operator.\n     *\n     * WARNING: Setting the version to 2**64 - 1 will prevent any future reinitialization.\n     *\n     * Emits an {Initialized} event.\n     */\n    modifier reinitializer(uint64 version) {\n        // solhint-disable-next-line var-name-mixedcase\n        InitializableStorage storage $ = _getInitializableStorage();\n\n        if ($._initializing || $._initialized >= version) {\n            revert InvalidInitialization();\n        }\n        $._initialized = version;\n        $._initializing = true;\n        _;\n        $._initializing = false;\n        emit Initialized(version);\n    }\n\n    /**\n     * @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\n     * {initializer} and {reinitializer} modifiers, directly or indirectly.\n     */\n    modifier onlyInitializing() {\n        _checkInitializing();\n        _;\n    }\n\n    /**\n     * @dev Reverts if the contract is not in an initializing state. See {onlyInitializing}.\n     */\n    function _checkInitializing() internal view virtual {\n        if (!_isInitializing()) {\n            revert NotInitializing();\n        }\n    }\n\n    /**\n     * @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\n     * Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\n     * to any version. It is recommended to use this to lock implementation contracts that are designed to be called\n     * through proxies.\n     *\n     * Emits an {Initialized} event the first time it is successfully executed.\n     */\n    function _disableInitializers() internal virtual {\n        // solhint-disable-next-line var-name-mixedcase\n        InitializableStorage storage $ = _getInitializableStorage();\n\n        if ($._initializing) {\n            revert InvalidInitialization();\n        }\n        if ($._initialized != type(uint64).max) {\n            $._initialized = type(uint64).max;\n            emit Initialized(type(uint64).max);\n        }\n    }\n\n    /**\n     * @dev Returns the highest version that has been initialized. See {reinitializer}.\n     */\n    function _getInitializedVersion() internal view returns (uint64) {\n        return _getInitializableStorage()._initialized;\n    }\n\n    /**\n     * @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}.\n     */\n    function _isInitializing() internal view returns (bool) {\n        return _getInitializableStorage()._initializing;\n    }\n\n    /**\n     * @dev Pointer to storage slot. Allows integrators to override it with a custom storage location.\n     *\n     * NOTE: Consider following the ERC-7201 formula to derive storage locations.\n     */\n    function _initializableStorageSlot() internal pure virtual returns (bytes32) {\n        return INITIALIZABLE_STORAGE;\n    }\n\n    /**\n     * @dev Returns a pointer to the storage namespace.\n     */\n    // solhint-disable-next-line var-name-mixedcase\n    function _getInitializableStorage() private pure returns (InitializableStorage storage $) {\n        bytes32 slot = _initializableStorageSlot();\n        assembly {\n            $.slot := slot\n        }\n    }\n}\n"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\n\npragma solidity ^0.8.20;\nimport {Initializable} from \"../proxy/utils/Initializable.sol\";\n\n/**\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\nabstract contract ContextUpgradeable is Initializable {\n    function __Context_init() internal onlyInitializing {\n    }\n\n    function __Context_init_unchained() internal onlyInitializing {\n    }\n    function _msgSender() internal view virtual returns (address) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view virtual returns (bytes calldata) {\n        return msg.data;\n    }\n\n    function _contextSuffixLength() internal view virtual returns (uint256) {\n        return 0;\n    }\n}\n"}, "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)\n\npragma solidity ^0.8.20;\nimport {Initializable} from \"../proxy/utils/Initializable.sol\";\n\n/**\n * @dev Contract module that helps prevent reentrant calls to a function.\n *\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n * available, which can be applied to functions to make sure there are no nested\n * (reentrant) calls to them.\n *\n * Note that because there is a single `nonReentrant` guard, functions marked as\n * `nonReentrant` may not call one another. This can be worked around by making\n * those functions `private`, and then adding `external` `nonReentrant` entry\n * points to them.\n *\n * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n * consider using {ReentrancyGuardTransient} instead.\n *\n * TIP: If you would like to learn more about reentrancy and alternative ways\n * to protect against it, check out our blog post\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\n */\nabstract contract ReentrancyGuardUpgradeable is Initializable {\n    // Booleans are more expensive than uint256 or any type that takes up a full\n    // word because each write operation emits an extra SLOAD to first read the\n    // slot's contents, replace the bits taken up by the boolean, and then write\n    // back. This is the compiler's defense against contract upgrades and\n    // pointer aliasing, and it cannot be disabled.\n\n    // The values being non-zero value makes deployment a bit more expensive,\n    // but in exchange the refund on every call to nonReentrant will be lower in\n    // amount. Since refunds are capped to a percentage of the total\n    // transaction's gas, it is best to keep them low in cases like this one, to\n    // increase the likelihood of the full refund coming into effect.\n    uint256 private constant NOT_ENTERED = 1;\n    uint256 private constant ENTERED = 2;\n\n    /// @custom:storage-location erc7201:openzeppelin.storage.ReentrancyGuard\n    struct ReentrancyGuardStorage {\n        uint256 _status;\n    }\n\n    // keccak256(abi.encode(uint256(keccak256(\"openzeppelin.storage.ReentrancyGuard\")) - 1)) & ~bytes32(uint256(0xff))\n    bytes32 private constant ReentrancyGuardStorageLocation = 0x9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00;\n\n    function _getReentrancyGuardStorage() private pure returns (ReentrancyGuardStorage storage $) {\n        assembly {\n            $.slot := ReentrancyGuardStorageLocation\n        }\n    }\n\n    /**\n     * @dev Unauthorized reentrant call.\n     */\n    error ReentrancyGuardReentrantCall();\n\n    function __ReentrancyGuard_init() internal onlyInitializing {\n        __ReentrancyGuard_init_unchained();\n    }\n\n    function __ReentrancyGuard_init_unchained() internal onlyInitializing {\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\n        $._status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Prevents a contract from calling itself, directly or indirectly.\n     * Calling a `nonReentrant` function from another `nonReentrant`\n     * function is not supported. It is possible to prevent this from happening\n     * by making the `nonReentrant` function external, and making it call a\n     * `private` function that does the actual work.\n     */\n    modifier nonReentrant() {\n        _nonReentrantBefore();\n        _;\n        _nonReentrantAfter();\n    }\n\n    function _nonReentrantBefore() private {\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\n        // On the first call to nonReentrant, _status will be NOT_ENTERED\n        if ($._status == ENTERED) {\n            revert ReentrancyGuardReentrantCall();\n        }\n\n        // Any calls to nonReentrant after this point will fail\n        $._status = ENTERED;\n    }\n\n    function _nonReentrantAfter() private {\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\n        // By storing the original value once again, a refund is triggered (see\n        // https://eips.ethereum.org/EIPS/eip-2200)\n        $._status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n     * `nonReentrant` function in the call stack.\n     */\n    function _reentrancyGuardEntered() internal view returns (bool) {\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\n        return $._status == ENTERED;\n    }\n}\n"}, "@openzeppelin/contracts/access/Ownable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\n\npragma solidity ^0.8.20;\n\nimport {Context} from \"../utils/Context.sol\";\n\n/**\n * @dev Contract module which provides a basic access control mechanism, where\n * there is an account (an owner) that can be granted exclusive access to\n * specific functions.\n *\n * The initial owner is set to the address provided by the deployer. This can\n * later be changed with {transferOwnership}.\n *\n * This module is used through inheritance. It will make available the modifier\n * `onlyOwner`, which can be applied to your functions to restrict their use to\n * the owner.\n */\nabstract contract Ownable is Context {\n    address private _owner;\n\n    /**\n     * @dev The caller account is not authorized to perform an operation.\n     */\n    error OwnableUnauthorizedAccount(address account);\n\n    /**\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\n     */\n    error OwnableInvalidOwner(address owner);\n\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\n\n    /**\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\n     */\n    constructor(address initialOwner) {\n        if (initialOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(initialOwner);\n    }\n\n    /**\n     * @dev Throws if called by any account other than the owner.\n     */\n    modifier onlyOwner() {\n        _checkOwner();\n        _;\n    }\n\n    /**\n     * @dev Returns the address of the current owner.\n     */\n    function owner() public view virtual returns (address) {\n        return _owner;\n    }\n\n    /**\n     * @dev Throws if the sender is not the owner.\n     */\n    function _checkOwner() internal view virtual {\n        if (owner() != _msgSender()) {\n            revert OwnableUnauthorizedAccount(_msgSender());\n        }\n    }\n\n    /**\n     * @dev Leaves the contract without owner. It will not be possible to call\n     * `onlyOwner` functions. Can only be called by the current owner.\n     *\n     * NOTE: Renouncing ownership will leave the contract without an owner,\n     * thereby disabling any functionality that is only available to the owner.\n     */\n    function renounceOwnership() public virtual onlyOwner {\n        _transferOwnership(address(0));\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Can only be called by the current owner.\n     */\n    function transferOwnership(address newOwner) public virtual onlyOwner {\n        if (newOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(newOwner);\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Internal function without access restriction.\n     */\n    function _transferOwnership(address newOwner) internal virtual {\n        address oldOwner = _owner;\n        _owner = newOwner;\n        emit OwnershipTransferred(oldOwner, newOwner);\n    }\n}\n"}, "@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (interfaces/draft-IERC6093.sol)\npragma solidity ^0.8.20;\n\n/**\n * @dev Standard ERC-20 Errors\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-20 tokens.\n */\ninterface IERC20Errors {\n    /**\n     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     * @param balance Current balance for the interacting account.\n     * @param needed Minimum amount required to perform a transfer.\n     */\n    error ERC20InsufficientBalance(address sender, uint256 balance, uint256 needed);\n\n    /**\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     */\n    error ERC20InvalidSender(address sender);\n\n    /**\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\n     * @param receiver Address to which tokens are being transferred.\n     */\n    error ERC20Invalid<PERSON><PERSON>eiver(address receiver);\n\n    /**\n     * @dev Indicates a failure with the `spender`’s `allowance`. Used in transfers.\n     * @param spender Address that may be allowed to operate on tokens without being their owner.\n     * @param allowance Amount of tokens a `spender` is allowed to operate with.\n     * @param needed Minimum amount required to perform a transfer.\n     */\n    error ERC20InsufficientAllowance(address spender, uint256 allowance, uint256 needed);\n\n    /**\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n     * @param approver Address initiating an approval operation.\n     */\n    error ERC20InvalidApprover(address approver);\n\n    /**\n     * @dev Indicates a failure with the `spender` to be approved. Used in approvals.\n     * @param spender Address that may be allowed to operate on tokens without being their owner.\n     */\n    error ERC20InvalidSpender(address spender);\n}\n\n/**\n * @dev Standard ERC-721 Errors\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-721 tokens.\n */\ninterface IERC721Errors {\n    /**\n     * @dev Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in ERC-20.\n     * Used in balance queries.\n     * @param owner Address of the current owner of a token.\n     */\n    error ERC721InvalidOwner(address owner);\n\n    /**\n     * @dev Indicates a `tokenId` whose `owner` is the zero address.\n     * @param tokenId Identifier number of a token.\n     */\n    error ERC721NonexistentToken(uint256 tokenId);\n\n    /**\n     * @dev Indicates an error related to the ownership over a particular token. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     * @param tokenId Identifier number of a token.\n     * @param owner Address of the current owner of a token.\n     */\n    error ERC721IncorrectOwner(address sender, uint256 tokenId, address owner);\n\n    /**\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     */\n    error ERC721InvalidSender(address sender);\n\n    /**\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\n     * @param receiver Address to which tokens are being transferred.\n     */\n    error ERC721InvalidReceiver(address receiver);\n\n    /**\n     * @dev Indicates a failure with the `operator`’s approval. Used in transfers.\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\n     * @param tokenId Identifier number of a token.\n     */\n    error ERC721InsufficientApproval(address operator, uint256 tokenId);\n\n    /**\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n     * @param approver Address initiating an approval operation.\n     */\n    error ERC721InvalidApprover(address approver);\n\n    /**\n     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\n     */\n    error ERC721InvalidOperator(address operator);\n}\n\n/**\n * @dev Standard ERC-1155 Errors\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-1155 tokens.\n */\ninterface IERC1155Errors {\n    /**\n     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     * @param balance Current balance for the interacting account.\n     * @param needed Minimum amount required to perform a transfer.\n     * @param tokenId Identifier number of a token.\n     */\n    error ERC1155InsufficientBalance(address sender, uint256 balance, uint256 needed, uint256 tokenId);\n\n    /**\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\n     * @param sender Address whose tokens are being transferred.\n     */\n    error ERC1155InvalidSender(address sender);\n\n    /**\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\n     * @param receiver Address to which tokens are being transferred.\n     */\n    error ERC1155InvalidReceiver(address receiver);\n\n    /**\n     * @dev Indicates a failure with the `operator`’s approval. Used in transfers.\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\n     * @param owner Address of the current owner of a token.\n     */\n    error ERC1155MissingApprovalForAll(address operator, address owner);\n\n    /**\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\n     * @param approver Address initiating an approval operation.\n     */\n    error ERC1155InvalidApprover(address approver);\n\n    /**\n     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\n     */\n    error ERC1155InvalidOperator(address operator);\n\n    /**\n     * @dev Indicates an array length mismatch between ids and values in a safeBatchTransferFrom operation.\n     * Used in batch transfers.\n     * @param idsLength Length of the array of token identifiers\n     * @param valuesLength Length of the array of token amounts\n     */\n    error ERC1155InvalidArrayLength(uint256 idsLength, uint256 valuesLength);\n}\n"}, "@openzeppelin/contracts/interfaces/IERC1363.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (interfaces/IERC1363.sol)\n\npragma solidity ^0.8.20;\n\nimport {IERC20} from \"./IERC20.sol\";\nimport {IERC165} from \"./IERC165.sol\";\n\n/**\n * @title IERC1363\n * @dev Interface of the ERC-1363 standard as defined in the https://eips.ethereum.org/EIPS/eip-1363[ERC-1363].\n *\n * Defines an extension interface for ERC-20 tokens that supports executing code on a recipient contract\n * after `transfer` or `transferFrom`, or code on a spender contract after `approve`, in a single transaction.\n */\ninterface IERC1363 is IERC20, IERC165 {\n    /*\n     * Note: the ERC-165 identifier for this interface is 0xb0202a11.\n     * 0xb0202a11 ===\n     *   bytes4(keccak256('transferAndCall(address,uint256)')) ^\n     *   bytes4(keccak256('transferAndCall(address,uint256,bytes)')) ^\n     *   bytes4(keccak256('transferFromAndCall(address,address,uint256)')) ^\n     *   bytes4(keccak256('transferFromAndCall(address,address,uint256,bytes)')) ^\n     *   bytes4(keccak256('approveAndCall(address,uint256)')) ^\n     *   bytes4(keccak256('approveAndCall(address,uint256,bytes)'))\n     */\n\n    /**\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\n     * @param to The address which you want to transfer to.\n     * @param value The amount of tokens to be transferred.\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\n     */\n    function transferAndCall(address to, uint256 value) external returns (bool);\n\n    /**\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\n     * @param to The address which you want to transfer to.\n     * @param value The amount of tokens to be transferred.\n     * @param data Additional data with no specified format, sent in call to `to`.\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\n     */\n    function transferAndCall(address to, uint256 value, bytes calldata data) external returns (bool);\n\n    /**\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\n     * @param from The address which you want to send tokens from.\n     * @param to The address which you want to transfer to.\n     * @param value The amount of tokens to be transferred.\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\n     */\n    function transferFromAndCall(address from, address to, uint256 value) external returns (bool);\n\n    /**\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\n     * @param from The address which you want to send tokens from.\n     * @param to The address which you want to transfer to.\n     * @param value The amount of tokens to be transferred.\n     * @param data Additional data with no specified format, sent in call to `to`.\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\n     */\n    function transferFromAndCall(address from, address to, uint256 value, bytes calldata data) external returns (bool);\n\n    /**\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n     * caller's tokens and then calls {IERC1363Spender-onApprovalReceived} on `spender`.\n     * @param spender The address which will spend the funds.\n     * @param value The amount of tokens to be spent.\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\n     */\n    function approveAndCall(address spender, uint256 value) external returns (bool);\n\n    /**\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n     * caller's tokens and then calls {IERC1363Spender-onApprovalReceived} on `spender`.\n     * @param spender The address which will spend the funds.\n     * @param value The amount of tokens to be spent.\n     * @param data Additional data with no specified format, sent in call to `spender`.\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\n     */\n    function approveAndCall(address spender, uint256 value, bytes calldata data) external returns (bool);\n}\n"}, "@openzeppelin/contracts/interfaces/IERC165.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC165.sol)\n\npragma solidity ^0.8.20;\n\nimport {IERC165} from \"../utils/introspection/IERC165.sol\";\n"}, "@openzeppelin/contracts/interfaces/IERC20.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC20.sol)\n\npragma solidity ^0.8.20;\n\nimport {IERC20} from \"../token/ERC20/IERC20.sol\";\n"}, "@openzeppelin/contracts/token/ERC20/ERC20.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (token/ERC20/ERC20.sol)\n\npragma solidity ^0.8.20;\n\nimport {IERC20} from \"./IERC20.sol\";\nimport {IERC20Metadata} from \"./extensions/IERC20Metadata.sol\";\nimport {Context} from \"../../utils/Context.sol\";\nimport {IERC20Errors} from \"../../interfaces/draft-IERC6093.sol\";\n\n/**\n * @dev Implementation of the {IERC20} interface.\n *\n * This implementation is agnostic to the way tokens are created. This means\n * that a supply mechanism has to be added in a derived contract using {_mint}.\n *\n * TIP: For a detailed writeup see our guide\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\n * to implement supply mechanisms].\n *\n * The default value of {decimals} is 18. To change this, you should override\n * this function so it returns a different value.\n *\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\n * instead returning `false` on failure. This behavior is nonetheless\n * conventional and does not conflict with the expectations of ERC-20\n * applications.\n */\nabstract contract ERC20 is Context, IERC20, IERC20Metadata, IERC20Errors {\n    mapping(address account => uint256) private _balances;\n\n    mapping(address account => mapping(address spender => uint256)) private _allowances;\n\n    uint256 private _totalSupply;\n\n    string private _name;\n    string private _symbol;\n\n    /**\n     * @dev Sets the values for {name} and {symbol}.\n     *\n     * Both values are immutable: they can only be set once during construction.\n     */\n    constructor(string memory name_, string memory symbol_) {\n        _name = name_;\n        _symbol = symbol_;\n    }\n\n    /**\n     * @dev Returns the name of the token.\n     */\n    function name() public view virtual returns (string memory) {\n        return _name;\n    }\n\n    /**\n     * @dev Returns the symbol of the token, usually a shorter version of the\n     * name.\n     */\n    function symbol() public view virtual returns (string memory) {\n        return _symbol;\n    }\n\n    /**\n     * @dev Returns the number of decimals used to get its user representation.\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\n     * be displayed to a user as `5.05` (`505 / 10 ** 2`).\n     *\n     * Tokens usually opt for a value of 18, imitating the relationship between\n     * Ether and Wei. This is the default value returned by this function, unless\n     * it's overridden.\n     *\n     * NOTE: This information is only used for _display_ purposes: it in\n     * no way affects any of the arithmetic of the contract, including\n     * {IERC20-balanceOf} and {IERC20-transfer}.\n     */\n    function decimals() public view virtual returns (uint8) {\n        return 18;\n    }\n\n    /**\n     * @dev See {IERC20-totalSupply}.\n     */\n    function totalSupply() public view virtual returns (uint256) {\n        return _totalSupply;\n    }\n\n    /**\n     * @dev See {IERC20-balanceOf}.\n     */\n    function balanceOf(address account) public view virtual returns (uint256) {\n        return _balances[account];\n    }\n\n    /**\n     * @dev See {IERC20-transfer}.\n     *\n     * Requirements:\n     *\n     * - `to` cannot be the zero address.\n     * - the caller must have a balance of at least `value`.\n     */\n    function transfer(address to, uint256 value) public virtual returns (bool) {\n        address owner = _msgSender();\n        _transfer(owner, to, value);\n        return true;\n    }\n\n    /**\n     * @dev See {IERC20-allowance}.\n     */\n    function allowance(address owner, address spender) public view virtual returns (uint256) {\n        return _allowances[owner][spender];\n    }\n\n    /**\n     * @dev See {IERC20-approve}.\n     *\n     * NOTE: If `value` is the maximum `uint256`, the allowance is not updated on\n     * `transferFrom`. This is semantically equivalent to an infinite approval.\n     *\n     * Requirements:\n     *\n     * - `spender` cannot be the zero address.\n     */\n    function approve(address spender, uint256 value) public virtual returns (bool) {\n        address owner = _msgSender();\n        _approve(owner, spender, value);\n        return true;\n    }\n\n    /**\n     * @dev See {IERC20-transferFrom}.\n     *\n     * Skips emitting an {Approval} event indicating an allowance update. This is not\n     * required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve].\n     *\n     * NOTE: Does not update the allowance if the current allowance\n     * is the maximum `uint256`.\n     *\n     * Requirements:\n     *\n     * - `from` and `to` cannot be the zero address.\n     * - `from` must have a balance of at least `value`.\n     * - the caller must have allowance for ``from``'s tokens of at least\n     * `value`.\n     */\n    function transferFrom(address from, address to, uint256 value) public virtual returns (bool) {\n        address spender = _msgSender();\n        _spendAllowance(from, spender, value);\n        _transfer(from, to, value);\n        return true;\n    }\n\n    /**\n     * @dev Moves a `value` amount of tokens from `from` to `to`.\n     *\n     * This internal function is equivalent to {transfer}, and can be used to\n     * e.g. implement automatic token fees, slashing mechanisms, etc.\n     *\n     * Emits a {Transfer} event.\n     *\n     * NOTE: This function is not virtual, {_update} should be overridden instead.\n     */\n    function _transfer(address from, address to, uint256 value) internal {\n        if (from == address(0)) {\n            revert ERC20InvalidSender(address(0));\n        }\n        if (to == address(0)) {\n            revert ERC20InvalidReceiver(address(0));\n        }\n        _update(from, to, value);\n    }\n\n    /**\n     * @dev Transfers a `value` amount of tokens from `from` to `to`, or alternatively mints (or burns) if `from`\n     * (or `to`) is the zero address. All customizations to transfers, mints, and burns should be done by overriding\n     * this function.\n     *\n     * Emits a {Transfer} event.\n     */\n    function _update(address from, address to, uint256 value) internal virtual {\n        if (from == address(0)) {\n            // Overflow check required: The rest of the code assumes that totalSupply never overflows\n            _totalSupply += value;\n        } else {\n            uint256 fromBalance = _balances[from];\n            if (fromBalance < value) {\n                revert ERC20InsufficientBalance(from, fromBalance, value);\n            }\n            unchecked {\n                // Overflow not possible: value <= fromBalance <= totalSupply.\n                _balances[from] = fromBalance - value;\n            }\n        }\n\n        if (to == address(0)) {\n            unchecked {\n                // Overflow not possible: value <= totalSupply or value <= fromBalance <= totalSupply.\n                _totalSupply -= value;\n            }\n        } else {\n            unchecked {\n                // Overflow not possible: balance + value is at most totalSupply, which we know fits into a uint256.\n                _balances[to] += value;\n            }\n        }\n\n        emit Transfer(from, to, value);\n    }\n\n    /**\n     * @dev Creates a `value` amount of tokens and assigns them to `account`, by transferring it from address(0).\n     * Relies on the `_update` mechanism\n     *\n     * Emits a {Transfer} event with `from` set to the zero address.\n     *\n     * NOTE: This function is not virtual, {_update} should be overridden instead.\n     */\n    function _mint(address account, uint256 value) internal {\n        if (account == address(0)) {\n            revert ERC20InvalidReceiver(address(0));\n        }\n        _update(address(0), account, value);\n    }\n\n    /**\n     * @dev Destroys a `value` amount of tokens from `account`, lowering the total supply.\n     * Relies on the `_update` mechanism.\n     *\n     * Emits a {Transfer} event with `to` set to the zero address.\n     *\n     * NOTE: This function is not virtual, {_update} should be overridden instead\n     */\n    function _burn(address account, uint256 value) internal {\n        if (account == address(0)) {\n            revert ERC20InvalidSender(address(0));\n        }\n        _update(account, address(0), value);\n    }\n\n    /**\n     * @dev Sets `value` as the allowance of `spender` over the `owner`'s tokens.\n     *\n     * This internal function is equivalent to `approve`, and can be used to\n     * e.g. set automatic allowances for certain subsystems, etc.\n     *\n     * Emits an {Approval} event.\n     *\n     * Requirements:\n     *\n     * - `owner` cannot be the zero address.\n     * - `spender` cannot be the zero address.\n     *\n     * Overrides to this logic should be done to the variant with an additional `bool emitEvent` argument.\n     */\n    function _approve(address owner, address spender, uint256 value) internal {\n        _approve(owner, spender, value, true);\n    }\n\n    /**\n     * @dev Variant of {_approve} with an optional flag to enable or disable the {Approval} event.\n     *\n     * By default (when calling {_approve}) the flag is set to true. On the other hand, approval changes made by\n     * `_spendAllowance` during the `transferFrom` operation set the flag to false. This saves gas by not emitting any\n     * `Approval` event during `transferFrom` operations.\n     *\n     * Anyone who wishes to continue emitting `Approval` events on the`transferFrom` operation can force the flag to\n     * true using the following override:\n     *\n     * ```solidity\n     * function _approve(address owner, address spender, uint256 value, bool) internal virtual override {\n     *     super._approve(owner, spender, value, true);\n     * }\n     * ```\n     *\n     * Requirements are the same as {_approve}.\n     */\n    function _approve(address owner, address spender, uint256 value, bool emitEvent) internal virtual {\n        if (owner == address(0)) {\n            revert ERC20InvalidApprover(address(0));\n        }\n        if (spender == address(0)) {\n            revert ERC20InvalidSpender(address(0));\n        }\n        _allowances[owner][spender] = value;\n        if (emitEvent) {\n            emit Approval(owner, spender, value);\n        }\n    }\n\n    /**\n     * @dev Updates `owner`'s allowance for `spender` based on spent `value`.\n     *\n     * Does not update the allowance value in case of infinite allowance.\n     * Revert if not enough allowance is available.\n     *\n     * Does not emit an {Approval} event.\n     */\n    function _spendAllowance(address owner, address spender, uint256 value) internal virtual {\n        uint256 currentAllowance = allowance(owner, spender);\n        if (currentAllowance < type(uint256).max) {\n            if (currentAllowance < value) {\n                revert ERC20InsufficientAllowance(spender, currentAllowance, value);\n            }\n            unchecked {\n                _approve(owner, spender, currentAllowance - value, false);\n            }\n        }\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/extensions/IERC20Metadata.sol)\n\npragma solidity ^0.8.20;\n\nimport {IERC20} from \"../IERC20.sol\";\n\n/**\n * @dev Interface for the optional metadata functions from the ERC-20 standard.\n */\ninterface IERC20Metadata is IERC20 {\n    /**\n     * @dev Returns the name of the token.\n     */\n    function name() external view returns (string memory);\n\n    /**\n     * @dev Returns the symbol of the token.\n     */\n    function symbol() external view returns (string memory);\n\n    /**\n     * @dev Returns the decimals places of the token.\n     */\n    function decimals() external view returns (uint8);\n}\n"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/IERC20.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Interface of the ERC-20 standard as defined in the ERC.\n */\ninterface IERC20 {\n    /**\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\n     * another (`to`).\n     *\n     * Note that `value` may be zero.\n     */\n    event Transfer(address indexed from, address indexed to, uint256 value);\n\n    /**\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\n     * a call to {approve}. `value` is the new allowance.\n     */\n    event Approval(address indexed owner, address indexed spender, uint256 value);\n\n    /**\n     * @dev Returns the value of tokens in existence.\n     */\n    function totalSupply() external view returns (uint256);\n\n    /**\n     * @dev Returns the value of tokens owned by `account`.\n     */\n    function balanceOf(address account) external view returns (uint256);\n\n    /**\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transfer(address to, uint256 value) external returns (bool);\n\n    /**\n     * @dev Returns the remaining number of tokens that `spender` will be\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\n     * zero by default.\n     *\n     * This value changes when {approve} or {transferFrom} are called.\n     */\n    function allowance(address owner, address spender) external view returns (uint256);\n\n    /**\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n     * caller's tokens.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\n     * that someone may use both the old and the new allowance by unfortunate\n     * transaction ordering. One possible solution to mitigate this race\n     * condition is to first reduce the spender's allowance to 0 and set the\n     * desired value afterwards:\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\n     *\n     * Emits an {Approval} event.\n     */\n    function approve(address spender, uint256 value) external returns (bool);\n\n    /**\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\n     * allowance mechanism. `value` is then deducted from the caller's\n     * allowance.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\n}\n"}, "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (token/ERC20/utils/SafeERC20.sol)\n\npragma solidity ^0.8.20;\n\nimport {IERC20} from \"../IERC20.sol\";\nimport {IERC1363} from \"../../../interfaces/IERC1363.sol\";\n\n/**\n * @title SafeERC20\n * @dev Wrappers around ERC-20 operations that throw on failure (when the token\n * contract returns false). Tokens that return no value (and instead revert or\n * throw on failure) are also supported, non-reverting calls are assumed to be\n * successful.\n * To use this library you can add a `using SafeERC20 for IERC20;` statement to your contract,\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\n */\nlibrary SafeERC20 {\n    /**\n     * @dev An operation with an ERC-20 token failed.\n     */\n    error SafeERC20FailedOperation(address token);\n\n    /**\n     * @dev Indicates a failed `decreaseAllowance` request.\n     */\n    error SafeERC20FailedDecreaseAllowance(address spender, uint256 currentAllowance, uint256 requestedDecrease);\n\n    /**\n     * @dev Transfer `value` amount of `token` from the calling contract to `to`. If `token` returns no value,\n     * non-reverting calls are assumed to be successful.\n     */\n    function safeTransfer(IERC20 token, address to, uint256 value) internal {\n        _callOptionalReturn(token, abi.encodeCall(token.transfer, (to, value)));\n    }\n\n    /**\n     * @dev Transfer `value` amount of `token` from `from` to `to`, spending the approval given by `from` to the\n     * calling contract. If `token` returns no value, non-reverting calls are assumed to be successful.\n     */\n    function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\n        _callOptionalReturn(token, abi.encodeCall(token.transferFrom, (from, to, value)));\n    }\n\n    /**\n     * @dev Variant of {safeTransfer} that returns a bool instead of reverting if the operation is not successful.\n     */\n    function trySafeTransfer(IERC20 token, address to, uint256 value) internal returns (bool) {\n        return _callOptionalReturnBool(token, abi.encodeCall(token.transfer, (to, value)));\n    }\n\n    /**\n     * @dev Variant of {safeTransferFrom} that returns a bool instead of reverting if the operation is not successful.\n     */\n    function trySafeTransferFrom(IERC20 token, address from, address to, uint256 value) internal returns (bool) {\n        return _callOptionalReturnBool(token, abi.encodeCall(token.transferFrom, (from, to, value)));\n    }\n\n    /**\n     * @dev Increase the calling contract's allowance toward `spender` by `value`. If `token` returns no value,\n     * non-reverting calls are assumed to be successful.\n     *\n     * IMPORTANT: If the token implements ERC-7674 (ERC-20 with temporary allowance), and if the \"client\"\n     * smart contract uses ERC-7674 to set temporary allowances, then the \"client\" smart contract should avoid using\n     * this function. Performing a {safeIncreaseAllowance} or {safeDecreaseAllowance} operation on a token contract\n     * that has a non-zero temporary allowance (for that particular owner-spender) will result in unexpected behavior.\n     */\n    function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\n        uint256 oldAllowance = token.allowance(address(this), spender);\n        forceApprove(token, spender, oldAllowance + value);\n    }\n\n    /**\n     * @dev Decrease the calling contract's allowance toward `spender` by `requestedDecrease`. If `token` returns no\n     * value, non-reverting calls are assumed to be successful.\n     *\n     * IMPORTANT: If the token implements ERC-7674 (ERC-20 with temporary allowance), and if the \"client\"\n     * smart contract uses ERC-7674 to set temporary allowances, then the \"client\" smart contract should avoid using\n     * this function. Performing a {safeIncreaseAllowance} or {safeDecreaseAllowance} operation on a token contract\n     * that has a non-zero temporary allowance (for that particular owner-spender) will result in unexpected behavior.\n     */\n    function safeDecreaseAllowance(IERC20 token, address spender, uint256 requestedDecrease) internal {\n        unchecked {\n            uint256 currentAllowance = token.allowance(address(this), spender);\n            if (currentAllowance < requestedDecrease) {\n                revert SafeERC20FailedDecreaseAllowance(spender, currentAllowance, requestedDecrease);\n            }\n            forceApprove(token, spender, currentAllowance - requestedDecrease);\n        }\n    }\n\n    /**\n     * @dev Set the calling contract's allowance toward `spender` to `value`. If `token` returns no value,\n     * non-reverting calls are assumed to be successful. Meant to be used with tokens that require the approval\n     * to be set to zero before setting it to a non-zero value, such as USDT.\n     *\n     * NOTE: If the token implements ERC-7674, this function will not modify any temporary allowance. This function\n     * only sets the \"standard\" allowance. Any temporary allowance will remain active, in addition to the value being\n     * set here.\n     */\n    function forceApprove(IERC20 token, address spender, uint256 value) internal {\n        bytes memory approvalCall = abi.encodeCall(token.approve, (spender, value));\n\n        if (!_callOptionalReturnBool(token, approvalCall)) {\n            _callOptionalReturn(token, abi.encodeCall(token.approve, (spender, 0)));\n            _callOptionalReturn(token, approvalCall);\n        }\n    }\n\n    /**\n     * @dev Performs an {ERC1363} transferAndCall, with a fallback to the simple {ERC20} transfer if the target has no\n     * code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\n     * targeting contracts.\n     *\n     * Reverts if the returned value is other than `true`.\n     */\n    function transferAndCallRelaxed(IERC1363 token, address to, uint256 value, bytes memory data) internal {\n        if (to.code.length == 0) {\n            safeTransfer(token, to, value);\n        } else if (!token.transferAndCall(to, value, data)) {\n            revert SafeERC20FailedOperation(address(token));\n        }\n    }\n\n    /**\n     * @dev Performs an {ERC1363} transferFromAndCall, with a fallback to the simple {ERC20} transferFrom if the target\n     * has no code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\n     * targeting contracts.\n     *\n     * Reverts if the returned value is other than `true`.\n     */\n    function transferFromAndCallRelaxed(\n        IERC1363 token,\n        address from,\n        address to,\n        uint256 value,\n        bytes memory data\n    ) internal {\n        if (to.code.length == 0) {\n            safeTransferFrom(token, from, to, value);\n        } else if (!token.transferFromAndCall(from, to, value, data)) {\n            revert SafeERC20FailedOperation(address(token));\n        }\n    }\n\n    /**\n     * @dev Performs an {ERC1363} approveAndCall, with a fallback to the simple {ERC20} approve if the target has no\n     * code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\n     * targeting contracts.\n     *\n     * NOTE: When the recipient address (`to`) has no code (i.e. is an EOA), this function behaves as {forceApprove}.\n     * Opposedly, when the recipient address (`to`) has code, this function only attempts to call {ERC1363-approveAndCall}\n     * once without retrying, and relies on the returned value to be true.\n     *\n     * Reverts if the returned value is other than `true`.\n     */\n    function approveAndCallRelaxed(IERC1363 token, address to, uint256 value, bytes memory data) internal {\n        if (to.code.length == 0) {\n            forceApprove(token, to, value);\n        } else if (!token.approveAndCall(to, value, data)) {\n            revert SafeERC20FailedOperation(address(token));\n        }\n    }\n\n    /**\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\n     * @param token The token targeted by the call.\n     * @param data The call data (encoded using abi.encode or one of its variants).\n     *\n     * This is a variant of {_callOptionalReturnBool} that reverts if call fails to meet the requirements.\n     */\n    function _callOptionalReturn(IERC20 token, bytes memory data) private {\n        uint256 returnSize;\n        uint256 returnValue;\n        assembly (\"memory-safe\") {\n            let success := call(gas(), token, 0, add(data, 0x20), mload(data), 0, 0x20)\n            // bubble errors\n            if iszero(success) {\n                let ptr := mload(0x40)\n                returndatacopy(ptr, 0, returndatasize())\n                revert(ptr, returndatasize())\n            }\n            returnSize := returndatasize()\n            returnValue := mload(0)\n        }\n\n        if (returnSize == 0 ? address(token).code.length == 0 : returnValue != 1) {\n            revert SafeERC20FailedOperation(address(token));\n        }\n    }\n\n    /**\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\n     * @param token The token targeted by the call.\n     * @param data The call data (encoded using abi.encode or one of its variants).\n     *\n     * This is a variant of {_callOptionalReturn} that silently catches all reverts and returns a bool instead.\n     */\n    function _callOptionalReturnBool(IERC20 token, bytes memory data) private returns (bool) {\n        bool success;\n        uint256 returnSize;\n        uint256 returnValue;\n        assembly (\"memory-safe\") {\n            success := call(gas(), token, 0, add(data, 0x20), mload(data), 0, 0x20)\n            returnSize := returndatasize()\n            returnValue := mload(0)\n        }\n        return success && (returnSize == 0 ? address(token).code.length > 0 : returnValue == 1);\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC721/IERC721Receiver.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @title ERC-721 token receiver interface\n * @dev Interface for any contract that wants to support safeTransfers\n * from ERC-721 asset contracts.\n */\ninterface IERC721Receiver {\n    /**\n     * @dev Whenever an {IERC721} `tokenId` token is transferred to this contract via {IERC721-safeTransferFrom}\n     * by `operator` from `from`, this function is called.\n     *\n     * It must return its Solidity selector to confirm the token transfer.\n     * If any other value is returned or the interface is not implemented by the recipient, the transfer will be\n     * reverted.\n     *\n     * The selector can be obtained in Solidity with `IERC721Receiver.onERC721Received.selector`.\n     */\n    function onERC721Received(\n        address operator,\n        address from,\n        uint256 tokenId,\n        bytes calldata data\n    ) external returns (bytes4);\n}\n"}, "@openzeppelin/contracts/utils/Context.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\nabstract contract Context {\n    function _msgSender() internal view virtual returns (address) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view virtual returns (bytes calldata) {\n        return msg.data;\n    }\n\n    function _contextSuffixLength() internal view virtual returns (uint256) {\n        return 0;\n    }\n}\n"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/introspection/IERC165.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Interface of the ERC-165 standard, as defined in the\n * https://eips.ethereum.org/EIPS/eip-165[ERC].\n *\n * Implementers can declare support of contract interfaces, which can then be\n * queried by others ({ERC165<PERSON><PERSON><PERSON>}).\n *\n * For an implementation, see {ERC165}.\n */\ninterface IERC165 {\n    /**\n     * @dev Returns true if this contract implements the interface defined by\n     * `interfaceId`. See the corresponding\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section]\n     * to learn more about how these ids are created.\n     *\n     * This function call must use less than 30 000 gas.\n     */\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\n}\n"}, "@openzeppelin/contracts/utils/math/Math.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (utils/math/Math.sol)\n\npragma solidity ^0.8.20;\n\nimport {Panic} from \"../Panic.sol\";\nimport {SafeCast} from \"./SafeCast.sol\";\n\n/**\n * @dev Standard math utilities missing in the Solidity language.\n */\nlibrary Math {\n    enum Rounding {\n        Floor, // Toward negative infinity\n        Ceil, // Toward positive infinity\n        Trunc, // Toward zero\n        Expand // Away from zero\n    }\n\n    /**\n     * @dev Return the 512-bit addition of two uint256.\n     *\n     * The result is stored in two 256 variables such that sum = high * 2²⁵⁶ + low.\n     */\n    function add512(uint256 a, uint256 b) internal pure returns (uint256 high, uint256 low) {\n        assembly (\"memory-safe\") {\n            low := add(a, b)\n            high := lt(low, a)\n        }\n    }\n\n    /**\n     * @dev Return the 512-bit multiplication of two uint256.\n     *\n     * The result is stored in two 256 variables such that product = high * 2²⁵⁶ + low.\n     */\n    function mul512(uint256 a, uint256 b) internal pure returns (uint256 high, uint256 low) {\n        // 512-bit multiply [high low] = x * y. Compute the product mod 2²⁵⁶ and mod 2²⁵⁶ - 1, then use\n        // the Chinese Remainder Theorem to reconstruct the 512 bit result. The result is stored in two 256\n        // variables such that product = high * 2²⁵⁶ + low.\n        assembly (\"memory-safe\") {\n            let mm := mulmod(a, b, not(0))\n            low := mul(a, b)\n            high := sub(sub(mm, low), lt(mm, low))\n        }\n    }\n\n    /**\n     * @dev Returns the addition of two unsigned integers, with a success flag (no overflow).\n     */\n    function tryAdd(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\n        unchecked {\n            uint256 c = a + b;\n            success = c >= a;\n            result = c * SafeCast.toUint(success);\n        }\n    }\n\n    /**\n     * @dev Returns the subtraction of two unsigned integers, with a success flag (no overflow).\n     */\n    function trySub(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\n        unchecked {\n            uint256 c = a - b;\n            success = c <= a;\n            result = c * SafeCast.toUint(success);\n        }\n    }\n\n    /**\n     * @dev Returns the multiplication of two unsigned integers, with a success flag (no overflow).\n     */\n    function tryMul(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\n        unchecked {\n            uint256 c = a * b;\n            assembly (\"memory-safe\") {\n                // Only true when the multiplication doesn't overflow\n                // (c / a == b) || (a == 0)\n                success := or(eq(div(c, a), b), iszero(a))\n            }\n            // equivalent to: success ? c : 0\n            result = c * SafeCast.toUint(success);\n        }\n    }\n\n    /**\n     * @dev Returns the division of two unsigned integers, with a success flag (no division by zero).\n     */\n    function tryDiv(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\n        unchecked {\n            success = b > 0;\n            assembly (\"memory-safe\") {\n                // The `DIV` opcode returns zero when the denominator is 0.\n                result := div(a, b)\n            }\n        }\n    }\n\n    /**\n     * @dev Returns the remainder of dividing two unsigned integers, with a success flag (no division by zero).\n     */\n    function tryMod(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\n        unchecked {\n            success = b > 0;\n            assembly (\"memory-safe\") {\n                // The `MOD` opcode returns zero when the denominator is 0.\n                result := mod(a, b)\n            }\n        }\n    }\n\n    /**\n     * @dev Unsigned saturating addition, bounds to `2²⁵⁶ - 1` instead of overflowing.\n     */\n    function saturatingAdd(uint256 a, uint256 b) internal pure returns (uint256) {\n        (bool success, uint256 result) = tryAdd(a, b);\n        return ternary(success, result, type(uint256).max);\n    }\n\n    /**\n     * @dev Unsigned saturating subtraction, bounds to zero instead of overflowing.\n     */\n    function saturatingSub(uint256 a, uint256 b) internal pure returns (uint256) {\n        (, uint256 result) = trySub(a, b);\n        return result;\n    }\n\n    /**\n     * @dev Unsigned saturating multiplication, bounds to `2²⁵⁶ - 1` instead of overflowing.\n     */\n    function saturatingMul(uint256 a, uint256 b) internal pure returns (uint256) {\n        (bool success, uint256 result) = tryMul(a, b);\n        return ternary(success, result, type(uint256).max);\n    }\n\n    /**\n     * @dev Branchless ternary evaluation for `a ? b : c`. Gas costs are constant.\n     *\n     * IMPORTANT: This function may reduce bytecode size and consume less gas when used standalone.\n     * However, the compiler may optimize Solidity ternary operations (i.e. `a ? b : c`) to only compute\n     * one branch when needed, making this function more expensive.\n     */\n    function ternary(bool condition, uint256 a, uint256 b) internal pure returns (uint256) {\n        unchecked {\n            // branchless ternary works because:\n            // b ^ (a ^ b) == a\n            // b ^ 0 == b\n            return b ^ ((a ^ b) * SafeCast.toUint(condition));\n        }\n    }\n\n    /**\n     * @dev Returns the largest of two numbers.\n     */\n    function max(uint256 a, uint256 b) internal pure returns (uint256) {\n        return ternary(a > b, a, b);\n    }\n\n    /**\n     * @dev Returns the smallest of two numbers.\n     */\n    function min(uint256 a, uint256 b) internal pure returns (uint256) {\n        return ternary(a < b, a, b);\n    }\n\n    /**\n     * @dev Returns the average of two numbers. The result is rounded towards\n     * zero.\n     */\n    function average(uint256 a, uint256 b) internal pure returns (uint256) {\n        // (a + b) / 2 can overflow.\n        return (a & b) + (a ^ b) / 2;\n    }\n\n    /**\n     * @dev Returns the ceiling of the division of two numbers.\n     *\n     * This differs from standard division with `/` in that it rounds towards infinity instead\n     * of rounding towards zero.\n     */\n    function ceilDiv(uint256 a, uint256 b) internal pure returns (uint256) {\n        if (b == 0) {\n            // Guarantee the same behavior as in a regular Solidity division.\n            Panic.panic(Panic.DIVISION_BY_ZERO);\n        }\n\n        // The following calculation ensures accurate ceiling division without overflow.\n        // Since a is non-zero, (a - 1) / b will not overflow.\n        // The largest possible result occurs when (a - 1) / b is type(uint256).max,\n        // but the largest value we can obtain is type(uint256).max - 1, which happens\n        // when a = type(uint256).max and b = 1.\n        unchecked {\n            return SafeCast.toUint(a > 0) * ((a - 1) / b + 1);\n        }\n    }\n\n    /**\n     * @dev Calculates floor(x * y / denominator) with full precision. Throws if result overflows a uint256 or\n     * denominator == 0.\n     *\n     * Original credit to Remco Bloemen under MIT license (https://xn--2-umb.com/21/muldiv) with further edits by\n     * Uniswap Labs also under MIT license.\n     */\n    function mulDiv(uint256 x, uint256 y, uint256 denominator) internal pure returns (uint256 result) {\n        unchecked {\n            (uint256 high, uint256 low) = mul512(x, y);\n\n            // Handle non-overflow cases, 256 by 256 division.\n            if (high == 0) {\n                // Solidity will revert if denominator == 0, unlike the div opcode on its own.\n                // The surrounding unchecked block does not change this fact.\n                // See https://docs.soliditylang.org/en/latest/control-structures.html#checked-or-unchecked-arithmetic.\n                return low / denominator;\n            }\n\n            // Make sure the result is less than 2²⁵⁶. Also prevents denominator == 0.\n            if (denominator <= high) {\n                Panic.panic(ternary(denominator == 0, Panic.DIVISION_BY_ZERO, Panic.UNDER_OVERFLOW));\n            }\n\n            ///////////////////////////////////////////////\n            // 512 by 256 division.\n            ///////////////////////////////////////////////\n\n            // Make division exact by subtracting the remainder from [high low].\n            uint256 remainder;\n            assembly (\"memory-safe\") {\n                // Compute remainder using mulmod.\n                remainder := mulmod(x, y, denominator)\n\n                // Subtract 256 bit number from 512 bit number.\n                high := sub(high, gt(remainder, low))\n                low := sub(low, remainder)\n            }\n\n            // Factor powers of two out of denominator and compute largest power of two divisor of denominator.\n            // Always >= 1. See https://cs.stackexchange.com/q/138556/92363.\n\n            uint256 twos = denominator & (0 - denominator);\n            assembly (\"memory-safe\") {\n                // Divide denominator by twos.\n                denominator := div(denominator, twos)\n\n                // Divide [high low] by twos.\n                low := div(low, twos)\n\n                // Flip twos such that it is 2²⁵⁶ / twos. If twos is zero, then it becomes one.\n                twos := add(div(sub(0, twos), twos), 1)\n            }\n\n            // Shift in bits from high into low.\n            low |= high * twos;\n\n            // Invert denominator mod 2²⁵⁶. Now that denominator is an odd number, it has an inverse modulo 2²⁵⁶ such\n            // that denominator * inv ≡ 1 mod 2²⁵⁶. Compute the inverse by starting with a seed that is correct for\n            // four bits. That is, denominator * inv ≡ 1 mod 2⁴.\n            uint256 inverse = (3 * denominator) ^ 2;\n\n            // Use the Newton-Raphson iteration to improve the precision. Thanks to Hensel's lifting lemma, this also\n            // works in modular arithmetic, doubling the correct bits in each step.\n            inverse *= 2 - denominator * inverse; // inverse mod 2⁸\n            inverse *= 2 - denominator * inverse; // inverse mod 2¹⁶\n            inverse *= 2 - denominator * inverse; // inverse mod 2³²\n            inverse *= 2 - denominator * inverse; // inverse mod 2⁶⁴\n            inverse *= 2 - denominator * inverse; // inverse mod 2¹²⁸\n            inverse *= 2 - denominator * inverse; // inverse mod 2²⁵⁶\n\n            // Because the division is now exact we can divide by multiplying with the modular inverse of denominator.\n            // This will give us the correct result modulo 2²⁵⁶. Since the preconditions guarantee that the outcome is\n            // less than 2²⁵⁶, this is the final result. We don't need to compute the high bits of the result and high\n            // is no longer required.\n            result = low * inverse;\n            return result;\n        }\n    }\n\n    /**\n     * @dev Calculates x * y / denominator with full precision, following the selected rounding direction.\n     */\n    function mulDiv(uint256 x, uint256 y, uint256 denominator, Rounding rounding) internal pure returns (uint256) {\n        return mulDiv(x, y, denominator) + SafeCast.toUint(unsignedRoundsUp(rounding) && mulmod(x, y, denominator) > 0);\n    }\n\n    /**\n     * @dev Calculates floor(x * y >> n) with full precision. Throws if result overflows a uint256.\n     */\n    function mulShr(uint256 x, uint256 y, uint8 n) internal pure returns (uint256 result) {\n        unchecked {\n            (uint256 high, uint256 low) = mul512(x, y);\n            if (high >= 1 << n) {\n                Panic.panic(Panic.UNDER_OVERFLOW);\n            }\n            return (high << (256 - n)) | (low >> n);\n        }\n    }\n\n    /**\n     * @dev Calculates x * y >> n with full precision, following the selected rounding direction.\n     */\n    function mulShr(uint256 x, uint256 y, uint8 n, Rounding rounding) internal pure returns (uint256) {\n        return mulShr(x, y, n) + SafeCast.toUint(unsignedRoundsUp(rounding) && mulmod(x, y, 1 << n) > 0);\n    }\n\n    /**\n     * @dev Calculate the modular multiplicative inverse of a number in Z/nZ.\n     *\n     * If n is a prime, then Z/nZ is a field. In that case all elements are inversible, except 0.\n     * If n is not a prime, then Z/nZ is not a field, and some elements might not be inversible.\n     *\n     * If the input value is not inversible, 0 is returned.\n     *\n     * NOTE: If you know for sure that n is (big) a prime, it may be cheaper to use Fermat's little theorem and get the\n     * inverse using `Math.modExp(a, n - 2, n)`. See {invModPrime}.\n     */\n    function invMod(uint256 a, uint256 n) internal pure returns (uint256) {\n        unchecked {\n            if (n == 0) return 0;\n\n            // The inverse modulo is calculated using the Extended Euclidean Algorithm (iterative version)\n            // Used to compute integers x and y such that: ax + ny = gcd(a, n).\n            // When the gcd is 1, then the inverse of a modulo n exists and it's x.\n            // ax + ny = 1\n            // ax = 1 + (-y)n\n            // ax ≡ 1 (mod n) # x is the inverse of a modulo n\n\n            // If the remainder is 0 the gcd is n right away.\n            uint256 remainder = a % n;\n            uint256 gcd = n;\n\n            // Therefore the initial coefficients are:\n            // ax + ny = gcd(a, n) = n\n            // 0a + 1n = n\n            int256 x = 0;\n            int256 y = 1;\n\n            while (remainder != 0) {\n                uint256 quotient = gcd / remainder;\n\n                (gcd, remainder) = (\n                    // The old remainder is the next gcd to try.\n                    remainder,\n                    // Compute the next remainder.\n                    // Can't overflow given that (a % gcd) * (gcd // (a % gcd)) <= gcd\n                    // where gcd is at most n (capped to type(uint256).max)\n                    gcd - remainder * quotient\n                );\n\n                (x, y) = (\n                    // Increment the coefficient of a.\n                    y,\n                    // Decrement the coefficient of n.\n                    // Can overflow, but the result is casted to uint256 so that the\n                    // next value of y is \"wrapped around\" to a value between 0 and n - 1.\n                    x - y * int256(quotient)\n                );\n            }\n\n            if (gcd != 1) return 0; // No inverse exists.\n            return ternary(x < 0, n - uint256(-x), uint256(x)); // Wrap the result if it's negative.\n        }\n    }\n\n    /**\n     * @dev Variant of {invMod}. More efficient, but only works if `p` is known to be a prime greater than `2`.\n     *\n     * From https://en.wikipedia.org/wiki/Fermat%27s_little_theorem[Fermat's little theorem], we know that if p is\n     * prime, then `a**(p-1) ≡ 1 mod p`. As a consequence, we have `a * a**(p-2) ≡ 1 mod p`, which means that\n     * `a**(p-2)` is the modular multiplicative inverse of a in Fp.\n     *\n     * NOTE: this function does NOT check that `p` is a prime greater than `2`.\n     */\n    function invModPrime(uint256 a, uint256 p) internal view returns (uint256) {\n        unchecked {\n            return Math.modExp(a, p - 2, p);\n        }\n    }\n\n    /**\n     * @dev Returns the modular exponentiation of the specified base, exponent and modulus (b ** e % m)\n     *\n     * Requirements:\n     * - modulus can't be zero\n     * - underlying staticcall to precompile must succeed\n     *\n     * IMPORTANT: The result is only valid if the underlying call succeeds. When using this function, make\n     * sure the chain you're using it on supports the precompiled contract for modular exponentiation\n     * at address 0x05 as specified in https://eips.ethereum.org/EIPS/eip-198[EIP-198]. Otherwise,\n     * the underlying function will succeed given the lack of a revert, but the result may be incorrectly\n     * interpreted as 0.\n     */\n    function modExp(uint256 b, uint256 e, uint256 m) internal view returns (uint256) {\n        (bool success, uint256 result) = tryModExp(b, e, m);\n        if (!success) {\n            Panic.panic(Panic.DIVISION_BY_ZERO);\n        }\n        return result;\n    }\n\n    /**\n     * @dev Returns the modular exponentiation of the specified base, exponent and modulus (b ** e % m).\n     * It includes a success flag indicating if the operation succeeded. Operation will be marked as failed if trying\n     * to operate modulo 0 or if the underlying precompile reverted.\n     *\n     * IMPORTANT: The result is only valid if the success flag is true. When using this function, make sure the chain\n     * you're using it on supports the precompiled contract for modular exponentiation at address 0x05 as specified in\n     * https://eips.ethereum.org/EIPS/eip-198[EIP-198]. Otherwise, the underlying function will succeed given the lack\n     * of a revert, but the result may be incorrectly interpreted as 0.\n     */\n    function tryModExp(uint256 b, uint256 e, uint256 m) internal view returns (bool success, uint256 result) {\n        if (m == 0) return (false, 0);\n        assembly (\"memory-safe\") {\n            let ptr := mload(0x40)\n            // | Offset    | Content    | Content (Hex)                                                      |\n            // |-----------|------------|--------------------------------------------------------------------|\n            // | 0x00:0x1f | size of b  | 0x0000000000000000000000000000000000000000000000000000000000000020 |\n            // | 0x20:0x3f | size of e  | 0x0000000000000000000000000000000000000000000000000000000000000020 |\n            // | 0x40:0x5f | size of m  | 0x0000000000000000000000000000000000000000000000000000000000000020 |\n            // | 0x60:0x7f | value of b | 0x<.............................................................b> |\n            // | 0x80:0x9f | value of e | 0x<.............................................................e> |\n            // | 0xa0:0xbf | value of m | 0x<.............................................................m> |\n            mstore(ptr, 0x20)\n            mstore(add(ptr, 0x20), 0x20)\n            mstore(add(ptr, 0x40), 0x20)\n            mstore(add(ptr, 0x60), b)\n            mstore(add(ptr, 0x80), e)\n            mstore(add(ptr, 0xa0), m)\n\n            // Given the result < m, it's guaranteed to fit in 32 bytes,\n            // so we can use the memory scratch space located at offset 0.\n            success := staticcall(gas(), 0x05, ptr, 0xc0, 0x00, 0x20)\n            result := mload(0x00)\n        }\n    }\n\n    /**\n     * @dev Variant of {modExp} that supports inputs of arbitrary length.\n     */\n    function modExp(bytes memory b, bytes memory e, bytes memory m) internal view returns (bytes memory) {\n        (bool success, bytes memory result) = tryModExp(b, e, m);\n        if (!success) {\n            Panic.panic(Panic.DIVISION_BY_ZERO);\n        }\n        return result;\n    }\n\n    /**\n     * @dev Variant of {tryModExp} that supports inputs of arbitrary length.\n     */\n    function tryModExp(\n        bytes memory b,\n        bytes memory e,\n        bytes memory m\n    ) internal view returns (bool success, bytes memory result) {\n        if (_zeroBytes(m)) return (false, new bytes(0));\n\n        uint256 mLen = m.length;\n\n        // Encode call args in result and move the free memory pointer\n        result = abi.encodePacked(b.length, e.length, mLen, b, e, m);\n\n        assembly (\"memory-safe\") {\n            let dataPtr := add(result, 0x20)\n            // Write result on top of args to avoid allocating extra memory.\n            success := staticcall(gas(), 0x05, dataPtr, mload(result), dataPtr, mLen)\n            // Overwrite the length.\n            // result.length > returndatasize() is guaranteed because returndatasize() == m.length\n            mstore(result, mLen)\n            // Set the memory pointer after the returned data.\n            mstore(0x40, add(dataPtr, mLen))\n        }\n    }\n\n    /**\n     * @dev Returns whether the provided byte array is zero.\n     */\n    function _zeroBytes(bytes memory byteArray) private pure returns (bool) {\n        for (uint256 i = 0; i < byteArray.length; ++i) {\n            if (byteArray[i] != 0) {\n                return false;\n            }\n        }\n        return true;\n    }\n\n    /**\n     * @dev Returns the square root of a number. If the number is not a perfect square, the value is rounded\n     * towards zero.\n     *\n     * This method is based on Newton's method for computing square roots; the algorithm is restricted to only\n     * using integer operations.\n     */\n    function sqrt(uint256 a) internal pure returns (uint256) {\n        unchecked {\n            // Take care of easy edge cases when a == 0 or a == 1\n            if (a <= 1) {\n                return a;\n            }\n\n            // In this function, we use Newton's method to get a root of `f(x) := x² - a`. It involves building a\n            // sequence x_n that converges toward sqrt(a). For each iteration x_n, we also define the error between\n            // the current value as `ε_n = | x_n - sqrt(a) |`.\n            //\n            // For our first estimation, we consider `e` the smallest power of 2 which is bigger than the square root\n            // of the target. (i.e. `2**(e-1) ≤ sqrt(a) < 2**e`). We know that `e ≤ 128` because `(2¹²⁸)² = 2²⁵⁶` is\n            // bigger than any uint256.\n            //\n            // By noticing that\n            // `2**(e-1) ≤ sqrt(a) < 2**e → (2**(e-1))² ≤ a < (2**e)² → 2**(2*e-2) ≤ a < 2**(2*e)`\n            // we can deduce that `e - 1` is `log2(a) / 2`. We can thus compute `x_n = 2**(e-1)` using a method similar\n            // to the msb function.\n            uint256 aa = a;\n            uint256 xn = 1;\n\n            if (aa >= (1 << 128)) {\n                aa >>= 128;\n                xn <<= 64;\n            }\n            if (aa >= (1 << 64)) {\n                aa >>= 64;\n                xn <<= 32;\n            }\n            if (aa >= (1 << 32)) {\n                aa >>= 32;\n                xn <<= 16;\n            }\n            if (aa >= (1 << 16)) {\n                aa >>= 16;\n                xn <<= 8;\n            }\n            if (aa >= (1 << 8)) {\n                aa >>= 8;\n                xn <<= 4;\n            }\n            if (aa >= (1 << 4)) {\n                aa >>= 4;\n                xn <<= 2;\n            }\n            if (aa >= (1 << 2)) {\n                xn <<= 1;\n            }\n\n            // We now have x_n such that `x_n = 2**(e-1) ≤ sqrt(a) < 2**e = 2 * x_n`. This implies ε_n ≤ 2**(e-1).\n            //\n            // We can refine our estimation by noticing that the middle of that interval minimizes the error.\n            // If we move x_n to equal 2**(e-1) + 2**(e-2), then we reduce the error to ε_n ≤ 2**(e-2).\n            // This is going to be our x_0 (and ε_0)\n            xn = (3 * xn) >> 1; // ε_0 := | x_0 - sqrt(a) | ≤ 2**(e-2)\n\n            // From here, Newton's method give us:\n            // x_{n+1} = (x_n + a / x_n) / 2\n            //\n            // One should note that:\n            // x_{n+1}² - a = ((x_n + a / x_n) / 2)² - a\n            //              = ((x_n² + a) / (2 * x_n))² - a\n            //              = (x_n⁴ + 2 * a * x_n² + a²) / (4 * x_n²) - a\n            //              = (x_n⁴ + 2 * a * x_n² + a² - 4 * a * x_n²) / (4 * x_n²)\n            //              = (x_n⁴ - 2 * a * x_n² + a²) / (4 * x_n²)\n            //              = (x_n² - a)² / (2 * x_n)²\n            //              = ((x_n² - a) / (2 * x_n))²\n            //              ≥ 0\n            // Which proves that for all n ≥ 1, sqrt(a) ≤ x_n\n            //\n            // This gives us the proof of quadratic convergence of the sequence:\n            // ε_{n+1} = | x_{n+1} - sqrt(a) |\n            //         = | (x_n + a / x_n) / 2 - sqrt(a) |\n            //         = | (x_n² + a - 2*x_n*sqrt(a)) / (2 * x_n) |\n            //         = | (x_n - sqrt(a))² / (2 * x_n) |\n            //         = | ε_n² / (2 * x_n) |\n            //         = ε_n² / | (2 * x_n) |\n            //\n            // For the first iteration, we have a special case where x_0 is known:\n            // ε_1 = ε_0² / | (2 * x_0) |\n            //     ≤ (2**(e-2))² / (2 * (2**(e-1) + 2**(e-2)))\n            //     ≤ 2**(2*e-4) / (3 * 2**(e-1))\n            //     ≤ 2**(e-3) / 3\n            //     ≤ 2**(e-3-log2(3))\n            //     ≤ 2**(e-4.5)\n            //\n            // For the following iterations, we use the fact that, 2**(e-1) ≤ sqrt(a) ≤ x_n:\n            // ε_{n+1} = ε_n² / | (2 * x_n) |\n            //         ≤ (2**(e-k))² / (2 * 2**(e-1))\n            //         ≤ 2**(2*e-2*k) / 2**e\n            //         ≤ 2**(e-2*k)\n            xn = (xn + a / xn) >> 1; // ε_1 := | x_1 - sqrt(a) | ≤ 2**(e-4.5)  -- special case, see above\n            xn = (xn + a / xn) >> 1; // ε_2 := | x_2 - sqrt(a) | ≤ 2**(e-9)    -- general case with k = 4.5\n            xn = (xn + a / xn) >> 1; // ε_3 := | x_3 - sqrt(a) | ≤ 2**(e-18)   -- general case with k = 9\n            xn = (xn + a / xn) >> 1; // ε_4 := | x_4 - sqrt(a) | ≤ 2**(e-36)   -- general case with k = 18\n            xn = (xn + a / xn) >> 1; // ε_5 := | x_5 - sqrt(a) | ≤ 2**(e-72)   -- general case with k = 36\n            xn = (xn + a / xn) >> 1; // ε_6 := | x_6 - sqrt(a) | ≤ 2**(e-144)  -- general case with k = 72\n\n            // Because e ≤ 128 (as discussed during the first estimation phase), we know have reached a precision\n            // ε_6 ≤ 2**(e-144) < 1. Given we're operating on integers, then we can ensure that xn is now either\n            // sqrt(a) or sqrt(a) + 1.\n            return xn - SafeCast.toUint(xn > a / xn);\n        }\n    }\n\n    /**\n     * @dev Calculates sqrt(a), following the selected rounding direction.\n     */\n    function sqrt(uint256 a, Rounding rounding) internal pure returns (uint256) {\n        unchecked {\n            uint256 result = sqrt(a);\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && result * result < a);\n        }\n    }\n\n    /**\n     * @dev Return the log in base 2 of a positive value rounded towards zero.\n     * Returns 0 if given 0.\n     */\n    function log2(uint256 x) internal pure returns (uint256 r) {\n        // If value has upper 128 bits set, log2 result is at least 128\n        r = SafeCast.toUint(x > 0xffffffffffffffffffffffffffffffff) << 7;\n        // If upper 64 bits of 128-bit half set, add 64 to result\n        r |= SafeCast.toUint((x >> r) > 0xffffffffffffffff) << 6;\n        // If upper 32 bits of 64-bit half set, add 32 to result\n        r |= SafeCast.toUint((x >> r) > 0xffffffff) << 5;\n        // If upper 16 bits of 32-bit half set, add 16 to result\n        r |= SafeCast.toUint((x >> r) > 0xffff) << 4;\n        // If upper 8 bits of 16-bit half set, add 8 to result\n        r |= SafeCast.toUint((x >> r) > 0xff) << 3;\n        // If upper 4 bits of 8-bit half set, add 4 to result\n        r |= SafeCast.toUint((x >> r) > 0xf) << 2;\n\n        // Shifts value right by the current result and use it as an index into this lookup table:\n        //\n        // | x (4 bits) |  index  | table[index] = MSB position |\n        // |------------|---------|-----------------------------|\n        // |    0000    |    0    |        table[0] = 0         |\n        // |    0001    |    1    |        table[1] = 0         |\n        // |    0010    |    2    |        table[2] = 1         |\n        // |    0011    |    3    |        table[3] = 1         |\n        // |    0100    |    4    |        table[4] = 2         |\n        // |    0101    |    5    |        table[5] = 2         |\n        // |    0110    |    6    |        table[6] = 2         |\n        // |    0111    |    7    |        table[7] = 2         |\n        // |    1000    |    8    |        table[8] = 3         |\n        // |    1001    |    9    |        table[9] = 3         |\n        // |    1010    |   10    |        table[10] = 3        |\n        // |    1011    |   11    |        table[11] = 3        |\n        // |    1100    |   12    |        table[12] = 3        |\n        // |    1101    |   13    |        table[13] = 3        |\n        // |    1110    |   14    |        table[14] = 3        |\n        // |    1111    |   15    |        table[15] = 3        |\n        //\n        // The lookup table is represented as a 32-byte value with the MSB positions for 0-15 in the last 16 bytes.\n        assembly (\"memory-safe\") {\n            r := or(r, byte(shr(r, x), 0x0000010102020202030303030303030300000000000000000000000000000000))\n        }\n    }\n\n    /**\n     * @dev Return the log in base 2, following the selected rounding direction, of a positive value.\n     * Returns 0 if given 0.\n     */\n    function log2(uint256 value, Rounding rounding) internal pure returns (uint256) {\n        unchecked {\n            uint256 result = log2(value);\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 1 << result < value);\n        }\n    }\n\n    /**\n     * @dev Return the log in base 10 of a positive value rounded towards zero.\n     * Returns 0 if given 0.\n     */\n    function log10(uint256 value) internal pure returns (uint256) {\n        uint256 result = 0;\n        unchecked {\n            if (value >= 10 ** 64) {\n                value /= 10 ** 64;\n                result += 64;\n            }\n            if (value >= 10 ** 32) {\n                value /= 10 ** 32;\n                result += 32;\n            }\n            if (value >= 10 ** 16) {\n                value /= 10 ** 16;\n                result += 16;\n            }\n            if (value >= 10 ** 8) {\n                value /= 10 ** 8;\n                result += 8;\n            }\n            if (value >= 10 ** 4) {\n                value /= 10 ** 4;\n                result += 4;\n            }\n            if (value >= 10 ** 2) {\n                value /= 10 ** 2;\n                result += 2;\n            }\n            if (value >= 10 ** 1) {\n                result += 1;\n            }\n        }\n        return result;\n    }\n\n    /**\n     * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\n     * Returns 0 if given 0.\n     */\n    function log10(uint256 value, Rounding rounding) internal pure returns (uint256) {\n        unchecked {\n            uint256 result = log10(value);\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 10 ** result < value);\n        }\n    }\n\n    /**\n     * @dev Return the log in base 256 of a positive value rounded towards zero.\n     * Returns 0 if given 0.\n     *\n     * Adding one to the result gives the number of pairs of hex symbols needed to represent `value` as a hex string.\n     */\n    function log256(uint256 x) internal pure returns (uint256 r) {\n        // If value has upper 128 bits set, log2 result is at least 128\n        r = SafeCast.toUint(x > 0xffffffffffffffffffffffffffffffff) << 7;\n        // If upper 64 bits of 128-bit half set, add 64 to result\n        r |= SafeCast.toUint((x >> r) > 0xffffffffffffffff) << 6;\n        // If upper 32 bits of 64-bit half set, add 32 to result\n        r |= SafeCast.toUint((x >> r) > 0xffffffff) << 5;\n        // If upper 16 bits of 32-bit half set, add 16 to result\n        r |= SafeCast.toUint((x >> r) > 0xffff) << 4;\n        // Add 1 if upper 8 bits of 16-bit half set, and divide accumulated result by 8\n        return (r >> 3) | SafeCast.toUint((x >> r) > 0xff);\n    }\n\n    /**\n     * @dev Return the log in base 256, following the selected rounding direction, of a positive value.\n     * Returns 0 if given 0.\n     */\n    function log256(uint256 value, Rounding rounding) internal pure returns (uint256) {\n        unchecked {\n            uint256 result = log256(value);\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 1 << (result << 3) < value);\n        }\n    }\n\n    /**\n     * @dev Returns whether a provided rounding mode is considered rounding up for unsigned integers.\n     */\n    function unsignedRoundsUp(Rounding rounding) internal pure returns (bool) {\n        return uint8(rounding) % 2 == 1;\n    }\n}\n"}, "@openzeppelin/contracts/utils/math/SafeCast.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/math/SafeCast.sol)\n// This file was procedurally generated from scripts/generate/templates/SafeCast.js.\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Wrappers over Solidity's uintXX/intXX/bool casting operators with added overflow\n * checks.\n *\n * Downcasting from uint256/int256 in Solidity does not revert on overflow. This can\n * easily result in undesired exploitation or bugs, since developers usually\n * assume that overflows raise errors. `SafeCast` restores this intuition by\n * reverting the transaction when such an operation overflows.\n *\n * Using this library instead of the unchecked operations eliminates an entire\n * class of bugs, so it's recommended to use it always.\n */\nlibrary SafeCast {\n    /**\n     * @dev Value doesn't fit in an uint of `bits` size.\n     */\n    error SafeCastOverflowedUintDowncast(uint8 bits, uint256 value);\n\n    /**\n     * @dev An int value doesn't fit in an uint of `bits` size.\n     */\n    error SafeCastOverflowedIntToUint(int256 value);\n\n    /**\n     * @dev Value doesn't fit in an int of `bits` size.\n     */\n    error SafeCastOverflowedIntDowncast(uint8 bits, int256 value);\n\n    /**\n     * @dev An uint value doesn't fit in an int of `bits` size.\n     */\n    error SafeCastOverflowedUintToInt(uint256 value);\n\n    /**\n     * @dev Returns the downcasted uint248 from uint256, reverting on\n     * overflow (when the input is greater than largest uint248).\n     *\n     * Counterpart to Solidity's `uint248` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 248 bits\n     */\n    function toUint248(uint256 value) internal pure returns (uint248) {\n        if (value > type(uint248).max) {\n            revert SafeCastOverflowedUintDowncast(248, value);\n        }\n        return uint248(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint240 from uint256, reverting on\n     * overflow (when the input is greater than largest uint240).\n     *\n     * Counterpart to Solidity's `uint240` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 240 bits\n     */\n    function toUint240(uint256 value) internal pure returns (uint240) {\n        if (value > type(uint240).max) {\n            revert SafeCastOverflowedUintDowncast(240, value);\n        }\n        return uint240(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint232 from uint256, reverting on\n     * overflow (when the input is greater than largest uint232).\n     *\n     * Counterpart to Solidity's `uint232` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 232 bits\n     */\n    function toUint232(uint256 value) internal pure returns (uint232) {\n        if (value > type(uint232).max) {\n            revert SafeCastOverflowedUintDowncast(232, value);\n        }\n        return uint232(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint224 from uint256, reverting on\n     * overflow (when the input is greater than largest uint224).\n     *\n     * Counterpart to Solidity's `uint224` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 224 bits\n     */\n    function toUint224(uint256 value) internal pure returns (uint224) {\n        if (value > type(uint224).max) {\n            revert SafeCastOverflowedUintDowncast(224, value);\n        }\n        return uint224(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint216 from uint256, reverting on\n     * overflow (when the input is greater than largest uint216).\n     *\n     * Counterpart to Solidity's `uint216` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 216 bits\n     */\n    function toUint216(uint256 value) internal pure returns (uint216) {\n        if (value > type(uint216).max) {\n            revert SafeCastOverflowedUintDowncast(216, value);\n        }\n        return uint216(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint208 from uint256, reverting on\n     * overflow (when the input is greater than largest uint208).\n     *\n     * Counterpart to Solidity's `uint208` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 208 bits\n     */\n    function toUint208(uint256 value) internal pure returns (uint208) {\n        if (value > type(uint208).max) {\n            revert SafeCastOverflowedUintDowncast(208, value);\n        }\n        return uint208(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint200 from uint256, reverting on\n     * overflow (when the input is greater than largest uint200).\n     *\n     * Counterpart to Solidity's `uint200` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 200 bits\n     */\n    function toUint200(uint256 value) internal pure returns (uint200) {\n        if (value > type(uint200).max) {\n            revert SafeCastOverflowedUintDowncast(200, value);\n        }\n        return uint200(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint192 from uint256, reverting on\n     * overflow (when the input is greater than largest uint192).\n     *\n     * Counterpart to Solidity's `uint192` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 192 bits\n     */\n    function toUint192(uint256 value) internal pure returns (uint192) {\n        if (value > type(uint192).max) {\n            revert SafeCastOverflowedUintDowncast(192, value);\n        }\n        return uint192(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint184 from uint256, reverting on\n     * overflow (when the input is greater than largest uint184).\n     *\n     * Counterpart to Solidity's `uint184` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 184 bits\n     */\n    function toUint184(uint256 value) internal pure returns (uint184) {\n        if (value > type(uint184).max) {\n            revert SafeCastOverflowedUintDowncast(184, value);\n        }\n        return uint184(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint176 from uint256, reverting on\n     * overflow (when the input is greater than largest uint176).\n     *\n     * Counterpart to Solidity's `uint176` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 176 bits\n     */\n    function toUint176(uint256 value) internal pure returns (uint176) {\n        if (value > type(uint176).max) {\n            revert SafeCastOverflowedUintDowncast(176, value);\n        }\n        return uint176(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint168 from uint256, reverting on\n     * overflow (when the input is greater than largest uint168).\n     *\n     * Counterpart to Solidity's `uint168` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 168 bits\n     */\n    function toUint168(uint256 value) internal pure returns (uint168) {\n        if (value > type(uint168).max) {\n            revert SafeCastOverflowedUintDowncast(168, value);\n        }\n        return uint168(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint160 from uint256, reverting on\n     * overflow (when the input is greater than largest uint160).\n     *\n     * Counterpart to Solidity's `uint160` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 160 bits\n     */\n    function toUint160(uint256 value) internal pure returns (uint160) {\n        if (value > type(uint160).max) {\n            revert SafeCastOverflowedUintDowncast(160, value);\n        }\n        return uint160(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint152 from uint256, reverting on\n     * overflow (when the input is greater than largest uint152).\n     *\n     * Counterpart to Solidity's `uint152` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 152 bits\n     */\n    function toUint152(uint256 value) internal pure returns (uint152) {\n        if (value > type(uint152).max) {\n            revert SafeCastOverflowedUintDowncast(152, value);\n        }\n        return uint152(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint144 from uint256, reverting on\n     * overflow (when the input is greater than largest uint144).\n     *\n     * Counterpart to Solidity's `uint144` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 144 bits\n     */\n    function toUint144(uint256 value) internal pure returns (uint144) {\n        if (value > type(uint144).max) {\n            revert SafeCastOverflowedUintDowncast(144, value);\n        }\n        return uint144(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint136 from uint256, reverting on\n     * overflow (when the input is greater than largest uint136).\n     *\n     * Counterpart to Solidity's `uint136` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 136 bits\n     */\n    function toUint136(uint256 value) internal pure returns (uint136) {\n        if (value > type(uint136).max) {\n            revert SafeCastOverflowedUintDowncast(136, value);\n        }\n        return uint136(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint128 from uint256, reverting on\n     * overflow (when the input is greater than largest uint128).\n     *\n     * Counterpart to Solidity's `uint128` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 128 bits\n     */\n    function toUint128(uint256 value) internal pure returns (uint128) {\n        if (value > type(uint128).max) {\n            revert SafeCastOverflowedUintDowncast(128, value);\n        }\n        return uint128(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint120 from uint256, reverting on\n     * overflow (when the input is greater than largest uint120).\n     *\n     * Counterpart to Solidity's `uint120` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 120 bits\n     */\n    function toUint120(uint256 value) internal pure returns (uint120) {\n        if (value > type(uint120).max) {\n            revert SafeCastOverflowedUintDowncast(120, value);\n        }\n        return uint120(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint112 from uint256, reverting on\n     * overflow (when the input is greater than largest uint112).\n     *\n     * Counterpart to Solidity's `uint112` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 112 bits\n     */\n    function toUint112(uint256 value) internal pure returns (uint112) {\n        if (value > type(uint112).max) {\n            revert SafeCastOverflowedUintDowncast(112, value);\n        }\n        return uint112(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint104 from uint256, reverting on\n     * overflow (when the input is greater than largest uint104).\n     *\n     * Counterpart to Solidity's `uint104` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 104 bits\n     */\n    function toUint104(uint256 value) internal pure returns (uint104) {\n        if (value > type(uint104).max) {\n            revert SafeCastOverflowedUintDowncast(104, value);\n        }\n        return uint104(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint96 from uint256, reverting on\n     * overflow (when the input is greater than largest uint96).\n     *\n     * Counterpart to Solidity's `uint96` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 96 bits\n     */\n    function toUint96(uint256 value) internal pure returns (uint96) {\n        if (value > type(uint96).max) {\n            revert SafeCastOverflowedUintDowncast(96, value);\n        }\n        return uint96(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint88 from uint256, reverting on\n     * overflow (when the input is greater than largest uint88).\n     *\n     * Counterpart to Solidity's `uint88` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 88 bits\n     */\n    function toUint88(uint256 value) internal pure returns (uint88) {\n        if (value > type(uint88).max) {\n            revert SafeCastOverflowedUintDowncast(88, value);\n        }\n        return uint88(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint80 from uint256, reverting on\n     * overflow (when the input is greater than largest uint80).\n     *\n     * Counterpart to Solidity's `uint80` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 80 bits\n     */\n    function toUint80(uint256 value) internal pure returns (uint80) {\n        if (value > type(uint80).max) {\n            revert SafeCastOverflowedUintDowncast(80, value);\n        }\n        return uint80(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint72 from uint256, reverting on\n     * overflow (when the input is greater than largest uint72).\n     *\n     * Counterpart to Solidity's `uint72` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 72 bits\n     */\n    function toUint72(uint256 value) internal pure returns (uint72) {\n        if (value > type(uint72).max) {\n            revert SafeCastOverflowedUintDowncast(72, value);\n        }\n        return uint72(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint64 from uint256, reverting on\n     * overflow (when the input is greater than largest uint64).\n     *\n     * Counterpart to Solidity's `uint64` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 64 bits\n     */\n    function toUint64(uint256 value) internal pure returns (uint64) {\n        if (value > type(uint64).max) {\n            revert SafeCastOverflowedUintDowncast(64, value);\n        }\n        return uint64(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint56 from uint256, reverting on\n     * overflow (when the input is greater than largest uint56).\n     *\n     * Counterpart to Solidity's `uint56` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 56 bits\n     */\n    function toUint56(uint256 value) internal pure returns (uint56) {\n        if (value > type(uint56).max) {\n            revert SafeCastOverflowedUintDowncast(56, value);\n        }\n        return uint56(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint48 from uint256, reverting on\n     * overflow (when the input is greater than largest uint48).\n     *\n     * Counterpart to Solidity's `uint48` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 48 bits\n     */\n    function toUint48(uint256 value) internal pure returns (uint48) {\n        if (value > type(uint48).max) {\n            revert SafeCastOverflowedUintDowncast(48, value);\n        }\n        return uint48(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint40 from uint256, reverting on\n     * overflow (when the input is greater than largest uint40).\n     *\n     * Counterpart to Solidity's `uint40` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 40 bits\n     */\n    function toUint40(uint256 value) internal pure returns (uint40) {\n        if (value > type(uint40).max) {\n            revert SafeCastOverflowedUintDowncast(40, value);\n        }\n        return uint40(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint32 from uint256, reverting on\n     * overflow (when the input is greater than largest uint32).\n     *\n     * Counterpart to Solidity's `uint32` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 32 bits\n     */\n    function toUint32(uint256 value) internal pure returns (uint32) {\n        if (value > type(uint32).max) {\n            revert SafeCastOverflowedUintDowncast(32, value);\n        }\n        return uint32(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint24 from uint256, reverting on\n     * overflow (when the input is greater than largest uint24).\n     *\n     * Counterpart to Solidity's `uint24` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 24 bits\n     */\n    function toUint24(uint256 value) internal pure returns (uint24) {\n        if (value > type(uint24).max) {\n            revert SafeCastOverflowedUintDowncast(24, value);\n        }\n        return uint24(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint16 from uint256, reverting on\n     * overflow (when the input is greater than largest uint16).\n     *\n     * Counterpart to Solidity's `uint16` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 16 bits\n     */\n    function toUint16(uint256 value) internal pure returns (uint16) {\n        if (value > type(uint16).max) {\n            revert SafeCastOverflowedUintDowncast(16, value);\n        }\n        return uint16(value);\n    }\n\n    /**\n     * @dev Returns the downcasted uint8 from uint256, reverting on\n     * overflow (when the input is greater than largest uint8).\n     *\n     * Counterpart to Solidity's `uint8` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 8 bits\n     */\n    function toUint8(uint256 value) internal pure returns (uint8) {\n        if (value > type(uint8).max) {\n            revert SafeCastOverflowedUintDowncast(8, value);\n        }\n        return uint8(value);\n    }\n\n    /**\n     * @dev Converts a signed int256 into an unsigned uint256.\n     *\n     * Requirements:\n     *\n     * - input must be greater than or equal to 0.\n     */\n    function toUint256(int256 value) internal pure returns (uint256) {\n        if (value < 0) {\n            revert SafeCastOverflowedIntToUint(value);\n        }\n        return uint256(value);\n    }\n\n    /**\n     * @dev Returns the downcasted int248 from int256, reverting on\n     * overflow (when the input is less than smallest int248 or\n     * greater than largest int248).\n     *\n     * Counterpart to Solidity's `int248` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 248 bits\n     */\n    function toInt248(int256 value) internal pure returns (int248 downcasted) {\n        downcasted = int248(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(248, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int240 from int256, reverting on\n     * overflow (when the input is less than smallest int240 or\n     * greater than largest int240).\n     *\n     * Counterpart to Solidity's `int240` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 240 bits\n     */\n    function toInt240(int256 value) internal pure returns (int240 downcasted) {\n        downcasted = int240(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(240, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int232 from int256, reverting on\n     * overflow (when the input is less than smallest int232 or\n     * greater than largest int232).\n     *\n     * Counterpart to Solidity's `int232` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 232 bits\n     */\n    function toInt232(int256 value) internal pure returns (int232 downcasted) {\n        downcasted = int232(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(232, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int224 from int256, reverting on\n     * overflow (when the input is less than smallest int224 or\n     * greater than largest int224).\n     *\n     * Counterpart to Solidity's `int224` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 224 bits\n     */\n    function toInt224(int256 value) internal pure returns (int224 downcasted) {\n        downcasted = int224(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(224, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int216 from int256, reverting on\n     * overflow (when the input is less than smallest int216 or\n     * greater than largest int216).\n     *\n     * Counterpart to Solidity's `int216` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 216 bits\n     */\n    function toInt216(int256 value) internal pure returns (int216 downcasted) {\n        downcasted = int216(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(216, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int208 from int256, reverting on\n     * overflow (when the input is less than smallest int208 or\n     * greater than largest int208).\n     *\n     * Counterpart to Solidity's `int208` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 208 bits\n     */\n    function toInt208(int256 value) internal pure returns (int208 downcasted) {\n        downcasted = int208(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(208, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int200 from int256, reverting on\n     * overflow (when the input is less than smallest int200 or\n     * greater than largest int200).\n     *\n     * Counterpart to Solidity's `int200` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 200 bits\n     */\n    function toInt200(int256 value) internal pure returns (int200 downcasted) {\n        downcasted = int200(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(200, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int192 from int256, reverting on\n     * overflow (when the input is less than smallest int192 or\n     * greater than largest int192).\n     *\n     * Counterpart to Solidity's `int192` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 192 bits\n     */\n    function toInt192(int256 value) internal pure returns (int192 downcasted) {\n        downcasted = int192(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(192, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int184 from int256, reverting on\n     * overflow (when the input is less than smallest int184 or\n     * greater than largest int184).\n     *\n     * Counterpart to Solidity's `int184` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 184 bits\n     */\n    function toInt184(int256 value) internal pure returns (int184 downcasted) {\n        downcasted = int184(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(184, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int176 from int256, reverting on\n     * overflow (when the input is less than smallest int176 or\n     * greater than largest int176).\n     *\n     * Counterpart to Solidity's `int176` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 176 bits\n     */\n    function toInt176(int256 value) internal pure returns (int176 downcasted) {\n        downcasted = int176(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(176, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int168 from int256, reverting on\n     * overflow (when the input is less than smallest int168 or\n     * greater than largest int168).\n     *\n     * Counterpart to Solidity's `int168` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 168 bits\n     */\n    function toInt168(int256 value) internal pure returns (int168 downcasted) {\n        downcasted = int168(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(168, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int160 from int256, reverting on\n     * overflow (when the input is less than smallest int160 or\n     * greater than largest int160).\n     *\n     * Counterpart to Solidity's `int160` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 160 bits\n     */\n    function toInt160(int256 value) internal pure returns (int160 downcasted) {\n        downcasted = int160(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(160, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int152 from int256, reverting on\n     * overflow (when the input is less than smallest int152 or\n     * greater than largest int152).\n     *\n     * Counterpart to Solidity's `int152` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 152 bits\n     */\n    function toInt152(int256 value) internal pure returns (int152 downcasted) {\n        downcasted = int152(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(152, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int144 from int256, reverting on\n     * overflow (when the input is less than smallest int144 or\n     * greater than largest int144).\n     *\n     * Counterpart to Solidity's `int144` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 144 bits\n     */\n    function toInt144(int256 value) internal pure returns (int144 downcasted) {\n        downcasted = int144(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(144, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int136 from int256, reverting on\n     * overflow (when the input is less than smallest int136 or\n     * greater than largest int136).\n     *\n     * Counterpart to Solidity's `int136` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 136 bits\n     */\n    function toInt136(int256 value) internal pure returns (int136 downcasted) {\n        downcasted = int136(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(136, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int128 from int256, reverting on\n     * overflow (when the input is less than smallest int128 or\n     * greater than largest int128).\n     *\n     * Counterpart to Solidity's `int128` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 128 bits\n     */\n    function toInt128(int256 value) internal pure returns (int128 downcasted) {\n        downcasted = int128(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(128, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int120 from int256, reverting on\n     * overflow (when the input is less than smallest int120 or\n     * greater than largest int120).\n     *\n     * Counterpart to Solidity's `int120` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 120 bits\n     */\n    function toInt120(int256 value) internal pure returns (int120 downcasted) {\n        downcasted = int120(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(120, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int112 from int256, reverting on\n     * overflow (when the input is less than smallest int112 or\n     * greater than largest int112).\n     *\n     * Counterpart to Solidity's `int112` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 112 bits\n     */\n    function toInt112(int256 value) internal pure returns (int112 downcasted) {\n        downcasted = int112(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(112, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int104 from int256, reverting on\n     * overflow (when the input is less than smallest int104 or\n     * greater than largest int104).\n     *\n     * Counterpart to Solidity's `int104` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 104 bits\n     */\n    function toInt104(int256 value) internal pure returns (int104 downcasted) {\n        downcasted = int104(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(104, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int96 from int256, reverting on\n     * overflow (when the input is less than smallest int96 or\n     * greater than largest int96).\n     *\n     * Counterpart to Solidity's `int96` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 96 bits\n     */\n    function toInt96(int256 value) internal pure returns (int96 downcasted) {\n        downcasted = int96(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(96, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int88 from int256, reverting on\n     * overflow (when the input is less than smallest int88 or\n     * greater than largest int88).\n     *\n     * Counterpart to Solidity's `int88` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 88 bits\n     */\n    function toInt88(int256 value) internal pure returns (int88 downcasted) {\n        downcasted = int88(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(88, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int80 from int256, reverting on\n     * overflow (when the input is less than smallest int80 or\n     * greater than largest int80).\n     *\n     * Counterpart to Solidity's `int80` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 80 bits\n     */\n    function toInt80(int256 value) internal pure returns (int80 downcasted) {\n        downcasted = int80(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(80, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int72 from int256, reverting on\n     * overflow (when the input is less than smallest int72 or\n     * greater than largest int72).\n     *\n     * Counterpart to Solidity's `int72` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 72 bits\n     */\n    function toInt72(int256 value) internal pure returns (int72 downcasted) {\n        downcasted = int72(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(72, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int64 from int256, reverting on\n     * overflow (when the input is less than smallest int64 or\n     * greater than largest int64).\n     *\n     * Counterpart to Solidity's `int64` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 64 bits\n     */\n    function toInt64(int256 value) internal pure returns (int64 downcasted) {\n        downcasted = int64(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(64, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int56 from int256, reverting on\n     * overflow (when the input is less than smallest int56 or\n     * greater than largest int56).\n     *\n     * Counterpart to Solidity's `int56` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 56 bits\n     */\n    function toInt56(int256 value) internal pure returns (int56 downcasted) {\n        downcasted = int56(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(56, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int48 from int256, reverting on\n     * overflow (when the input is less than smallest int48 or\n     * greater than largest int48).\n     *\n     * Counterpart to Solidity's `int48` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 48 bits\n     */\n    function toInt48(int256 value) internal pure returns (int48 downcasted) {\n        downcasted = int48(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(48, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int40 from int256, reverting on\n     * overflow (when the input is less than smallest int40 or\n     * greater than largest int40).\n     *\n     * Counterpart to Solidity's `int40` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 40 bits\n     */\n    function toInt40(int256 value) internal pure returns (int40 downcasted) {\n        downcasted = int40(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(40, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int32 from int256, reverting on\n     * overflow (when the input is less than smallest int32 or\n     * greater than largest int32).\n     *\n     * Counterpart to Solidity's `int32` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 32 bits\n     */\n    function toInt32(int256 value) internal pure returns (int32 downcasted) {\n        downcasted = int32(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(32, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int24 from int256, reverting on\n     * overflow (when the input is less than smallest int24 or\n     * greater than largest int24).\n     *\n     * Counterpart to Solidity's `int24` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 24 bits\n     */\n    function toInt24(int256 value) internal pure returns (int24 downcasted) {\n        downcasted = int24(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(24, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int16 from int256, reverting on\n     * overflow (when the input is less than smallest int16 or\n     * greater than largest int16).\n     *\n     * Counterpart to Solidity's `int16` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 16 bits\n     */\n    function toInt16(int256 value) internal pure returns (int16 downcasted) {\n        downcasted = int16(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(16, value);\n        }\n    }\n\n    /**\n     * @dev Returns the downcasted int8 from int256, reverting on\n     * overflow (when the input is less than smallest int8 or\n     * greater than largest int8).\n     *\n     * Counterpart to Solidity's `int8` operator.\n     *\n     * Requirements:\n     *\n     * - input must fit into 8 bits\n     */\n    function toInt8(int256 value) internal pure returns (int8 downcasted) {\n        downcasted = int8(value);\n        if (downcasted != value) {\n            revert SafeCastOverflowedIntDowncast(8, value);\n        }\n    }\n\n    /**\n     * @dev Converts an unsigned uint256 into a signed int256.\n     *\n     * Requirements:\n     *\n     * - input must be less than or equal to maxInt256.\n     */\n    function toInt256(uint256 value) internal pure returns (int256) {\n        // Note: Unsafe cast below is okay because `type(int256).max` is guaranteed to be positive\n        if (value > uint256(type(int256).max)) {\n            revert SafeCastOverflowedUintToInt(value);\n        }\n        return int256(value);\n    }\n\n    /**\n     * @dev Cast a boolean (false or true) to a uint256 (0 or 1) with no jump.\n     */\n    function toUint(bool b) internal pure returns (uint256 u) {\n        assembly (\"memory-safe\") {\n            u := iszero(iszero(b))\n        }\n    }\n}\n"}, "@openzeppelin/contracts/utils/Panic.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/Panic.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Helper library for emitting standardized panic codes.\n *\n * ```solidity\n * contract Example {\n *      using Panic for uint256;\n *\n *      // Use any of the declared internal constants\n *      function foo() { Panic.GENERIC.panic(); }\n *\n *      // Alternatively\n *      function foo() { Panic.panic(Panic.GENERIC); }\n * }\n * ```\n *\n * Follows the list from https://github.com/ethereum/solidity/blob/v0.8.24/libsolutil/ErrorCodes.h[libsolutil].\n *\n * _Available since v5.1._\n */\n// slither-disable-next-line unused-state\nlibrary Panic {\n    /// @dev generic / unspecified error\n    uint256 internal constant GENERIC = 0x00;\n    /// @dev used by the assert() builtin\n    uint256 internal constant ASSERT = 0x01;\n    /// @dev arithmetic underflow or overflow\n    uint256 internal constant UNDER_OVERFLOW = 0x11;\n    /// @dev division or modulo by zero\n    uint256 internal constant DIVISION_BY_ZERO = 0x12;\n    /// @dev enum conversion error\n    uint256 internal constant ENUM_CONVERSION_ERROR = 0x21;\n    /// @dev invalid encoding in storage\n    uint256 internal constant STORAGE_ENCODING_ERROR = 0x22;\n    /// @dev empty array pop\n    uint256 internal constant EMPTY_ARRAY_POP = 0x31;\n    /// @dev array out of bounds access\n    uint256 internal constant ARRAY_OUT_OF_BOUNDS = 0x32;\n    /// @dev resource error (too large allocation or too large array)\n    uint256 internal constant RESOURCE_ERROR = 0x41;\n    /// @dev calling invalid internal function\n    uint256 internal constant INVALID_INTERNAL_FUNCTION = 0x51;\n\n    /// @dev Reverts with a panic code. Recommended to use with\n    /// the internal constants with predefined codes.\n    function panic(uint256 code) internal pure {\n        assembly (\"memory-safe\") {\n            mstore(0x00, 0x4e487b71)\n            mstore(0x20, code)\n            revert(0x1c, 0x24)\n        }\n    }\n}\n"}, "contracts/interfaces/IERC165.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\n/**\n * @dev Interface of the ERC165 standard, as defined in the\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\n *\n * Implementers can declare support of contract interfaces, which can then be\n * queried by others ({<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}).\n *\n * For an implementation, see {ERC165}.\n */\ninterface IERC165 {\n    /**\n     * @dev Returns true if this contract implements the interface defined by\n     * `interfaceId`. See the corresponding\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\n     * to learn more about how these ids are created.\n     *\n     * This function call must use less than 30 000 gas.\n     */\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\n}\n"}, "contracts/interfaces/IERC20Extended.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\nimport \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\n\n/**\n * @title IERC20Extended\n * @dev Interface that extends the standard IERC20 with additional metadata functions\n * This interface includes the optional metadata extension functions that are commonly used\n */\ninterface IERC20Extended is IERC20 {\n    /**\n     * @dev Returns the name of the token\n     */\n    function name() external view returns (string memory);\n\n    /**\n     * @dev Returns the symbol of the token\n     */\n    function symbol() external view returns (string memory);\n\n    /**\n     * @dev Returns the number of decimals used to get its user representation\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\n     * be displayed to a user as `5.05` (`505 / 10 ** 2`)\n     */\n    function decimals() external view returns (uint8);\n\n    /**\n     * @dev Returns the total amount of tokens in existence\n     */\n    function totalSupply() external view override returns (uint256);\n\n    /**\n     * @dev Returns the amount of tokens owned by `account`\n     */\n    function balanceOf(address account) external view override returns (uint256);\n\n    /**\n     * @dev Moves `amount` tokens from the caller's account to `to`\n     *\n     * Returns a boolean value indicating whether the operation succeeded\n     *\n     * Emits a {Transfer} event\n     */\n    function transfer(address to, uint256 amount) external override returns (bool);\n\n    /**\n     * @dev Returns the remaining number of tokens that `spender` will be\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\n     * zero by default\n     *\n     * This value changes when {approve} or {transferFrom} are called\n     */\n    function allowance(address owner, address spender) external view override returns (uint256);\n\n    /**\n     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens\n     *\n     * Returns a boolean value indicating whether the operation succeeded\n     *\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\n     * that someone may use both the old and the new allowance by unfortunate\n     * transaction ordering. One possible solution to mitigate this race\n     * condition is to first reduce the spender's allowance to 0 and set the\n     * desired value afterwards:\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\n     *\n     * Emits an {Approval} event\n     */\n    function approve(address spender, uint256 amount) external override returns (bool);\n\n    /**\n     * @dev Moves `amount` tokens from `from` to `to` using the\n     * allowance mechanism. `amount` is then deducted from the caller's\n     * allowance\n     *\n     * Returns a boolean value indicating whether the operation succeeded\n     *\n     * Emits a {Transfer} event\n     */\n    function transferFrom(\n        address from,\n        address to,\n        uint256 amount\n    )\n        external\n        override\n        returns (bool);\n}\n"}, "contracts/interfaces/IERC721.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\nimport \"./IERC165.sol\";\n\n/**\n * @dev Required interface of an ERC721 compliant contract.\n */\ninterface IERC721 is IERC165 {\n    /**\n     * @dev Emitted when `tokenId` token is transferred from `from` to `to`.\n     */\n    event Transfer(address indexed from, address indexed to, uint256 indexed tokenId);\n\n    /**\n     * @dev Emitted when `owner` enables `approved` to manage the `tokenId` token.\n     */\n    event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId);\n\n    /**\n     * @dev Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its\n     * assets.\n     */\n    event ApprovalForAll(address indexed owner, address indexed operator, bool approved);\n\n    /**\n     * @dev Returns the number of tokens in ``owner``'s account.\n     */\n    function balanceOf(address owner) external view returns (uint256 balance);\n\n    /**\n     * @dev Returns the owner of the `tokenId` token.\n     *\n     * Requirements:\n     *\n     * - `tokenId` must exist.\n     */\n    function ownerOf(uint256 tokenId) external view returns (address owner);\n\n    /**\n     * @dev Safely transfers `tokenId` token from `from` to `to`.\n     *\n     * Requirements:\n     *\n     * - `from` cannot be the zero address.\n     * - `to` cannot be the zero address.\n     * - `tokenId` token must exist and be owned by `from`.\n     * - If the caller is not `from`, it must have been allowed to move this token by either\n     * {approve} or {setApprovalForAll}.\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received},\n     * which is called upon a safe transfer.\n     *\n     * Emits a {Transfer} event.\n     */\n    function safeTransferFrom(\n        address from,\n        address to,\n        uint256 tokenId,\n        bytes calldata data\n    )\n        external;\n\n    /**\n     * @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract\n     * recipients\n     * are aware of the ERC721 protocol to prevent tokens from being forever locked.\n     *\n     * Requirements:\n     *\n     * - `from` cannot be the zero address.\n     * - `to` cannot be the zero address.\n     * - `tokenId` token must exist and be owned by `from`.\n     * - If the caller is not `from`, it must have been allowed to move this token by either\n     * {approve} or {setApprovalForAll}.\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received},\n     * which is called upon a safe transfer.\n     *\n     * Emits a {Transfer} event.\n     */\n    function safeTransferFrom(address from, address to, uint256 tokenId) external;\n\n    /**\n     * @dev Transfers `tokenId` token from `from` to `to`.\n     *\n     * WARNING: Note that the caller is responsible to confirm that the recipient is capable of\n     * receiving ERC721\n     * or else they may be permanently lost. Usage of {safeTransferFrom} prevents loss, though the\n     * caller must\n     * understand this adds an external call which potentially creates a reentrancy vulnerability.\n     *\n     * Requirements:\n     *\n     * - `from` cannot be the zero address.\n     * - `to` cannot be the zero address.\n     * - `tokenId` token must be owned by `from`.\n     * - If the caller is not `from`, it must be approved to move this token by either {approve} or\n     * {setApprovalForAll}.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transferFrom(address from, address to, uint256 tokenId) external;\n\n    /**\n     * @dev Gives permission to `to` to transfer `tokenId` token to another account.\n     * The approval is cleared when the token is transferred.\n     *\n     * Only a single account can be approved at a time, so approving the zero address clears\n     * previous approvals.\n     *\n     * Requirements:\n     *\n     * - The caller must own the token or be an approved operator.\n     * - `tokenId` must exist.\n     *\n     * Emits an {Approval} event.\n     */\n    function approve(address to, uint256 tokenId) external;\n\n    /**\n     * @dev Approve or remove `operator` as an operator for the caller.\n     * Operators can call {transferFrom} or {safeTransferFrom} for any token owned by the caller.\n     *\n     * Requirements:\n     *\n     * - The `operator` cannot be the caller.\n     *\n     * Emits an {ApprovalForAll} event.\n     */\n    function setApprovalForAll(address operator, bool approved) external;\n\n    /**\n     * @dev Returns the account approved for `tokenId` token.\n     *\n     * Requirements:\n     *\n     * - `tokenId` must exist.\n     */\n    function getApproved(uint256 tokenId) external view returns (address operator);\n\n    /**\n     * @dev Returns if the `operator` is allowed to manage all of the assets of `owner`.\n     *\n     * See {setApprovalForAll}\n     */\n    function isApprovedForAll(address owner, address operator) external view returns (bool);\n}\n"}, "contracts/interfaces/IERC721Enumerable.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\nimport \"./IERC721.sol\";\n\n/**\n * @title ERC-721 Non-Fungible Token Standard, optional enumeration extension\n * @dev See https://eips.ethereum.org/EIPS/eip-721\n */\ninterface IERC721Enumerable is IERC721 {\n    /**\n     * @dev Returns the total amount of tokens stored by the contract.\n     */\n    function totalSupply() external view returns (uint256);\n\n    /**\n     * @dev Returns a token ID owned by `owner` at a given `index` of its token list.\n     * Use along with {balanceOf} to enumerate all of ``owner``'s tokens.\n     */\n    function tokenOfOwnerByIndex(address owner, uint256 index) external view returns (uint256);\n\n    /**\n     * @dev Returns a token ID at a given `index` of all the tokens stored by the contract.\n     * Use along with {totalSupply} to enumerate all tokens.\n     */\n    function tokenByIndex(uint256 index) external view returns (uint256);\n}\n"}, "contracts/interfaces/IERC721Metadata.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\nimport \"./IERC721.sol\";\n\n/**\n * @title ERC-721 Non-Fungible Token Standard, optional metadata extension\n * @dev See https://eips.ethereum.org/EIPS/eip-721\n */\ninterface IERC721Metadata is IERC721 {\n    /**\n     * @dev Returns the token collection name.\n     */\n    function name() external view returns (string memory);\n\n    /**\n     * @dev Returns the token collection symbol.\n     */\n    function symbol() external view returns (string memory);\n\n    /**\n     * @dev Returns the Uniform Resource Identifier (URI) for `tokenId` token.\n     */\n    function tokenURI(uint256 tokenId) external view returns (string memory);\n}\n"}, "contracts/interfaces/IERC721Permit.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\n\nimport \"./IERC721.sol\";\n\n/// @title ERC721 with permit\n/// @notice Extension to ERC721 that includes a permit function for signature based approvals\ninterface IERC721Permit is IERC721 {\n    /// @notice The permit typehash used in the permit signature\n    /// @return The typehash for the permit\n    function PERMIT_TYPEHASH() external pure returns (bytes32);\n\n    /// @notice The domain separator used in the permit signature\n    /// @return The domain seperator used in encoding of permit signature\n    function DOMAIN_SEPARATOR() external view returns (bytes32);\n\n    /// @notice Approve of a specific token ID for spending by spender via signature\n    /// @param spender The account that is being approved\n    /// @param tokenId The ID of the token that is being approved for spending\n    /// @param deadline The deadline timestamp by which the call must be mined for the approve to\n    /// work\n    /// @param v Must produce valid secp256k1 signature from the holder along with `r` and `s`\n    /// @param r Must produce valid secp256k1 signature from the holder along with `v` and `s`\n    /// @param s Must produce valid secp256k1 signature from the holder along with `r` and `v`\n    function permit(\n        address spender,\n        uint256 tokenId,\n        uint256 deadline,\n        uint8 v,\n        bytes32 r,\n        bytes32 s\n    )\n        external\n        payable;\n}\n"}, "contracts/interfaces/INonfungiblePositionManager.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\npragma abicoder v2;\n\nimport { IERC721Metadata } from \"./IERC721Metadata.sol\";\nimport { IERC721Enumerable } from \"./IERC721Enumerable.sol\";\n\nimport { IERC721Permit } from \"./IERC721Permit.sol\";\nimport { IPoolInitializer } from \"./IPoolInitializer.sol\";\nimport { IPeripheryPayments } from \"./IPeripheryPayments.sol\";\nimport { IPeripheryImmutableState } from \"./IPeripheryImmutableState.sol\";\n\n/// @title Non-fungible token for positions\n/// @notice Wraps Uniswap V3 positions in a non-fungible token interface which allows for them to be\n/// transferred\n/// and authorized.\ninterface INonfungiblePositionManager is\n    IPoolInitializer,\n    IPeripheryPayments,\n    IPeripheryImmutableState,\n    IERC721Metadata,\n    IERC721Enumerable,\n    IERC721Permit\n{\n    /// @notice Emitted when liquidity is increased for a position NFT\n    /// @dev Also emitted when a token is minted\n    /// @param tokenId The ID of the token for which liquidity was increased\n    /// @param liquidity The amount by which liquidity for the NFT position was increased\n    /// @param amount0 The amount of token0 that was paid for the increase in liquidity\n    /// @param amount1 The amount of token1 that was paid for the increase in liquidity\n    event IncreaseLiquidity(\n        uint256 indexed tokenId, uint128 liquidity, uint256 amount0, uint256 amount1\n    );\n    /// @notice Emitted when liquidity is decreased for a position NFT\n    /// @param tokenId The ID of the token for which liquidity was decreased\n    /// @param liquidity The amount by which liquidity for the NFT position was decreased\n    /// @param amount0 The amount of token0 that was accounted for the decrease in liquidity\n    /// @param amount1 The amount of token1 that was accounted for the decrease in liquidity\n    event DecreaseLiquidity(\n        uint256 indexed tokenId, uint128 liquidity, uint256 amount0, uint256 amount1\n    );\n    /// @notice Emitted when tokens are collected for a position NFT\n    /// @dev The amounts reported may not be exactly equivalent to the amounts transferred, due to\n    /// rounding behavior\n    /// @param tokenId The ID of the token for which underlying tokens were collected\n    /// @param recipient The address of the account that received the collected tokens\n    /// @param amount0 The amount of token0 owed to the position that was collected\n    /// @param amount1 The amount of token1 owed to the position that was collected\n    event Collect(uint256 indexed tokenId, address recipient, uint256 amount0, uint256 amount1);\n\n    /// @notice Returns the position information associated with a given token ID.\n    /// @dev Throws if the token ID is not valid.\n    /// @param tokenId The ID of the token that represents the position\n    /// @return nonce The nonce for permits\n    /// @return operator The address that is approved for spending\n    /// @return token0 The address of the token0 for a specific pool\n    /// @return token1 The address of the token1 for a specific pool\n    /// @return fee The fee associated with the pool\n    /// @return tickLower The lower end of the tick range for the position\n    /// @return tickUpper The higher end of the tick range for the position\n    /// @return liquidity The liquidity of the position\n    /// @return feeGrowthInside0LastX128 The fee growth of token0 as of the last action on the\n    /// individual position\n    /// @return feeGrowthInside1LastX128 The fee growth of token1 as of the last action on the\n    /// individual position\n    /// @return tokensOwed0 The uncollected amount of token0 owed to the position as of the last\n    /// computation\n    /// @return tokensOwed1 The uncollected amount of token1 owed to the position as of the last\n    /// computation\n    function positions(uint256 tokenId)\n        external\n        view\n        returns (\n            uint96 nonce,\n            address operator,\n            address token0,\n            address token1,\n            uint24 fee,\n            int24 tickLower,\n            int24 tickUpper,\n            uint128 liquidity,\n            uint256 feeGrowthInside0LastX128,\n            uint256 feeGrowthInside1LastX128,\n            uint128 tokensOwed0,\n            uint128 tokensOwed1\n        );\n\n    struct MintParams {\n        address token0;\n        address token1;\n        uint24 fee;\n        int24 tickLower;\n        int24 tickUpper;\n        uint256 amount0Desired;\n        uint256 amount1Desired;\n        uint256 amount0Min;\n        uint256 amount1Min;\n        address recipient;\n        uint256 deadline;\n    }\n\n    /// @notice Creates a new position wrapped in a NFT\n    /// @dev Call this when the pool does exist and is initialized. Note that if the pool is created\n    /// but not initialized\n    /// a method does not exist, i.e. the pool is assumed to be initialized.\n    /// @param params The params necessary to mint a position, encoded as `MintParams` in calldata\n    /// @return tokenId The ID of the token that represents the minted position\n    /// @return liquidity The amount of liquidity for this position\n    /// @return amount0 The amount of token0\n    /// @return amount1 The amount of token1\n    function mint(MintParams calldata params)\n        external\n        payable\n        returns (uint256 tokenId, uint128 liquidity, uint256 amount0, uint256 amount1);\n\n    struct IncreaseLiquidityParams {\n        uint256 tokenId;\n        uint256 amount0Desired;\n        uint256 amount1Desired;\n        uint256 amount0Min;\n        uint256 amount1Min;\n        uint256 deadline;\n    }\n\n    /// @notice Increases the amount of liquidity in a position, with tokens paid by the\n    /// `msg.sender`\n    /// @param params tokenId The ID of the token for which liquidity is being increased,\n    /// amount0Desired The desired amount of token0 to be spent,\n    /// amount1Desired The desired amount of token1 to be spent,\n    /// amount0Min The minimum amount of token0 to spend, which serves as a slippage check,\n    /// amount1Min The minimum amount of token1 to spend, which serves as a slippage check,\n    /// deadline The time by which the transaction must be included to effect the change\n    /// @return liquidity The new liquidity amount as a result of the increase\n    /// @return amount0 The amount of token0 to acheive resulting liquidity\n    /// @return amount1 The amount of token1 to acheive resulting liquidity\n    function increaseLiquidity(IncreaseLiquidityParams calldata params)\n        external\n        payable\n        returns (uint128 liquidity, uint256 amount0, uint256 amount1);\n\n    struct DecreaseLiquidityParams {\n        uint256 tokenId;\n        uint128 liquidity;\n        uint256 amount0Min;\n        uint256 amount1Min;\n        uint256 deadline;\n    }\n\n    /// @notice Decreases the amount of liquidity in a position and accounts it to the position\n    /// @param params tokenId The ID of the token for which liquidity is being decreased,\n    /// amount The amount by which liquidity will be decreased,\n    /// amount0Min The minimum amount of token0 that should be accounted for the burned liquidity,\n    /// amount1Min The minimum amount of token1 that should be accounted for the burned liquidity,\n    /// deadline The time by which the transaction must be included to effect the change\n    /// @return amount0 The amount of token0 accounted to the position's tokens owed\n    /// @return amount1 The amount of token1 accounted to the position's tokens owed\n    function decreaseLiquidity(DecreaseLiquidityParams calldata params)\n        external\n        payable\n        returns (uint256 amount0, uint256 amount1);\n\n    struct CollectParams {\n        uint256 tokenId;\n        address recipient;\n        uint128 amount0Max;\n        uint128 amount1Max;\n    }\n\n    /// @notice Collects up to a maximum amount of fees owed to a specific position to the recipient\n    /// @param params tokenId The ID of the NFT for which tokens are being collected,\n    /// recipient The account that should receive the tokens,\n    /// amount0Max The maximum amount of token0 to collect,\n    /// amount1Max The maximum amount of token1 to collect\n    /// @return amount0 The amount of fees collected in token0\n    /// @return amount1 The amount of fees collected in token1\n    function collect(CollectParams calldata params)\n        external\n        payable\n        returns (uint256 amount0, uint256 amount1);\n\n    /// @notice Burns a token ID, which deletes it from the NFT contract. The token must have 0\n    /// liquidity and all tokens\n    /// must be collected first.\n    /// @param tokenId The ID of the token that is being burned\n    function burn(uint256 tokenId) external payable;\n}\n"}, "contracts/interfaces/IPeripheryImmutableState.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\n\n/// @title Immutable state\n/// @notice Functions that return immutable state of the router\ninterface IPeripheryImmutableState {\n    /// @return Returns the address of the Uniswap V3 factory\n    function factory() external view returns (address);\n\n    /// @return Returns the address of WETH9\n    function WETH9() external view returns (address);\n}\n"}, "contracts/interfaces/IPeripheryPayments.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\n\n/// @title Periphery Payments\n/// @notice Functions to ease deposits and withdrawals of ETH\ninterface IPeripheryPayments {\n    /// @notice Unwraps the contract's WETH9 balance and sends it to recipient as ETH.\n    /// @dev The amountMinimum parameter prevents malicious contracts from stealing WETH9 from\n    /// users.\n    /// @param amountMinimum The minimum amount of WETH9 to unwrap\n    /// @param recipient The address receiving ETH\n    function unwrapWETH9(uint256 amountMinimum, address recipient) external payable;\n\n    /// @notice Refunds any ETH balance held by this contract to the `msg.sender`\n    /// @dev Useful for bundling with mint or increase liquidity that uses ether, or exact output\n    /// swaps\n    /// that use ether for the input amount\n    function refundETH() external payable;\n\n    /// @notice Transfers the full amount of a token held by this contract to recipient\n    /// @dev The amountMinimum parameter prevents malicious contracts from stealing the token from\n    /// users\n    /// @param token The contract address of the token which will be transferred to `recipient`\n    /// @param amountMinimum The minimum amount of token required for a transfer\n    /// @param recipient The destination address of the token\n    function sweepToken(address token, uint256 amountMinimum, address recipient) external payable;\n}\n"}, "contracts/interfaces/IPoolInitializer.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\npragma abicoder v2;\n\n/// @title Creates and initializes V3 Pools\n/// @notice Provides a method for creating and initializing a pool, if necessary, for bundling with\n/// other methods that\n/// require the pool to exist.\ninterface IPoolInitializer {\n    /// @notice Creates a new pool if it does not exist, then initializes if not initialized\n    /// @dev This method can be bundled with others via IMulticall for the first action (e.g. mint)\n    /// performed against a pool\n    /// @param token0 The contract address of token0 of the pool\n    /// @param token1 The contract address of token1 of the pool\n    /// @param fee The fee amount of the v3 pool for the specified token pair\n    /// @param sqrtPriceX96 The initial square root price of the pool as a Q64.96 value\n    /// @return pool Returns the pool address based on the pair of tokens and fee, will return the\n    /// newly created pool address if necessary\n    function createAndInitializePoolIfNecessary(\n        address token0,\n        address token1,\n        uint24 fee,\n        uint160 sqrtPriceX96\n    )\n        external\n        payable\n        returns (address pool);\n}\n"}, "contracts/interfaces/ISwapRouter.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\npragma abicoder v2;\n\nimport { IUniswapV3SwapCallback } from \"./IUniswapV3SwapCallback.sol\";\n\n/// @title Router token swapping functionality\n/// @notice Functions for swapping tokens via Uniswap V3\ninterface ISwapRouter is IUniswapV3SwapCallback {\n    struct ExactInputSingleParams {\n        address tokenIn;\n        address tokenOut;\n        uint24 fee;\n        address recipient;\n        uint256 deadline;\n        uint256 amountIn;\n        uint256 amountOutMinimum;\n        uint160 sqrtPriceLimitX96;\n    }\n\n    /// @notice Swaps `amountIn` of one token for as much as possible of another token\n    /// @param params The parameters necessary for the swap, encoded as `ExactInputSingleParams` in\n    /// calldata\n    /// @return amountOut The amount of the received token\n    function exactInputSingle(ExactInputSingleParams calldata params)\n        external\n        payable\n        returns (uint256 amountOut);\n\n    struct ExactInputParams {\n        bytes path;\n        address recipient;\n        uint256 deadline;\n        uint256 amountIn;\n        uint256 amountOutMinimum;\n    }\n\n    /// @notice Swaps `amountIn` of one token for as much as possible of another along the specified\n    /// path\n    /// @param params The parameters necessary for the multi-hop swap, encoded as `ExactInputParams`\n    /// in calldata\n    /// @return amountOut The amount of the received token\n    function exactInput(ExactInputParams calldata params)\n        external\n        payable\n        returns (uint256 amountOut);\n\n    struct ExactOutputSingleParams {\n        address tokenIn;\n        address tokenOut;\n        uint24 fee;\n        address recipient;\n        uint256 deadline;\n        uint256 amountOut;\n        uint256 amountInMaximum;\n        uint160 sqrtPriceLimitX96;\n    }\n\n    /// @notice Swaps as little as possible of one token for `amountOut` of another token\n    /// @param params The parameters necessary for the swap, encoded as `ExactOutputSingleParams` in\n    /// calldata\n    /// @return amountIn The amount of the input token\n    function exactOutputSingle(ExactOutputSingleParams calldata params)\n        external\n        payable\n        returns (uint256 amountIn);\n\n    struct ExactOutputParams {\n        bytes path;\n        address recipient;\n        uint256 deadline;\n        uint256 amountOut;\n        uint256 amountInMaximum;\n    }\n\n    /// @notice Swaps as little as possible of one token for `amountOut` of another along the\n    /// specified path (reversed)\n    /// @param params The parameters necessary for the multi-hop swap, encoded as\n    /// `ExactOutputParams` in calldata\n    /// @return amountIn The amount of the input token\n    function exactOutput(ExactOutputParams calldata params)\n        external\n        payable\n        returns (uint256 amountIn);\n}\n"}, "contracts/interfaces/IUniswapV3Factory.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\n\n/// @title The interface for the Uniswap V3 Factory\n/// @notice The Uniswap V3 Factory facilitates creation of Uniswap V3 pools and control over the\n/// protocol fees\ninterface IUniswapV3Factory {\n    /// @notice Emitted when the owner of the factory is changed\n    /// @param oldOwner The owner before the owner was changed\n    /// @param newOwner The owner after the owner was changed\n    event OwnerChanged(address indexed oldOwner, address indexed newOwner);\n\n    /// @notice Emitted when a pool is created\n    /// @param token0 The first token of the pool by address sort order\n    /// @param token1 The second token of the pool by address sort order\n    /// @param fee The fee collected upon every swap in the pool, denominated in hundredths of a bip\n    /// @param tickSpacing The minimum number of ticks between initialized ticks\n    /// @param pool The address of the created pool\n    event PoolCreated(\n        address indexed token0,\n        address indexed token1,\n        uint24 indexed fee,\n        int24 tickSpacing,\n        address pool\n    );\n\n    /// @notice Emitted when a new fee amount is enabled for pool creation via the factory\n    /// @param fee The enabled fee, denominated in hundredths of a bip\n    /// @param tickSpacing The minimum number of ticks between initialized ticks for pools created\n    /// with the given fee\n    event FeeAmountEnabled(uint24 indexed fee, int24 indexed tickSpacing);\n\n    /// @notice Returns the current owner of the factory\n    /// @dev Can be changed by the current owner via setOwner\n    /// @return The address of the factory owner\n    function owner() external view returns (address);\n\n    /// @notice Returns the tick spacing for a given fee amount, if enabled, or 0 if not enabled\n    /// @dev A fee amount can never be removed, so this value should be hard coded or cached in the\n    /// calling context\n    /// @param fee The enabled fee, denominated in hundredths of a bip. Returns 0 in case of\n    /// unenabled fee\n    /// @return The tick spacing\n    function feeAmountTickSpacing(uint24 fee) external view returns (int24);\n\n    /// @notice Returns the pool address for a given pair of tokens and a fee, or address 0 if it\n    /// does not exist\n    /// @dev tokenA and tokenB may be passed in either token0/token1 or token1/token0 order\n    /// @param tokenA The contract address of either token0 or token1\n    /// @param tokenB The contract address of the other token\n    /// @param fee The fee collected upon every swap in the pool, denominated in hundredths of a bip\n    /// @return pool The pool address\n    function getPool(\n        address tokenA,\n        address tokenB,\n        uint24 fee\n    )\n        external\n        view\n        returns (address pool);\n\n    /// @notice Creates a pool for the given two tokens and fee\n    /// @param tokenA One of the two tokens in the desired pool\n    /// @param tokenB The other of the two tokens in the desired pool\n    /// @param fee The desired fee for the pool\n    /// @dev tokenA and tokenB may be passed in either order: token0/token1 or token1/token0.\n    /// tickSpacing is retrieved\n    /// from the fee. The call will revert if the pool already exists, the fee is invalid, or the\n    /// token arguments\n    /// are invalid.\n    /// @return pool The address of the newly created pool\n    function createPool(\n        address tokenA,\n        address tokenB,\n        uint24 fee\n    )\n        external\n        returns (address pool);\n\n    /// @notice Updates the owner of the factory\n    /// @dev Must be called by the current owner\n    /// @param _owner The new owner of the factory\n    function setOwner(address _owner) external;\n\n    /// @notice Enables a fee amount with the given tickSpacing\n    /// @dev Fee amounts may never be removed once enabled\n    /// @param fee The fee amount to enable, denominated in hundredths of a bip (i.e. 1e-6)\n    /// @param tickSpacing The spacing between ticks to be enforced for all pools created with the\n    /// given fee amount\n    function enableFeeAmount(uint24 fee, int24 tickSpacing) external;\n}\n"}, "contracts/interfaces/IUniswapV3SwapCallback.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\n\n/// @title Callback for IUniswapV3PoolActions#swap\n/// @notice Any contract that calls IUniswapV3PoolActions#swap must implement this interface\ninterface IUniswapV3SwapCallback {\n    /// @notice Called to `msg.sender` after executing a swap via IUniswapV3Pool#swap.\n    /// @dev In the implementation you must pay the pool tokens owed for the swap.\n    /// The caller of this method must be checked to be a UniswapV3Pool deployed by the canonical\n    /// UniswapV3Factory.\n    /// amount0Delta and amount1Delta can both be 0 if no tokens were swapped.\n    /// @param amount0Delta The amount of token0 that was sent (negative) or must be received\n    /// (positive) by the pool by\n    /// the end of the swap. If positive, the callback must send that amount of token0 to the pool.\n    /// @param amount1Delta The amount of token1 that was sent (negative) or must be received\n    /// (positive) by the pool by\n    /// the end of the swap. If positive, the callback must send that amount of token1 to the pool.\n    /// @param data Any data passed through by the caller via the IUniswapV3PoolActions#swap call\n    function uniswapV3SwapCallback(\n        int256 amount0Delta,\n        int256 amount1Delta,\n        bytes calldata data\n    )\n        external;\n}\n"}, "contracts/interfaces/IWETH9.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\n\nimport \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\n\n/// @title Interface for WETH9\ninterface IWETH9 is IERC20 {\n    /// @notice Deposit ether to get wrapped ether\n    function deposit() external payable;\n\n    /// @notice Withdraw wrapped ether to get ether\n    function withdraw(uint256) external;\n}\n"}, "contracts/LaunchpadToken.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\nimport { ERC20 } from \"@openzeppelin/contracts/token/ERC20/ERC20.sol\";\nimport { Ownable } from \"@openzeppelin/contracts/access/Ownable.sol\";\n\n/**\n * @title LaunchpadToken\n * @notice ERC20 token deployed through the Moonbags launchpad\n */\ncontract LaunchpadToken is ERC20, Ownable {\n    error TokenTransfersBlocked();\n\n    bool public isListed;\n    uint8 private _decimals;\n    string private _tokenURI;\n    address public tokenLock;\n\n    /**\n     * @notice Constructor for LaunchpadToken\n     * @param name_ Token name\n     * @param symbol_ Token symbol\n     * @param decimals_ Token decimals\n     * @param uri_ Token metadata URI\n     */\n    constructor(\n        string memory name_,\n        string memory symbol_,\n        uint8 decimals_,\n        string memory uri_\n    )\n        ERC20(name_, symbol_)\n        Ownable(msg.sender)\n    {\n        _decimals = decimals_;\n        _tokenURI = uri_;\n        isListed = false; // Initially set to false\n    }\n\n    /**\n     * @notice Mint new tokens to a specified address\n     * @param to Address to receive the minted tokens\n     * @param amount Amount of tokens to mint\n     */\n    function mint(address to, uint256 amount) external onlyOwner {\n        _mint(to, amount);\n    }\n\n    /**\n     * @notice Set the listed status of the token\n     * @param isListed_ Boolean indicating if the token is listed\n     */\n    function setListed(bool isListed_) external onlyOwner {\n        isListed = isListed_;\n    }\n\n    /**\n     * @notice Set the address of the token lock contract\n     * @param _tokenLock Address of the token lock contract\n     */\n    function setTokenLockContract(address _tokenLock) external onlyOwner {\n        tokenLock = _tokenLock;\n    }\n\n    /**\n     * @notice Burn tokens from a specified address\n     * @param from Address from which tokens will be burned\n     * @param amount Amount of tokens to burn\n     */\n    function burn(address from, uint256 amount) external onlyOwner {\n        _burn(from, amount);\n    }\n\n    /**\n     * @dev Internal function to handle token transfers with transfer restrictions\n     * @param from Sender address\n     * @param to Recipient address\n     * @param value Amount of tokens to transfer\n     */\n    function _update(address from, address to, uint256 value) internal override {\n        if (\n            !isListed && from != address(0) && to != address(0) && from != owner() && to != owner()\n                && from != tokenLock\n        ) {\n            revert TokenTransfersBlocked();\n        }\n        super._update(from, to, value);\n    }\n\n    /**\n     * @notice Returns the number of decimals used for the token\n     * @return uint8 Number of decimals\n     */\n    function decimals() public view override returns (uint8) {\n        return _decimals;\n    }\n\n    /**\n     * @notice Returns the token metadata URI\n     * @return string Token URI\n     */\n    function tokenURI() public view returns (string memory) {\n        return _tokenURI;\n    }\n\n    /**\n     * @notice Set the token metadata URI\n     * @param uri_ New token URI\n     */\n    function setTokenURI(string memory uri_) external onlyOwner {\n        _tokenURI = uri_;\n    }\n}\n"}, "contracts/libraries/BondingCurveMath.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\n/**\n * @title BondingCurveMath\n * @notice Mathematical functions for bonding curve calculations\n * @dev Implements constant product formula: x * y = k\n */\nlibrary BondingCurveMath {\n    error InsufficientReserves();\n    error InvalidInput();\n    error MathOverflow();\n\n    /**\n     * @notice Calculate the cost in base currency to get exact amount of tokens\n     * @param baseReserves Current base currency reserves\n     * @param tokenReserves Current token reserves\n     * @param tokenAmountOut Exact amount of tokens desired\n     * @return baseCost Amount of base currency needed\n     */\n    function calculateBaseCostForExactTokens(\n        uint256 baseReserves,\n        uint256 tokenReserves,\n        uint256 tokenAmountOut\n    )\n        internal\n        pure\n        returns (uint256 baseCost)\n    {\n        if (tokenAmountOut == 0) return 0;\n        if (tokenAmountOut >= tokenReserves) revert InsufficientReserves();\n\n        uint256 remainingTokenReserves = tokenReserves - tokenAmountOut;\n        uint256 newBaseReserves = (baseReserves * tokenReserves) / remainingTokenReserves;\n\n        if (newBaseReserves <= baseReserves) revert InvalidInput();\n\n        baseCost = newBaseReserves - baseReserves;\n    }\n\n    /**\n     * @notice Calculate tokens received for exact base currency input\n     * @param baseReserves Current base currency reserves\n     * @param tokenReserves Current token reserves\n     * @param baseAmountIn Exact amount of base currency to spend\n     * @return tokenAmountOut Amount of tokens that will be received\n     */\n    function calculateTokensForExactBase(\n        uint256 baseReserves,\n        uint256 tokenReserves,\n        uint256 baseAmountIn\n    )\n        internal\n        pure\n        returns (uint256 tokenAmountOut)\n    {\n        if (baseAmountIn == 0) return 0;\n\n        uint256 newBaseReserves = baseReserves + baseAmountIn;\n        uint256 newTokenReserves = (baseReserves * tokenReserves) / newBaseReserves;\n\n        if (newTokenReserves >= tokenReserves) revert InvalidInput();\n\n        tokenAmountOut = tokenReserves - newTokenReserves;\n    }\n\n    /**\n     * @notice Calculate base currency received for exact token input (selling)\n     * @param baseReserves Current base currency reserves\n     * @param tokenReserves Current token reserves\n     * @param tokenAmountIn Exact amount of tokens to sell\n     * @return baseAmountOut Amount of base currency that will be received\n     */\n    function calculateBaseForExactTokens(\n        uint256 baseReserves,\n        uint256 tokenReserves,\n        uint256 tokenAmountIn\n    )\n        internal\n        pure\n        returns (uint256 baseAmountOut)\n    {\n        if (tokenAmountIn == 0) return 0;\n\n        uint256 newTokenReserves = tokenReserves + tokenAmountIn;\n        uint256 newBaseReserves = (baseReserves * tokenReserves) / newTokenReserves;\n\n        if (newBaseReserves >= baseReserves) revert InvalidInput();\n\n        baseAmountOut = baseReserves - newBaseReserves;\n    }\n}\n"}, "contracts/libraries/uniswap/FullMath.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\n/// @title Contains 512-bit math functions\n/// @notice Facilitates multiplication and division that can have overflow of an intermediate value\n/// without any loss of precision\n/// @dev <PERSON>les \"phantom overflow\" i.e., allows multiplication and division where an intermediate\n/// value overflows 256 bits\nlibrary FullMath {\n    /// @notice Calculates floor(a×b÷denominator) with full precision. Throws if result overflows\n    /// a uint256 or denominator == 0\n    /// @param a The multiplicand\n    /// @param b The multiplier\n    /// @param denominator The divisor\n    /// @return result The 256-bit result\n    /// @dev Credit to Remco Bloemen under MIT license https://xn--2-umb.com/21/muldiv\n    function mulDiv(\n        uint256 a,\n        uint256 b,\n        uint256 denominator\n    )\n        internal\n        pure\n        returns (uint256 result)\n    {\n        // 512-bit multiply [prod1 prod0] = a * b\n        // Compute the product mod 2**256 and mod 2**256 - 1\n        // then use the Chinese Remainder Theorem to reconstruct\n        // the 512 bit result. The result is stored in two 256\n        // variables such that product = prod1 * 2**256 + prod0\n        uint256 prod0; // Least significant 256 bits of the product\n        uint256 prod1; // Most significant 256 bits of the product\n        assembly {\n            let mm := mulmod(a, b, not(0))\n            prod0 := mul(a, b)\n            prod1 := sub(sub(mm, prod0), lt(mm, prod0))\n        }\n\n        // Handle non-overflow cases, 256 by 256 division\n        if (prod1 == 0) {\n            require(denominator > 0);\n            assembly {\n                result := div(prod0, denominator)\n            }\n            return result;\n        }\n\n        // Make sure the result is less than 2**256.\n        // Also prevents denominator == 0\n        require(denominator > prod1);\n\n        ///////////////////////////////////////////////\n        // 512 by 256 division.\n        ///////////////////////////////////////////////\n\n        // Make division exact by subtracting the remainder from [prod1 prod0]\n        // Compute remainder using mulmod\n        uint256 remainder;\n        assembly {\n            remainder := mulmod(a, b, denominator)\n        }\n        // Subtract 256 bit number from 512 bit number\n        assembly {\n            prod1 := sub(prod1, gt(remainder, prod0))\n            prod0 := sub(prod0, remainder)\n        }\n\n        // Factor powers of two out of denominator\n        // Compute largest power of two divisor of denominator.\n        // Always >= 1.\n        uint256 twos = denominator & (~denominator + 1);\n        // Divide denominator by power of two\n        assembly {\n            denominator := div(denominator, twos)\n        }\n\n        // Divide [prod1 prod0] by the factors of two\n        assembly {\n            prod0 := div(prod0, twos)\n        }\n        // Shift in bits from prod1 into prod0. For this we need\n        // to flip `twos` such that it is 2**256 / twos.\n        // If twos is zero, then it becomes one\n        assembly {\n            twos := add(div(sub(0, twos), twos), 1)\n        }\n        prod0 |= prod1 * twos;\n\n        // Invert denominator mod 2**256\n        // Now that denominator is an odd number, it has an inverse\n        // modulo 2**256 such that denominator * inv = 1 mod 2**256.\n        // Compute the inverse by starting with a seed that is correct\n        // correct for four bits. That is, denominator * inv = 1 mod 2**4\n        uint256 inv = (3 * denominator) ^ 2;\n        // Now use Newton-Raphson iteration to improve the precision.\n        // Thanks to Hensel's lifting lemma, this also works in modular\n        // arithmetic, doubling the correct bits in each step.\n        inv *= 2 - denominator * inv; // inverse mod 2**8\n        inv *= 2 - denominator * inv; // inverse mod 2**16\n        inv *= 2 - denominator * inv; // inverse mod 2**32\n        inv *= 2 - denominator * inv; // inverse mod 2**64\n        inv *= 2 - denominator * inv; // inverse mod 2**128\n        inv *= 2 - denominator * inv; // inverse mod 2**256\n\n        // Because the division is now exact we can divide by multiplying\n        // with the modular inverse of denominator. This will give us the\n        // correct result modulo 2**256. Since the precoditions guarantee\n        // that the outcome is less than 2**256, this is the final result.\n        // We don't need to compute the high bits of the result and prod1\n        // is no longer required.\n        result = prod0 * inv;\n        return result;\n    }\n\n    /// @notice Calculates ceil(a×b÷denominator) with full precision. Throws if result overflows a\n    /// uint256 or denominator == 0\n    /// @param a The multiplicand\n    /// @param b The multiplier\n    /// @param denominator The divisor\n    /// @return result The 256-bit result\n    function mulDivRoundingUp(\n        uint256 a,\n        uint256 b,\n        uint256 denominator\n    )\n        internal\n        pure\n        returns (uint256 result)\n    {\n        result = mulDiv(a, b, denominator);\n        if (mulmod(a, b, denominator) > 0) {\n            require(result < type(uint256).max);\n            result++;\n        }\n    }\n}\n"}, "contracts/libraries/uniswap/TickMath.sol": {"content": "// SPDX-License-Identifier: GPL-2.0-or-later\npragma solidity ^0.8.23;\n\n/// @title Math library for computing sqrt prices from ticks and vice versa\n/// @notice Computes sqrt price for ticks of size 1.0001, i.e. sqrt(1.0001^tick) as fixed point\n/// Q64.96 numbers. Supports\n/// prices between 2**-128 and 2**128\nlibrary TickMath {\n    /// @dev The minimum tick that may be passed to #getSqrtRatioAtTick computed from log base\n    /// 1.0001 of 2**-128\n    int24 internal constant MIN_TICK = -887_272;\n    /// @dev The maximum tick that may be passed to #getSqrtRatioAtTick computed from log base\n    /// 1.0001 of 2**128\n    int24 internal constant MAX_TICK = -MIN_TICK;\n\n    /// @dev The minimum value that can be returned from #getSqrtRatioAtTick. Equivalent to\n    /// getSqrtRatioAtTick(MIN_TICK)\n    uint160 internal constant MIN_SQRT_RATIO = 4_295_128_739;\n    /// @dev The maximum value that can be returned from #getSqrtRatioAtTick. Equivalent to\n    /// getSqrtRatioAtTick(MAX_TICK)\n    uint160 internal constant MAX_SQRT_RATIO =\n        1_461_446_703_485_210_103_287_273_052_203_988_822_378_723_970_342;\n\n    function getSqrtRatioAtTicks(\n        int24 tickLower,\n        int24 tickUpper\n    )\n        public\n        pure\n        returns (uint160 sqrtPriceAX96, uint160 sqrtPriceBX96)\n    {\n        sqrtPriceAX96 = getSqrtRatioAtTick(tickLower);\n        sqrtPriceBX96 = getSqrtRatioAtTick(tickUpper);\n    }\n\n    /// @notice Calculates sqrt(1.0001^tick) * 2^96\n    /// @dev Throws if |tick| > max tick\n    /// @param tick The input tick for the above formula\n    /// @return sqrtPriceX96 A Fixed point Q64.96 number representing the sqrt of the ratio of the\n    /// two assets (token1/token0)\n    /// at the given tick\n    function getSqrtRatioAtTick(int24 tick) public pure returns (uint160 sqrtPriceX96) {\n        uint256 absTick = tick < 0 ? uint256(-int256(tick)) : uint256(int256(tick));\n        require(absTick <= uint256(int256(MAX_TICK)), \"T\");\n\n        uint256 ratio = absTick & 0x1 != 0\n            ? 0xfffcb933bd6fad37aa2d162d1a594001\n            : 0x100000000000000000000000000000000;\n        if (absTick & 0x2 != 0) ratio = (ratio * 0xfff97272373d413259a46990580e213a) >> 128;\n        if (absTick & 0x4 != 0) ratio = (ratio * 0xfff2e50f5f656932ef12357cf3c7fdcc) >> 128;\n        if (absTick & 0x8 != 0) ratio = (ratio * 0xffe5caca7e10e4e61c3624eaa0941cd0) >> 128;\n        if (absTick & 0x10 != 0) ratio = (ratio * 0xffcb9843d60f6159c9db58835c926644) >> 128;\n        if (absTick & 0x20 != 0) ratio = (ratio * 0xff973b41fa98c081472e6896dfb254c0) >> 128;\n        if (absTick & 0x40 != 0) ratio = (ratio * 0xff2ea16466c96a3843ec78b326b52861) >> 128;\n        if (absTick & 0x80 != 0) ratio = (ratio * 0xfe5dee046a99a2a811c461f1969c3053) >> 128;\n        if (absTick & 0x100 != 0) ratio = (ratio * 0xfcbe86c7900a88aedcffc83b479aa3a4) >> 128;\n        if (absTick & 0x200 != 0) ratio = (ratio * 0xf987a7253ac413176f2b074cf7815e54) >> 128;\n        if (absTick & 0x400 != 0) ratio = (ratio * 0xf3392b0822b70005940c7a398e4b70f3) >> 128;\n        if (absTick & 0x800 != 0) ratio = (ratio * 0xe7159475a2c29b7443b29c7fa6e889d9) >> 128;\n        if (absTick & 0x1000 != 0) ratio = (ratio * 0xd097f3bdfd2022b8845ad8f792aa5825) >> 128;\n        if (absTick & 0x2000 != 0) ratio = (ratio * 0xa9f746462d870fdf8a65dc1f90e061e5) >> 128;\n        if (absTick & 0x4000 != 0) ratio = (ratio * 0x70d869a156d2a1b890bb3df62baf32f7) >> 128;\n        if (absTick & 0x8000 != 0) ratio = (ratio * 0x31be135f97d08fd981231505542fcfa6) >> 128;\n        if (absTick & 0x10000 != 0) ratio = (ratio * 0x9aa508b5b7a84e1c677de54f3e99bc9) >> 128;\n        if (absTick & 0x20000 != 0) ratio = (ratio * 0x5d6af8dedb81196699c329225ee604) >> 128;\n        if (absTick & 0x40000 != 0) ratio = (ratio * 0x2216e584f5fa1ea926041bedfe98) >> 128;\n        if (absTick & 0x80000 != 0) ratio = (ratio * 0x48a170391f7dc42444e8fa2) >> 128;\n\n        if (tick > 0) ratio = type(uint256).max / ratio;\n\n        // this divides by 1<<32 rounding up to go from a Q128.128 to a Q128.96.\n        // we then downcast because we know the result always fits within 160 bits due to our tick\n        // input constraint\n        // we round up in the division so getTickAtSqrtRatio of the output price is always\n        // consistent\n        sqrtPriceX96 = uint160((ratio >> 32) + (ratio % (1 << 32) == 0 ? 0 : 1));\n    }\n\n    /// @notice Calculates the greatest tick value such that getRatioAtTick(tick) <= ratio\n    /// @dev Throws in case sqrtPriceX96 < MIN_SQRT_RATIO, as MIN_SQRT_RATIO is the lowest value\n    /// getRatioAtTick may\n    /// ever return.\n    /// @param sqrtPriceX96 The sqrt ratio for which to compute the tick as a Q64.96\n    /// @return tick The greatest tick for which the ratio is less than or equal to the input ratio\n    function getTickAtSqrtRatio(uint160 sqrtPriceX96) public pure returns (int24 tick) {\n        // second inequality must be < because the price can never reach the price at the max tick\n        require(sqrtPriceX96 >= MIN_SQRT_RATIO && sqrtPriceX96 < MAX_SQRT_RATIO, \"R\");\n        uint256 ratio = uint256(sqrtPriceX96) << 32;\n\n        uint256 r = ratio;\n        uint256 msb = 0;\n\n        assembly {\n            let f := shl(7, gt(r, 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF))\n            msb := or(msb, f)\n            r := shr(f, r)\n        }\n        assembly {\n            let f := shl(6, gt(r, 0xFFFFFFFFFFFFFFFF))\n            msb := or(msb, f)\n            r := shr(f, r)\n        }\n        assembly {\n            let f := shl(5, gt(r, 0xFFFFFFFF))\n            msb := or(msb, f)\n            r := shr(f, r)\n        }\n        assembly {\n            let f := shl(4, gt(r, 0xFFFF))\n            msb := or(msb, f)\n            r := shr(f, r)\n        }\n        assembly {\n            let f := shl(3, gt(r, 0xFF))\n            msb := or(msb, f)\n            r := shr(f, r)\n        }\n        assembly {\n            let f := shl(2, gt(r, 0xF))\n            msb := or(msb, f)\n            r := shr(f, r)\n        }\n        assembly {\n            let f := shl(1, gt(r, 0x3))\n            msb := or(msb, f)\n            r := shr(f, r)\n        }\n        assembly {\n            let f := gt(r, 0x1)\n            msb := or(msb, f)\n        }\n\n        if (msb >= 128) r = ratio >> (msb - 127);\n        else r = ratio << (127 - msb);\n\n        int256 log_2 = (int256(msb) - 128) << 64;\n\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(63, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(62, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(61, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(60, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(59, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(58, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(57, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(56, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(55, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(54, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(53, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(52, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(51, f))\n            r := shr(f, r)\n        }\n        assembly {\n            r := shr(127, mul(r, r))\n            let f := shr(128, r)\n            log_2 := or(log_2, shl(50, f))\n        }\n\n        int256 log_sqrt10001 = log_2 * 255_738_958_999_603_826_347_141; // 128.128 number\n\n        int24 tickLow =\n            int24((log_sqrt10001 - 3_402_992_956_809_132_418_596_140_100_660_247_210) >> 128);\n        int24 tickHi =\n            int24((log_sqrt10001 + 291_339_464_771_989_622_907_027_621_153_398_088_495) >> 128);\n\n        tick = tickLow == tickHi\n            ? tickLow\n            : getSqrtRatioAtTick(tickHi) <= sqrtPriceX96 ? tickHi : tickLow;\n    }\n}\n"}, "contracts/MoonbagsLaunchpad.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\nimport \"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\";\nimport \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\nimport \"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\";\nimport \"@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\";\nimport \"./interfaces/INonfungiblePositionManager.sol\";\nimport \"./MoonbagsStake.sol\";\nimport \"./libraries/uniswap/TickMath.sol\";\nimport \"./libraries/uniswap/FullMath.sol\";\nimport \"./LaunchpadToken.sol\";\nimport \"./TokenLock.sol\";\nimport \"./libraries/BondingCurveMath.sol\";\nimport \"./interfaces/IWETH9.sol\";\n\n/**\n * @title Moonbags Launchpad\n */\ncontract MoonbagsLaunchpad is\n    Initializable,\n    ReentrancyGuardUpgradeable,\n    OwnableUpgradeable,\n    IERC721Receiver\n{\n    using SafeERC20 for IERC20;\n    using BondingCurveMath for uint256;\n\n    // Constants\n    uint256 public constant VERSION = 1;\n    uint256 public constant FEE_DENOMINATOR = 10_000; // 100% = 10000\n    uint256 public constant DEFAULT_THRESHOLD = 0.03 ether; // 3 SUI\n    uint256 public constant MINIMUM_THRESHOLD = 0.02 ether; // 2 SUI\n    uint256 public constant MIN_LOCK_DURATION = 1 hours;\n    uint256 public constant ONE_CHECKPOINT_TIMESTAMP = 1 seconds;\n    uint256 public constant DISTRIBUTE_FEE_LOCK_DURATION = 5 minutes;\n\n    uint24 public constant FEE_LOW = 500; // 0.05%\n    uint24 public constant FEE_MEDIUM = 3000; // 0.3%\n    uint24 public constant FEE_HIGH = 10_000; // 1%\n    uint24 public constant DEFAULT_FEE_TIER = FEE_MEDIUM; // Use 0.3% as default (most common)\n    uint256 public constant PRICE_SCALE_192 = 2 ** 192;\n    uint256 public constant POOL_CREATION_FEE = 0.001 ether; // 0.01 SUI\n\n    uint256 public constant DEFAULT_PLATFORM_FEE = 100; // 1%\n    uint256 public constant DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES = 8_000_000 * 10 ** 6; // 8M\n    // tokens (6 decimals)\n    uint256 public constant DEFAULT_REMAIN_TOKEN_RESERVES = 2_000_000 * 10 ** 6; // 2M tokens (6\n    // decimals)\n    uint8 public constant DEFAULT_TOKEN_DECIMALS = 6;\n    uint16 public constant DEFAULT_PLATFORM_FEE_WITHDRAW = 1500; // 15%\n    uint16 public constant DEFAULT_CREATOR_FEE_WITHDRAW = 3000; // 30%\n    uint16 public constant DEFAULT_STAKE_FEE_WITHDRAW = 2500; // 25%\n    uint16 public constant DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW = 2000; // 20%\n\n    // address public constant DEAD_ADDRESS = ******************************************;\n    address public constant PLATFORM_TOKEN_BUYER = ******************************************;\n\n    uint256 public constant MAX_URI_LENGTH = 300;\n    uint256 public constant MAX_DESCRIPTION_LENGTH = 1000;\n    uint256 public constant MAX_SOCIAL_LENGTH = 500;\n\n    struct Configuration {\n        address admin;\n        address treasury;\n        address feePlatformRecipient;\n        uint256 platformFee;\n        uint256 initialVirtualTokenReserves;\n        uint256 remainTokenReserves;\n        uint8 tokenDecimals;\n        uint16 initPlatformFeeWithdraw; // 15% to platform\n        uint16 initCreatorFeeWithdraw; // 30% to creator\n        uint16 initStakeFeeWithdraw; // 25% to stakers\n        uint16 initPlatformStakeFeeWithdraw; // 20% to platform stakers\n        address platformTokenAddress;\n    }\n\n    struct Pool {\n        uint256 realHypeReserves;\n        uint256 realTokenReserves;\n        uint256 virtualTokenReserves;\n        uint256 virtualHypeReserves;\n        uint256 remainTokenReserves;\n        uint256 virtualRemainTokenReserves;\n        uint256 feeRecipient;\n        bool isCompleted;\n        uint256 threshold;\n        uint16 platformFeeWithdraw;\n        uint16 creatorFeeWithdraw;\n        uint16 stakeFeeWithdraw;\n        uint16 platformStakeFeeWithdraw;\n        uint256 creationTimestamp;\n        uint256 feeDistributionUnlockTime;\n    }\n\n    struct ThresholdConfig {\n        uint256 threshold;\n    }\n\n    Configuration public config;\n    ThresholdConfig public thresholdConfig;\n    TokenLock public tokenLock;\n    MoonbagsStake public moonbagsStake;\n    mapping(address => Pool) public pools;\n    mapping(address => uint256) public tokenToPositionId;\n\n    // HyperSwap V3 Integration Variables\n    INonfungiblePositionManager public nonfungiblePositionManager;\n    address public weth9;\n    uint24 public activeFeeTier;\n\n    event ConfigurationUpdated(\n        uint256 newPlatformFee,\n        uint256 newInitialVirtualTokenReserves,\n        uint256 newRemainTokenReserves,\n        uint8 newTokenDecimals,\n        uint16 newInitPlatformFeeWithdraw,\n        uint16 newInitCreatorFeeWithdraw,\n        uint16 newInitStakeFeeWithdraw,\n        uint16 newInitPlatformStakeFeeWithdraw,\n        address newPlatformTokenAddress,\n        uint256 timestamp\n    );\n\n    event TokenCreated(\n        address indexed token,\n        address indexed creator,\n        string name,\n        string symbol,\n        string uri,\n        string description,\n        string twitter,\n        string telegram,\n        string website,\n        uint256 virtualHypeReserves,\n        uint256 virtualTokenReserves,\n        uint256 realHypeReserves,\n        uint256 realTokenReserves,\n        uint16 platformFeeWithdraw,\n        uint16 creatorFeeWithdraw,\n        uint16 stakeFeeWithdraw,\n        uint16 platformStakeFeeWithdraw,\n        uint256 threshold,\n        uint256 timestamp\n    );\n\n    event Trade(\n        address indexed token,\n        address indexed user,\n        bool indexed isBuy,\n        uint256 hypeAmount,\n        uint256 tokenAmount,\n        uint256 virtualHypeReserves,\n        uint256 virtualTokenReserves,\n        uint256 realHypeReserves,\n        uint256 realTokenReserves,\n        uint256 fee,\n        uint256 timestamp\n    );\n\n    event TokenLockContractUpdated(address indexed oldTokenLock, address indexed newTokenLock);\n    event MoonbagsStakeUpdated(address indexed oldMoonbagsStake, address indexed newMoonbagsStake);\n\n    event PoolCompleted(address indexed token, string lp, uint256 timestamp);\n\n    event V3PoolCreated(address indexed token, address indexed v3Pool, uint256 timestamp);\n\n    // Custom errors\n    error InvalidInput();\n    error InsufficientAmount();\n    error PoolAlreadyCompleted();\n    error PoolNotCompleted();\n    error TokenNotExists();\n    error Unauthorized();\n    error InvalidConfiguration();\n    error InsufficientTokenReserves();\n    error InsufficientHypeReserves();\n    error LPValueDecreased();\n    error NoPositionFound();\n    error InvalidDistributionTime();\n    error InvalidWithdrawalAmount();\n    error NotEnoughThreshold();\n\n    /**\n     * @notice Initialize the contract\n     * @param _nonfungiblePositionManager Position Manager address\n     * @param _weth9 Weth9 address\n     * @param _poolFee Pool fee (e.g., 3000 for 0.3%)\n     * @param _platformTokenAddress Platform token address\n     * @param _moonbagsStake MoonbagsStake contract address\n     */\n    function initialize(\n        address _nonfungiblePositionManager,\n        address _weth9,\n        uint24 _poolFee,\n        address _platformTokenAddress,\n        address _moonbagsStake,\n        address _tokenLock\n    )\n        public\n        initializer\n    {\n        if (\n            _nonfungiblePositionManager == address(0) || _weth9 == address(0)\n                || _platformTokenAddress == address(0) || _moonbagsStake == address(0)\n                || _tokenLock == address(0)\n        ) {\n            revert InvalidInput();\n        }\n\n        __ReentrancyGuard_init();\n        __Ownable_init(msg.sender);\n\n        config = Configuration({\n            admin: msg.sender,\n            treasury: msg.sender,\n            feePlatformRecipient: msg.sender,\n            platformTokenAddress: _platformTokenAddress,\n            platformFee: DEFAULT_PLATFORM_FEE,\n            initialVirtualTokenReserves: DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES,\n            remainTokenReserves: DEFAULT_REMAIN_TOKEN_RESERVES,\n            tokenDecimals: DEFAULT_TOKEN_DECIMALS,\n            initPlatformFeeWithdraw: DEFAULT_PLATFORM_FEE_WITHDRAW,\n            initCreatorFeeWithdraw: DEFAULT_CREATOR_FEE_WITHDRAW,\n            initStakeFeeWithdraw: DEFAULT_STAKE_FEE_WITHDRAW,\n            initPlatformStakeFeeWithdraw: DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW\n        });\n\n        nonfungiblePositionManager = INonfungiblePositionManager(_nonfungiblePositionManager);\n        weth9 = _weth9;\n        activeFeeTier = _poolFee;\n        moonbagsStake = MoonbagsStake(_moonbagsStake);\n        tokenLock = TokenLock(_tokenLock);\n\n        emit ConfigurationUpdated(\n            DEFAULT_PLATFORM_FEE,\n            DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES,\n            DEFAULT_REMAIN_TOKEN_RESERVES,\n            DEFAULT_TOKEN_DECIMALS,\n            DEFAULT_PLATFORM_FEE_WITHDRAW,\n            DEFAULT_CREATOR_FEE_WITHDRAW,\n            DEFAULT_STAKE_FEE_WITHDRAW,\n            DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW,\n            _platformTokenAddress,\n            block.timestamp\n        );\n    }\n\n    /**\n     * @notice Create a new pool with bonding curve\n     * @param name Token name\n     * @param symbol Token symbol\n     * @param uri Token metadata URI (max 300 characters)\n     * @param description Token description (max 1000 characters)\n     * @param twitter Twitter handle (max 500 characters)\n     * @param telegram Telegram link (max 500 characters)\n     * @param website Website URL (max 500 characters)\n     * @param customThreshold Custom threshold (0 for default)\n     */\n    function createPool(\n        string memory name,\n        string memory symbol,\n        string memory uri,\n        string memory description,\n        string memory twitter,\n        string memory telegram,\n        string memory website,\n        uint256 customThreshold\n    )\n        external\n        payable\n        nonReentrant\n        returns (address tokenAddress)\n    {\n        if (msg.value < POOL_CREATION_FEE) revert InvalidInput();\n\n        tokenAddress = _createPoolInternal(\n            name, symbol, uri, description, twitter, telegram, website, customThreshold\n        );\n\n        payable(msg.sender).transfer(msg.value - POOL_CREATION_FEE);\n        payable(config.feePlatformRecipient).transfer(POOL_CREATION_FEE);\n\n        return tokenAddress;\n    }\n\n    /**\n     * @notice Create a new pool with bonding curve and lock the first buy\n     * @param name Token name\n     * @param symbol Token symbol\n     * @param uri Token metadata URI (max 300 characters)\n     * @param description Token description (max 1000 characters)\n     * @param twitter Twitter handle (max 500 characters)\n     * @param telegram Telegram link (max 500 characters)\n     * @param website Website URL (max 500 characters)\n     * @param customThreshold Custom threshold (0 for default)\n     * @param amountOut Amount of tokens to buy and lock\n     * @param lockDuration Duration to lock the purchased tokens (minimum 1 hour)\n     */\n    function createAndLockFirstBuy(\n        string memory name,\n        string memory symbol,\n        string memory uri,\n        string memory description,\n        string memory twitter,\n        string memory telegram,\n        string memory website,\n        uint256 customThreshold,\n        uint256 amountOut,\n        uint256 lockDuration\n    )\n        external\n        payable\n        nonReentrant\n        returns (address tokenAddress)\n    {\n        // Enhanced validation based on Sui implementation\n        if (address(tokenLock) == address(0)) revert InvalidConfiguration();\n        if (lockDuration < MIN_LOCK_DURATION) revert InvalidInput();\n        if (msg.value < POOL_CREATION_FEE) revert InvalidInput();\n\n        tokenAddress = _createPoolInternal(\n            name, symbol, uri, description, twitter, telegram, website, customThreshold\n        );\n\n        uint256 amountIn = msg.value - POOL_CREATION_FEE;\n\n        if (amountIn > 0) {\n            _buyDirectAndLock(tokenAddress, amountIn, amountOut, lockDuration, msg.sender);\n        }\n\n        payable(config.feePlatformRecipient).transfer(POOL_CREATION_FEE);\n\n        return tokenAddress;\n    }\n\n    function _createPoolInternal(\n        string memory name,\n        string memory symbol,\n        string memory uri,\n        string memory description,\n        string memory twitter,\n        string memory telegram,\n        string memory website,\n        uint256 customThreshold\n    )\n        internal\n        returns (address tokenAddress)\n    {\n        if (bytes(name).length == 0 || bytes(symbol).length == 0) revert InvalidInput();\n        if (bytes(uri).length > MAX_URI_LENGTH) revert InvalidInput();\n        if (bytes(description).length > MAX_DESCRIPTION_LENGTH) revert InvalidInput();\n        if (bytes(twitter).length > MAX_SOCIAL_LENGTH) revert InvalidInput();\n        if (bytes(telegram).length > MAX_SOCIAL_LENGTH) revert InvalidInput();\n        if (bytes(website).length > MAX_SOCIAL_LENGTH) revert InvalidInput();\n\n        uint256 threshold = customThreshold == 0 ? DEFAULT_THRESHOLD : customThreshold;\n        if (threshold < MINIMUM_THRESHOLD) revert InvalidInput();\n\n        uint256 initialVirtualHypeReserves = calculateInitialVirtualHypeReserves(threshold);\n        uint256 actualVirtualTokenReserves = calculateActualVirtualTokenReserves();\n        uint256 virtualRemainTokenReserves = calculateVirtualRemainTokenReserves();\n\n        LaunchpadToken token = new LaunchpadToken(name, symbol, config.tokenDecimals, uri);\n        tokenAddress = address(token);\n\n        if (address(tokenLock) != address(0)) {\n            token.setTokenLockContract(address(tokenLock));\n        }\n\n        pools[tokenAddress] = Pool({\n            realHypeReserves: 0,\n            realTokenReserves: config.initialVirtualTokenReserves,\n            virtualTokenReserves: actualVirtualTokenReserves,\n            virtualHypeReserves: initialVirtualHypeReserves,\n            remainTokenReserves: config.remainTokenReserves,\n            virtualRemainTokenReserves: virtualRemainTokenReserves,\n            feeRecipient: 0,\n            isCompleted: false,\n            threshold: threshold,\n            platformFeeWithdraw: config.initPlatformFeeWithdraw,\n            creatorFeeWithdraw: config.initCreatorFeeWithdraw,\n            stakeFeeWithdraw: config.initStakeFeeWithdraw,\n            platformStakeFeeWithdraw: config.initPlatformStakeFeeWithdraw,\n            creationTimestamp: block.timestamp,\n            feeDistributionUnlockTime: block.timestamp + DISTRIBUTE_FEE_LOCK_DURATION\n        });\n\n        Pool storage pool = pools[tokenAddress];\n        emit TokenCreated(\n            tokenAddress,\n            msg.sender,\n            name,\n            symbol,\n            uri,\n            description,\n            twitter,\n            telegram,\n            website,\n            pool.virtualHypeReserves,\n            pool.virtualTokenReserves,\n            pool.realHypeReserves,\n            pool.realTokenReserves,\n            pool.platformFeeWithdraw,\n            pool.creatorFeeWithdraw,\n            pool.stakeFeeWithdraw,\n            pool.platformStakeFeeWithdraw,\n            threshold,\n            block.timestamp\n        );\n\n        _initializeStakingPools(tokenAddress, msg.sender);\n\n        return tokenAddress;\n    }\n\n    /**\n     * @notice Initialize staking pools for a newly created token\n     * @param tokenAddress The address of the newly created token\n     * @param creator The address of the token creator\n     */\n    function _initializeStakingPools(address tokenAddress, address creator) internal {\n        if (address(moonbagsStake) == address(0)) revert InvalidInput();\n\n        moonbagsStake.initializeStakingPool(tokenAddress);\n        moonbagsStake.initializeCreatorPool(tokenAddress, creator);\n    }\n\n    /**\n     * @notice Buy exact amount in and lock tokens for specified duration\n     * @param token Token address\n     * @param amountIn Exact amount of ETH to spend\n     * @param amountOutMin Minimum amount of tokens to receive\n     */\n    function buyExactIn(\n        address token,\n        uint256 amountIn,\n        uint256 amountOutMin\n    )\n        external\n        payable\n        nonReentrant\n        returns (uint256 tokenAmountOut)\n    {\n        if (!isToken(token)) revert TokenNotExists();\n        if (address(tokenLock) == address(0)) revert InvalidConfiguration();\n        if (amountIn == 0) revert InvalidInput();\n        if (msg.value < amountIn) revert InsufficientAmount();\n\n        Pool storage pool = pools[token];\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\n\n        uint256 tokenReservesInPool = pool.virtualTokenReserves > pool.virtualRemainTokenReserves\n            ? pool.virtualTokenReserves - pool.virtualRemainTokenReserves\n            : 0;\n\n        (uint256 actualAmountOut, uint256 hypeAmountInSwap, uint256 fee) =\n        _calculateBuyExactInAmounts(\n            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn, tokenReservesInPool\n        );\n\n        if (actualAmountOut < amountOutMin) revert InvalidInput();\n        if (msg.value < hypeAmountInSwap + fee) revert InsufficientAmount();\n\n        _executeSwap(pool, 0, hypeAmountInSwap, actualAmountOut, 0);\n        pool.virtualTokenReserves = pool.virtualTokenReserves - actualAmountOut;\n        pool.feeRecipient += fee;\n\n        emit Trade(\n            token,\n            msg.sender,\n            true,\n            hypeAmountInSwap,\n            actualAmountOut,\n            pool.virtualHypeReserves,\n            pool.virtualTokenReserves,\n            pool.realHypeReserves,\n            pool.realTokenReserves,\n            fee,\n            block.timestamp\n        );\n\n        if (actualAmountOut == tokenReservesInPool) {\n            _completePool(token, pool);\n        }\n\n        if (block.timestamp < pool.creationTimestamp + ONE_CHECKPOINT_TIMESTAMP) {\n            LaunchpadToken(token).mint(address(this), actualAmountOut);\n            IERC20(token).safeIncreaseAllowance(address(tokenLock), actualAmountOut);\n            uint256 lockEndTime = block.timestamp + 1 hours;\n            tokenLock.createLock(token, actualAmountOut, lockEndTime, msg.sender);\n        } else {\n            LaunchpadToken(token).mint(msg.sender, actualAmountOut);\n        }\n\n        uint256 totalUsed = hypeAmountInSwap + fee;\n        uint256 refundAmount = msg.value - totalUsed;\n        if (refundAmount > 0) {\n            payable(msg.sender).transfer(refundAmount);\n        }\n\n        return actualAmountOut;\n    }\n\n    function _calculateBuyExactInAmounts(\n        uint256 virtualHypeReserves,\n        uint256 virtualTokenReserves,\n        uint256 amountIn,\n        uint256 tokenReservesInPool\n    )\n        internal\n        view\n        returns (uint256 actualAmountOut, uint256 hypeAmountInSwap, uint256 fee)\n    {\n        uint256 initialTokenOut = BondingCurveMath.calculateTokensForExactBase(\n            virtualHypeReserves, virtualTokenReserves, amountIn\n        );\n        if (initialTokenOut > tokenReservesInPool) {\n            actualAmountOut = tokenReservesInPool;\n            hypeAmountInSwap = BondingCurveMath.calculateBaseCostForExactTokens(\n                virtualHypeReserves, virtualTokenReserves, actualAmountOut\n            ) + 1;\n        } else {\n            actualAmountOut = initialTokenOut;\n            hypeAmountInSwap = amountIn;\n        }\n\n        fee = (hypeAmountInSwap * config.platformFee) / FEE_DENOMINATOR;\n    }\n\n    // /**\n    //  * @notice Buy exact amount out with automatic token locking\n    //  * @param token Token address\n    //  * @param tokenAmountOut Amount of tokens to buy and lock\n    //  * @return actualAmountOut Amount of tokens purchased and locked\n    //  */\n    // function buyExactOut(\n    //     address token,\n    //     uint256 tokenAmountOut\n    // )\n    //     external\n    //     payable\n    //     nonReentrant\n    //     returns (uint256 actualAmountOut)\n    // {\n    //     if (!isToken(token)) revert TokenNotExists();\n    //     if (address(tokenLock) == address(0)) revert InvalidConfiguration();\n\n    //     Pool storage pool = pools[token];\n    //     if (pool.isCompleted) revert PoolAlreadyCompleted();\n    //     if (tokenAmountOut == 0) revert InvalidInput();\n\n    //     uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;\n    //     uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves\n    //         ? pool.virtualTokenReserves - virtualRemainTokenReserves\n    //         : 0;\n\n    //     uint256 hypeCostBeforeFee;\n    //     uint256 fee;\n    //     (actualAmountOut, hypeCostBeforeFee, fee) = _calculateBuyExactOutAmounts(\n    //         pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut,\n    // tokenReservesInPool\n    //     );\n\n    //     uint256 totalCost = hypeCostBeforeFee + fee;\n    //     if (msg.value < totalCost) revert InsufficientAmount();\n\n    //     _executeSwap(pool, 0, msg.value - fee, actualAmountOut, msg.value - totalCost);\n\n    //     pool.feeRecipient += fee;\n    //     pool.virtualTokenReserves = pool.virtualTokenReserves - actualAmountOut;\n\n    //     LaunchpadToken(token).mint(msg.sender, actualAmountOut);\n\n    //     _handleFeesAndRefund(fee, msg.value - totalCost);\n\n    //     emit Trade(\n    //         token,\n    //         msg.sender,\n    //         true,\n    //         hypeCostBeforeFee,\n    //         actualAmountOut,\n    //         pool.virtualHypeReserves,\n    //         pool.virtualTokenReserves,\n    //         pool.realHypeReserves,\n    //         pool.realTokenReserves,\n    //         fee,\n    //         block.timestamp\n    //     );\n\n    //     if (actualAmountOut == tokenReservesInPool) {\n    //         _completePool(token, pool);\n    //     }\n\n    //     return actualAmountOut;\n    // }\n\n    function _calculateBuyExactOutAmounts(\n        uint256 virtualHypeReserves,\n        uint256 virtualTokenReserves,\n        uint256 tokenAmountOut,\n        uint256 tokenReservesInPool\n    )\n        internal\n        view\n        returns (uint256 actualAmountOut, uint256 hypeCostBeforeFee, uint256 fee)\n    {\n        actualAmountOut =\n            tokenAmountOut > tokenReservesInPool ? tokenReservesInPool : tokenAmountOut;\n        hypeCostBeforeFee = BondingCurveMath.calculateBaseCostForExactTokens(\n            virtualHypeReserves, virtualTokenReserves, actualAmountOut\n        ) + 1;\n\n        fee = (hypeCostBeforeFee * config.platformFee) / FEE_DENOMINATOR;\n    }\n\n    function sellExactIn(\n        address token,\n        uint256 tokenAmountIn,\n        uint256 amountOutMin\n    )\n        external\n        nonReentrant\n        returns (uint256 hypeAmountOut)\n    {\n        if (!isToken(token)) revert TokenNotExists();\n        Pool storage pool = pools[token];\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\n        if (tokenAmountIn == 0) revert InvalidInput();\n\n        LaunchpadToken(token).burn(msg.sender, tokenAmountIn);\n\n        uint256 netAmount;\n        uint256 fee;\n        (hypeAmountOut, netAmount, fee) = _calculateSellExactInAmounts(\n            pool.virtualHypeReserves,\n            pool.virtualTokenReserves,\n            tokenAmountIn,\n            pool.realHypeReserves\n        );\n\n        if (netAmount < amountOutMin) revert InsufficientAmount();\n\n        _executeSwap(pool, tokenAmountIn, 0, 0, hypeAmountOut);\n        pool.virtualHypeReserves = pool.virtualHypeReserves - hypeAmountOut;\n        pool.feeRecipient += fee;\n\n        payable(msg.sender).transfer(netAmount);\n\n        emit Trade(\n            token,\n            msg.sender,\n            false,\n            hypeAmountOut,\n            tokenAmountIn,\n            pool.virtualHypeReserves,\n            pool.virtualTokenReserves,\n            pool.realHypeReserves,\n            pool.realTokenReserves,\n            fee,\n            block.timestamp\n        );\n\n        return netAmount;\n    }\n\n    function _calculateSellExactInAmounts(\n        uint256 virtualHypeReserves,\n        uint256 virtualTokenReserves,\n        uint256 tokenAmountIn,\n        uint256 realHypeReserves\n    )\n        internal\n        view\n        returns (uint256 hypeAmountOut, uint256 netAmount, uint256 fee)\n    {\n        hypeAmountOut = BondingCurveMath.calculateBaseForExactTokens(\n            virtualHypeReserves, virtualTokenReserves, tokenAmountIn\n        );\n        if (hypeAmountOut > realHypeReserves) {\n            hypeAmountOut = realHypeReserves;\n        }\n        fee = (hypeAmountOut * config.platformFee) / FEE_DENOMINATOR;\n        netAmount = hypeAmountOut - fee;\n    }\n\n    function _executeSwap(\n        Pool storage pool,\n        uint256 tokenAmountIn,\n        uint256 hypeAmountIn,\n        uint256 tokenAmountOut,\n        uint256 hypeAmountOut\n    )\n        internal\n        returns (uint256 actualTokenOut, uint256 actualHypeOut)\n    {\n        uint256 beforeVirtualTokenReserves = pool.virtualTokenReserves;\n        uint256 beforeVirtualHypeReserves = pool.virtualHypeReserves;\n\n        if (tokenAmountIn == 0 && hypeAmountIn == 0) revert InvalidInput();\n\n        pool.virtualTokenReserves = pool.virtualTokenReserves + tokenAmountIn;\n        pool.virtualHypeReserves = pool.virtualHypeReserves + hypeAmountIn;\n\n        if (tokenAmountIn > 0) {\n            pool.virtualTokenReserves = pool.virtualTokenReserves - tokenAmountOut;\n        }\n        if (hypeAmountIn > 0) {\n            pool.virtualHypeReserves = pool.virtualHypeReserves - hypeAmountOut;\n        }\n\n        if (\n            beforeVirtualTokenReserves * beforeVirtualHypeReserves\n                > pool.virtualTokenReserves * pool.virtualHypeReserves\n        ) revert LPValueDecreased();\n\n        pool.realTokenReserves += tokenAmountIn;\n        pool.realHypeReserves += hypeAmountIn;\n\n        if (pool.realTokenReserves < tokenAmountOut) revert InsufficientTokenReserves();\n        if (pool.realHypeReserves < hypeAmountOut) revert InsufficientHypeReserves();\n\n        pool.realTokenReserves -= tokenAmountOut;\n        pool.realHypeReserves -= hypeAmountOut;\n\n        return (tokenAmountOut, hypeAmountOut);\n    }\n\n    /**\n     * @notice Complete pool when all tokens are sold\n     */\n    function _completePool(address token, Pool storage pool) internal {\n        pool.isCompleted = true;\n        _migrateToHyperSwapV3(token, pool);\n    }\n\n    function createThresholdConfig(uint256 _threshold) external onlyOwner {\n        thresholdConfig = ThresholdConfig(_threshold);\n    }\n\n    function earlyCompletePool(address token) external onlyOwner nonReentrant {\n        if (!isToken(token)) revert TokenNotExists();\n\n        Pool storage pool = pools[token];\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\n\n        pool.isCompleted = true;\n\n        uint256 realHypeReserves = pool.realHypeReserves;\n        uint256 realTokenReserves = pool.realTokenReserves;\n        uint256 remainTokenReserves = pool.remainTokenReserves;\n\n        if (realHypeReserves < thresholdConfig.threshold) {\n            revert NotEnoughThreshold();\n        }\n\n        if (thresholdConfig.threshold > 0) {\n            payable(config.admin).transfer(thresholdConfig.threshold);\n        }\n\n        if (config.remainTokenReserves > 0) {\n            LaunchpadToken(token).mint(config.admin, config.remainTokenReserves);\n        }\n\n        uint256 remainingHype = realHypeReserves - thresholdConfig.threshold;\n        if (remainingHype > 0) {\n            payable(msg.sender).transfer(remainingHype);\n        }\n\n        uint256 remainingTokens =\n            realTokenReserves + remainTokenReserves - config.remainTokenReserves;\n        if (remainingTokens > 0) {\n            LaunchpadToken(token).mint(msg.sender, remainingTokens);\n        }\n\n        pool.realHypeReserves = 0;\n        pool.realTokenReserves = 0;\n\n        emit PoolCompleted(token, \"0x0\", block.timestamp);\n    }\n\n    /**\n     * @notice Distribute fees for a token\n     * @param token Token address\n     */\n    function _distributeFees(address token) internal {\n        if (!isToken(token)) revert TokenNotExists();\n        Pool storage pool = pools[token];\n\n        if (block.timestamp < pool.feeDistributionUnlockTime) {\n            revert InvalidDistributionTime();\n        }\n\n        uint256 totalFees = pool.feeRecipient;\n        if (totalFees == 0) return;\n\n        if (address(this).balance < totalFees) {\n            return;\n        }\n\n        uint256 platformShare = (totalFees * pool.platformFeeWithdraw) / FEE_DENOMINATOR;\n        uint256 creatorShare = (totalFees * pool.creatorFeeWithdraw) / FEE_DENOMINATOR;\n        uint256 stakeShare = (totalFees * pool.stakeFeeWithdraw) / FEE_DENOMINATOR;\n        uint256 platformStakeShare = (totalFees * pool.platformStakeFeeWithdraw) / FEE_DENOMINATOR;\n\n        uint256 totalDistribution = platformShare + creatorShare + stakeShare + platformStakeShare;\n        if (totalDistribution > totalFees) {\n            revert InvalidWithdrawalAmount();\n        }\n\n        if (platformShare > 0) {\n            payable(config.feePlatformRecipient).transfer(platformShare);\n        }\n\n        if (stakeShare > 0) {\n            moonbagsStake.updateRewardIndex{ value: stakeShare }(token);\n        }\n\n        if (creatorShare > 0) {\n            moonbagsStake.depositCreatorPool{ value: creatorShare }(token);\n        }\n\n        if (platformStakeShare > 0) {\n            moonbagsStake.updateRewardIndex{ value: platformStakeShare }(\n                config.platformTokenAddress\n            );\n        }\n\n        pool.feeDistributionUnlockTime = block.timestamp + DISTRIBUTE_FEE_LOCK_DURATION;\n\n        uint256 remainingBalance = totalFees - totalDistribution;\n        pool.feeRecipient -= totalFees;\n\n        if (remainingBalance > 0) {\n            payable(PLATFORM_TOKEN_BUYER).transfer(remainingBalance);\n        }\n    }\n\n    /**\n     * @notice Distribute accumulated bonding curve fees for a token\n     * @param token Token address\n     */\n    function distributeBondingCurveFees(address token) external nonReentrant {\n        _distributeFees(token);\n    }\n\n    /**\n     * @notice Collect all fees from a HyperSwap V3 position\n     * @param token Token address associated with the position\n     * @return amount0 The amount of fees collected in token0\n     * @return amount1 The amount of fees collected in token1\n     */\n    function collectHyperSwapFees(address token)\n        external\n        nonReentrant\n        returns (uint256 amount0, uint256 amount1)\n    {\n        if (!isToken(token)) revert TokenNotExists();\n\n        Pool storage pool = pools[token];\n        if (!pool.isCompleted) revert PoolNotCompleted();\n\n        uint256 tokenId = tokenToPositionId[token];\n        if (tokenId == 0) revert NoPositionFound();\n\n        INonfungiblePositionManager.CollectParams memory params = INonfungiblePositionManager\n            .CollectParams({\n            tokenId: tokenId,\n            recipient: address(this),\n            amount0Max: type(uint128).max,\n            amount1Max: type(uint128).max\n        });\n\n        (amount0, amount1) = nonfungiblePositionManager.collect(params);\n        (,, address token0,,,,,,,,,) = nonfungiblePositionManager.positions(tokenId);\n        (uint256 bondedTokenAmount, uint256 whypeTokenAmount) =\n            token0 == weth9 ? (amount1, amount0) : (amount0, amount1);\n\n        if (bondedTokenAmount > 0) {\n            IERC20(token).safeTransfer(config.treasury, bondedTokenAmount);\n        }\n\n        if (whypeTokenAmount > 0) {\n            IWETH9(weth9).withdraw(whypeTokenAmount);\n            pool.feeRecipient += whypeTokenAmount;\n        }\n\n        _distributeFees(token);\n\n        return (amount0, amount1);\n    }\n\n    /**\n     * @notice Update configuration (only owner)\n     */\n    function updateConfiguration(\n        uint256 _platformFee,\n        uint256 _initialVirtualTokenReserves,\n        uint256 _remainTokenReserves,\n        uint8 _tokenDecimals,\n        uint16 _initPlatformFeeWithdraw,\n        uint16 _initCreatorFeeWithdraw,\n        uint16 _initStakeFeeWithdraw,\n        uint16 _initPlatformStakeFeeWithdraw,\n        address _platformTokenAddress\n    )\n        external\n        onlyOwner\n    {\n        if (\n            _initPlatformFeeWithdraw + _initCreatorFeeWithdraw + _initStakeFeeWithdraw\n                + _initPlatformStakeFeeWithdraw > FEE_DENOMINATOR\n        ) revert InvalidInput();\n\n        config.platformFee = _platformFee;\n        config.initialVirtualTokenReserves = _initialVirtualTokenReserves;\n        config.remainTokenReserves = _remainTokenReserves;\n        config.tokenDecimals = _tokenDecimals;\n        config.initPlatformFeeWithdraw = _initPlatformFeeWithdraw;\n        config.initCreatorFeeWithdraw = _initCreatorFeeWithdraw;\n        config.initStakeFeeWithdraw = _initStakeFeeWithdraw;\n        config.initPlatformStakeFeeWithdraw = _initPlatformStakeFeeWithdraw;\n        config.platformTokenAddress = _platformTokenAddress;\n\n        emit ConfigurationUpdated(\n            _platformFee,\n            _initialVirtualTokenReserves,\n            _remainTokenReserves,\n            _tokenDecimals,\n            _initPlatformFeeWithdraw,\n            _initCreatorFeeWithdraw,\n            _initStakeFeeWithdraw,\n            _initPlatformStakeFeeWithdraw,\n            _platformTokenAddress,\n            block.timestamp\n        );\n    }\n\n    /**\n     * @notice skim\n     * @param token Token address to skim\n     */\n    function skim(address token) external onlyOwner {\n        if (token == address(0)) revert InvalidInput();\n        if (!isToken(token)) revert TokenNotExists();\n        Pool storage pool = pools[token];\n\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\n\n        (uint256 realHypeReserves, uint256 realTokenReserves) =\n            (pool.realHypeReserves, pool.realTokenReserves);\n\n        if (realHypeReserves > 0) {\n            payable(msg.sender).transfer(realHypeReserves);\n            pool.realHypeReserves = 0;\n        }\n\n        if (realTokenReserves > 0) {\n            LaunchpadToken(token).mint(msg.sender, realTokenReserves);\n            pool.realTokenReserves = 0;\n        }\n    }\n\n    function updateThresholdConfig(uint256 newThreshold) external onlyOwner {\n        thresholdConfig.threshold = newThreshold;\n    }\n\n    /**\n     * @notice Update the initial virtual token reserves (only owner)\n     * @param _initialVirtualTokenReserves New initial virtual token reserves\n     */\n    function updateInitialVirtualTokenReserves(uint256 _initialVirtualTokenReserves)\n        external\n        onlyOwner\n    {\n        config.initialVirtualTokenReserves = _initialVirtualTokenReserves;\n    }\n\n    /**\n     * @notice Update fee recipients (only owner)\n     */\n    function updateFeeRecipients(\n        address _treasury,\n        address _feePlatformRecipient\n    )\n        external\n        onlyOwner\n    {\n        config.treasury = _treasury;\n        config.feePlatformRecipient = _feePlatformRecipient;\n    }\n\n    /**\n     * @notice Update the initial withdraw fees (only owner)\n     */\n    function updateConfigWithdrawFee(\n        uint16 _newInitPlatformFeeWithdraw,\n        uint16 _newInitCreatorFeeWithdraw,\n        uint16 _newInitStakeFeeWithdraw,\n        uint16 _newInitPlatformStakeFeeWithdraw\n    )\n        external\n        onlyOwner\n    {\n        config.initPlatformFeeWithdraw = _newInitPlatformFeeWithdraw;\n        config.initCreatorFeeWithdraw = _newInitCreatorFeeWithdraw;\n        config.initStakeFeeWithdraw = _newInitStakeFeeWithdraw;\n        config.initPlatformStakeFeeWithdraw = _newInitPlatformStakeFeeWithdraw;\n    }\n\n    /**\n     * @notice Set TokenLock contract (only owner)\n     * @param _tokenLock Address of the new TokenLock contract\n     */\n    function setTokenLock(address _tokenLock) external onlyOwner {\n        if (_tokenLock == address(0)) revert InvalidInput();\n\n        address oldTokenLock = address(tokenLock);\n        tokenLock = TokenLock(_tokenLock);\n\n        emit TokenLockContractUpdated(oldTokenLock, _tokenLock);\n    }\n\n    /**\n     * @notice Set the MoonbagsStake contract address (only owner)\n     * @param _moonbagsStake Address of the MoonbagsStake contract\n     */\n    function setMoonbagsStake(address _moonbagsStake) external onlyOwner {\n        if (_moonbagsStake == address(0)) revert InvalidInput();\n\n        address oldMoonbagsStake = address(moonbagsStake);\n        moonbagsStake = MoonbagsStake(_moonbagsStake);\n\n        emit MoonbagsStakeUpdated(oldMoonbagsStake, _moonbagsStake);\n    }\n\n    /**\n     * @notice Update the active fee tier for new pools (only owner)\n     * @param newFeeTier The new fee tier (500, 3000, or 10000)\n     */\n    function updateActiveFeeTier(uint24 newFeeTier) external onlyOwner {\n        _getTickSpacingInternal(newFeeTier); // Will revert if invalid\n        activeFeeTier = newFeeTier;\n    }\n\n    function calculateInitialVirtualHypeReserves(uint256 threshold) public view returns (uint256) {\n        uint256 remainTokenReserves = config.remainTokenReserves;\n        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;\n\n        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();\n\n        return\n            (threshold * remainTokenReserves) / (initialVirtualTokenReserves - remainTokenReserves);\n    }\n\n    function calculateVirtualRemainTokenReserves() public view returns (uint256) {\n        uint256 remainTokenReserves = config.remainTokenReserves;\n        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;\n\n        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();\n\n        return (remainTokenReserves * initialVirtualTokenReserves)\n            / (initialVirtualTokenReserves - remainTokenReserves);\n    }\n\n    function calculateActualVirtualTokenReserves() public view returns (uint256) {\n        uint256 remainTokenReserves = config.remainTokenReserves;\n        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;\n\n        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();\n\n        return (initialVirtualTokenReserves * initialVirtualTokenReserves)\n            / (initialVirtualTokenReserves - remainTokenReserves);\n    }\n\n    function getPool(address token) external view returns (Pool memory) {\n        return pools[token];\n    }\n\n    /**\n     * @notice Check if a token exists (has been created through the launchpad)\n     * @param token The token address to check\n     * @return exists True if the token exists\n     */\n    function isToken(address token) public view returns (bool exists) {\n        return pools[token].creationTimestamp != 0;\n    }\n\n    function estimateBuyExactInTokens(\n        address token,\n        uint256 amountIn\n    )\n        external\n        view\n        returns (uint256)\n    {\n        if (!isToken(token)) return 0;\n        Pool memory pool = pools[token];\n        if (pool.isCompleted) return 0;\n\n        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;\n        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves\n            ? pool.virtualTokenReserves - virtualRemainTokenReserves\n            : 0;\n\n        uint256 initialTokenOut = BondingCurveMath.calculateTokensForExactBase(\n            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn\n        );\n\n        return initialTokenOut > tokenReservesInPool ? tokenReservesInPool : initialTokenOut;\n    }\n\n    function estimateBuyExactInCost(\n        address token,\n        uint256 amountIn\n    )\n        external\n        view\n        returns (uint256 totalCost)\n    {\n        if (!isToken(token)) return 0;\n        Pool memory pool = pools[token];\n        if (pool.isCompleted) return 0;\n\n        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;\n        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves\n            ? pool.virtualTokenReserves - virtualRemainTokenReserves\n            : 0;\n\n        (, uint256 hypeAmountInSwap, uint256 fee) = _calculateBuyExactInAmounts(\n            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn, tokenReservesInPool\n        );\n\n        return hypeAmountInSwap + fee;\n    }\n\n    function estimateBuyExactOutCost(\n        address token,\n        uint256 tokenAmountOut\n    )\n        external\n        view\n        returns (uint256)\n    {\n        if (!isToken(token)) return 0;\n        Pool memory pool = pools[token];\n        if (pool.isCompleted) return 0;\n\n        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;\n        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves\n            ? pool.virtualTokenReserves - virtualRemainTokenReserves\n            : 0;\n\n        (, uint256 hypeCostBeforeFee, uint256 fee) = _calculateBuyExactOutAmounts(\n            pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut, tokenReservesInPool\n        );\n\n        return hypeCostBeforeFee + fee;\n    }\n\n    function estimateSellTokens(\n        address token,\n        uint256 tokenAmountIn\n    )\n        external\n        view\n        returns (uint256)\n    {\n        if (!isToken(token)) return 0;\n        Pool memory pool = pools[token];\n        if (pool.isCompleted) return 0;\n\n        (, uint256 netAmount,) = _calculateSellExactInAmounts(\n            pool.virtualHypeReserves,\n            pool.virtualTokenReserves,\n            tokenAmountIn,\n            pool.realHypeReserves\n        );\n\n        return netAmount;\n    }\n\n    function _buyDirectAndLock(\n        address token,\n        uint256 amountIn,\n        uint256 amountOut,\n        uint256 lockDuration,\n        address recipient\n    )\n        internal\n        returns (uint256 tokenAmountOut)\n    {\n        Pool storage pool = pools[token];\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\n        if (amountOut == 0) revert InvalidInput();\n        if (lockDuration < MIN_LOCK_DURATION) revert InvalidInput();\n\n        uint256 tokenReservesInPool = pool.virtualTokenReserves - pool.virtualRemainTokenReserves;\n        tokenAmountOut = amountOut > tokenReservesInPool ? tokenReservesInPool : amountOut;\n\n        uint256 amountInSwap = BondingCurveMath.calculateBaseCostForExactTokens(\n            pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut\n        ) + 1;\n\n        uint256 swapFee = (amountInSwap * config.platformFee) / FEE_DENOMINATOR;\n        uint256 totalSwapCost = amountInSwap + swapFee;\n\n        if (amountIn < totalSwapCost) revert InsufficientAmount();\n\n        _executeSwap(pool, 0, amountInSwap, tokenAmountOut, 0);\n        pool.virtualTokenReserves = pool.virtualTokenReserves - tokenAmountOut;\n        pool.feeRecipient += swapFee;\n\n        LaunchpadToken(token).mint(address(this), tokenAmountOut);\n        IERC20(token).safeIncreaseAllowance(address(tokenLock), tokenAmountOut);\n        uint256 endTime = block.timestamp + lockDuration;\n        tokenLock.createLock(token, tokenAmountOut, endTime, recipient);\n\n        uint256 refundAmount = amountIn - totalSwapCost;\n        if (refundAmount > 0) {\n            payable(msg.sender).transfer(refundAmount);\n        }\n\n        emit Trade(\n            token,\n            msg.sender,\n            true,\n            amountInSwap,\n            tokenAmountOut,\n            pool.virtualHypeReserves,\n            pool.virtualTokenReserves,\n            pool.realHypeReserves,\n            pool.realTokenReserves,\n            swapFee,\n            block.timestamp\n        );\n\n        // Check if pool should be completed\n        if (tokenAmountOut == tokenReservesInPool) {\n            _completePool(token, pool);\n        }\n\n        return tokenAmountOut;\n    }\n\n    /**\n     * @notice Migrate liquidity to HyperSwap V3 pool using Uniswap V3 standard parameters\n     * @param token The token address\n     * @param pool The pool data\n     */\n    function _migrateToHyperSwapV3(address token, Pool storage pool) internal virtual {\n        (uint256 tokenAmount, uint256 hypeAmount) =\n            (pool.realTokenReserves + pool.remainTokenReserves, pool.realHypeReserves);\n        pool.realHypeReserves = 0;\n        pool.realTokenReserves = 0;\n        pool.remainTokenReserves = 0;\n\n        (address token0, address token1) = token < weth9 ? (token, weth9) : (weth9, token);\n        (uint256 amount0Desired, uint256 amount1Desired) =\n            token < weth9 ? (tokenAmount, hypeAmount) : (hypeAmount, tokenAmount);\n\n        uint160 sqrtPriceX96 = _calculateInitialSqrtPrice(amount0Desired, amount1Desired);\n\n        address v3Pool = nonfungiblePositionManager.createAndInitializePoolIfNecessary(\n            token0, token1, activeFeeTier, sqrtPriceX96\n        );\n\n        emit V3PoolCreated(token, v3Pool, block.timestamp);\n\n        LaunchpadToken(token).setListed(true);\n        LaunchpadToken(token).mint(address(this), tokenAmount);\n\n        IWETH9(weth9).deposit{ value: hypeAmount }();\n        IERC20(weth9).safeIncreaseAllowance(address(nonfungiblePositionManager), hypeAmount);\n        IERC20(token).safeIncreaseAllowance(address(nonfungiblePositionManager), tokenAmount);\n\n        (uint256 tokenId,,,) = _mintV3Position(token0, token1, amount0Desired, amount1Desired);\n\n        tokenToPositionId[token] = tokenId;\n    }\n\n    /**\n     * @notice Mint V3 liquidity position\n     * @dev Uses standard fee tiers and full-range tick calculations based on tick spacing\n     */\n    function _mintV3Position(\n        address token0,\n        address token1,\n        uint256 amount0Desired,\n        uint256 amount1Desired\n    )\n        internal\n        returns (uint256 tokenId, uint128 liquidity, uint256 amount0, uint256 amount1)\n    {\n        uint24 feeTier = activeFeeTier;\n        int24 tickSpacing = _getTickSpacingInternal(feeTier);\n\n        int24 tickLower = (TickMath.MIN_TICK / tickSpacing) * tickSpacing;\n        int24 tickUpper = (TickMath.MAX_TICK / tickSpacing) * tickSpacing;\n\n        INonfungiblePositionManager.MintParams memory params = INonfungiblePositionManager\n            .MintParams({\n            token0: token0,\n            token1: token1,\n            fee: feeTier,\n            tickLower: tickLower,\n            tickUpper: tickUpper,\n            amount0Desired: amount0Desired,\n            amount1Desired: amount1Desired,\n            amount0Min: 0,\n            amount1Min: 0,\n            recipient: address(this),\n            deadline: block.timestamp + 300\n        });\n\n        return nonfungiblePositionManager.mint(params);\n    }\n\n    /**\n     * @notice Internal tick spacing calculation\n     */\n    function _getTickSpacingInternal(uint24 fee) internal pure returns (int24 tickSpacing) {\n        if (fee == FEE_LOW) {\n            // 500 = 0.05%\n            return 10;\n        } else if (fee == FEE_MEDIUM) {\n            // 3000 = 0.3%\n            return 60;\n        } else if (fee == FEE_HIGH) {\n            // 10000 = 1%\n            return 200;\n        } else {\n            revert InvalidInput();\n        }\n    }\n\n    /**\n     * @notice Calculate the initial sqrt price for a V3 pool\n     * @param amount0Desired Amount of token0 to be paired\n     * @param amount1Desired Amount of token1 to be paired\n     * @return sqrtPriceX96 The sqrt price in Q64.96 format for pool initialization\n     */\n    function _calculateInitialSqrtPrice(\n        uint256 amount0Desired,\n        uint256 amount1Desired\n    )\n        internal\n        pure\n        returns (uint160 sqrtPriceX96)\n    {\n        uint256 priceX96 =\n            FullMath.mulDivRoundingUp(PRICE_SCALE_192, amount1Desired, amount0Desired);\n        return uint160(sqrt(priceX96));\n    }\n\n    /**\n     * @notice Babylonian sqrt implementation\n     * @param y The value to calculate sqrt for\n     * @return z The square root of y\n     */\n    function sqrt(uint256 y) internal pure returns (uint256 z) {\n        if (y > 3) {\n            z = y;\n            uint256 x = y / 2 + 1;\n            while (x < z) {\n                z = x;\n                x = (y / x + x) / 2;\n            }\n        } else if (y != 0) {\n            z = 1;\n        }\n        // else z = 0 (default value)\n    }\n\n    /**\n     * @notice Handle NFT transfers (required for IERC721Receiver)\n     */\n    function onERC721Received(\n        address,\n        address,\n        uint256,\n        bytes calldata\n    )\n        external\n        view\n        override\n        returns (bytes4)\n    {\n        if (msg.sender != address(nonfungiblePositionManager)) {\n            revert Unauthorized();\n        }\n        return this.onERC721Received.selector;\n    }\n\n    /**\n     * @notice Receive function to accept ETH\n     */\n    receive() external payable { }\n}\n"}, "contracts/MoonbagsStake.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\nimport \"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\";\nimport \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\nimport \"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\";\nimport \"@openzeppelin/contracts/utils/math/Math.sol\";\n\n/**\n * @title Moonbags Staking\n */\ncontract MoonbagsStake is Initializable, ReentrancyGuardUpgradeable, OwnableUpgradeable {\n    using SafeERC20 for IERC20;\n    using Math for uint256;\n\n    // === Constants ===\n    uint256 private constant MULTIPLIER = 1e18;\n    uint256 public constant DEFAULT_DENY_UNSTAKE_DURATION = 1 hours;\n\n    // === Structs ===\n    struct Configuration {\n        address admin;\n        uint256 denyUnstakeDuration; // Duration in seconds users must wait before unstaking\n    }\n\n    struct StakingPool {\n        address initializer;\n        uint256 totalSupply;\n        uint256 rewardIndex;\n        uint256 pendingInitialRewards;\n        uint256 totalRewards; // Total HYPE rewards in pool\n    }\n\n    struct CreatorPool {\n        address initializer;\n        address creator;\n        uint256 totalRewards; // Total HYPE rewards for creator\n    }\n\n    struct StakingAccount {\n        address staker;\n        uint256 balance;\n        uint256 rewardIndex;\n        uint256 earned;\n        uint256 unstakeDeadline;\n    }\n\n    // === State Variables ===\n    Configuration public config;\n\n    // Mapping from token address to staking pool\n    mapping(address => StakingPool) public stakingPools;\n\n    // Mapping from token address to creator pool\n    mapping(address => CreatorPool) public creatorPools;\n\n    // Mapping from token address to user address to staking account\n    mapping(address => mapping(address => StakingAccount)) public stakingAccounts;\n\n    // === Events ===\n    event InitializeStakingPoolEvent(\n        address indexed tokenAddress, address indexed initializer, uint256 timestamp\n    );\n\n    event InitializeCreatorPoolEvent(\n        address indexed tokenAddress,\n        address indexed initializer,\n        address indexed creator,\n        uint256 timestamp\n    );\n\n    event StakeEvent(\n        address indexed tokenAddress, address indexed staker, uint256 amount, uint256 timestamp\n    );\n\n    event UnstakeEvent(\n        address indexed tokenAddress,\n        address indexed unstaker,\n        uint256 amount,\n        bool isStakingAccountDeleted,\n        uint256 timestamp\n    );\n\n    event UpdateRewardIndexEvent(\n        address indexed tokenAddress,\n        address indexed rewardUpdater,\n        uint256 reward,\n        bool isInitialRewards,\n        uint256 timestamp\n    );\n\n    event DepositPoolCreatorEvent(\n        address indexed tokenAddress, address indexed depositor, uint256 amount, uint256 timestamp\n    );\n\n    event ClaimStakingPoolEvent(\n        address indexed tokenAddress,\n        address indexed claimer,\n        uint256 reward,\n        bool isStakingAccountDeleted,\n        uint256 timestamp\n    );\n\n    event ClaimCreatorPoolEvent(\n        address indexed tokenAddress, address indexed claimer, uint256 reward, uint256 timestamp\n    );\n\n    // === Custom Errors ===\n    error StakingPoolNotExist();\n    error StakingCreatorNotExist();\n    error StakingAccountNotExist();\n    error AccountBalanceNotEnough();\n    error InvalidCreator();\n    error InvalidAmount();\n    error RewardToClaimNotValid();\n    error UnstakeDeadlineNotAllow();\n    error NotUpgrade();\n    error ZeroAddress();\n    error StakingPoolAlreadyExists();\n    error CreatorPoolAlreadyExists();\n    error InvalidTokenAddress();\n    error OnlyAdmin();\n\n    // === Modifiers ===\n    modifier onlyAdmin() {\n        if (msg.sender != config.admin) revert OnlyAdmin();\n        _;\n    }\n\n    // === Initialize Function ===\n    /**\n     * @notice Initialize the contract\n     */\n    function initialize() public initializer {\n        __ReentrancyGuard_init();\n        __Ownable_init(msg.sender);\n\n        config =\n            Configuration({ admin: msg.sender, denyUnstakeDuration: DEFAULT_DENY_UNSTAKE_DURATION });\n    }\n\n    // === Public Functions ===\n\n    /**\n     * @notice Initializes a new staking pool for a specific token\n     * @param stakingToken The ERC20 token that will be staked in this pool\n     */\n    function initializeStakingPool(address stakingToken) external {\n        if (stakingToken == address(0)) revert InvalidTokenAddress();\n\n        if (stakingPoolExists(stakingToken)) {\n            return;\n        }\n\n        stakingPools[stakingToken] = StakingPool({\n            initializer: msg.sender,\n            totalSupply: 0,\n            rewardIndex: 0,\n            pendingInitialRewards: 0,\n            totalRewards: 0\n        });\n\n        emit InitializeStakingPoolEvent(stakingToken, msg.sender, block.timestamp);\n    }\n\n    /**\n     * @notice Initializes a creator pool for a specific token\n     * @param stakingToken The ERC20 token associated with this creator pool\n     * @param creator Address of the creator for this pool\n     */\n    function initializeCreatorPool(address stakingToken, address creator) external {\n        if (stakingToken == address(0)) revert InvalidTokenAddress();\n        if (creator == address(0)) revert ZeroAddress();\n\n        if (creatorPoolExists(stakingToken)) {\n            return;\n        }\n\n        creatorPools[stakingToken] =\n            CreatorPool({ initializer: msg.sender, creator: creator, totalRewards: 0 });\n\n        emit InitializeCreatorPoolEvent(stakingToken, msg.sender, creator, block.timestamp);\n    }\n\n    /**\n     * @notice Updates the reward index of a staking pool by adding new rewards\n     * @param stakingToken The token associated with the staking pool\n     */\n    function updateRewardIndex(address stakingToken) external payable {\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\n        if (msg.value == 0) revert InvalidAmount();\n\n        StakingPool storage pool = stakingPools[stakingToken];\n        uint256 rewardAmount = msg.value;\n\n        // No stakers - add to pending initial rewards\n        if (pool.totalSupply == 0) {\n            pool.pendingInitialRewards += rewardAmount;\n            pool.totalRewards += rewardAmount;\n\n            emit UpdateRewardIndexEvent(\n                stakingToken,\n                msg.sender,\n                rewardAmount,\n                true, // is initial rewards\n                block.timestamp\n            );\n            return;\n        }\n\n        // Update reward index\n        pool.rewardIndex += (rewardAmount * MULTIPLIER) / pool.totalSupply;\n        pool.totalRewards += rewardAmount;\n\n        emit UpdateRewardIndexEvent(\n            stakingToken,\n            msg.sender,\n            rewardAmount,\n            false, // not initial rewards\n            block.timestamp\n        );\n    }\n\n    /**\n     * @notice Deposits HYPE rewards into a creator pool\n     * @param stakingToken The token associated with the creator pool\n     */\n    function depositCreatorPool(address stakingToken) external payable {\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\n        if (msg.value == 0) revert InvalidAmount();\n\n        CreatorPool storage pool = creatorPools[stakingToken];\n        pool.totalRewards += msg.value;\n\n        emit DepositPoolCreatorEvent(stakingToken, msg.sender, msg.value, block.timestamp);\n    }\n\n    /**\n     * @notice Stakes tokens in a staking pool\n     * @param stakingToken The token to stake\n     * @param amount Amount of tokens to stake\n     */\n    function stake(address stakingToken, uint256 amount) external nonReentrant {\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\n        if (amount == 0) revert InvalidAmount();\n\n        StakingPool storage pool = stakingPools[stakingToken];\n\n        IERC20(stakingToken).safeTransferFrom(msg.sender, address(this), amount);\n\n        // Initialize staking account if first time staking\n        if (!stakingAccountExists(stakingToken, msg.sender)) {\n            stakingAccounts[stakingToken][msg.sender] = StakingAccount({\n                staker: msg.sender,\n                balance: 0,\n                rewardIndex: 0,\n                earned: pool.pendingInitialRewards,\n                unstakeDeadline: 0\n            });\n\n            // Reset pending initial rewards since first staker gets them\n            pool.pendingInitialRewards = 0;\n        }\n\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\n\n        // Update rewards before staking\n        _updateRewards(pool.rewardIndex, account);\n\n        // Update staking account\n        uint256 currentTime = block.timestamp;\n        account.unstakeDeadline = currentTime + config.denyUnstakeDuration;\n        account.balance += amount;\n        pool.totalSupply += amount;\n\n        emit StakeEvent(stakingToken, msg.sender, amount, currentTime);\n    }\n\n    /**\n     * @notice Unstakes tokens from a staking pool\n     * @param stakingToken The token to unstake\n     * @param unstakeAmount Amount of tokens to unstake\n     */\n    function unstake(address stakingToken, uint256 unstakeAmount) external nonReentrant {\n        if (unstakeAmount == 0) revert InvalidAmount();\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\n\n        StakingPool storage pool = stakingPools[stakingToken];\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\n\n        uint256 currentTime = block.timestamp;\n        if (currentTime < account.unstakeDeadline) revert UnstakeDeadlineNotAllow();\n\n        // Update rewards before unstaking\n        _updateRewards(pool.rewardIndex, account);\n\n        if (account.balance < unstakeAmount) revert AccountBalanceNotEnough();\n\n        // Update balances\n        account.balance -= unstakeAmount;\n        pool.totalSupply -= unstakeAmount;\n\n        IERC20(stakingToken).safeTransfer(msg.sender, unstakeAmount);\n\n        bool isAccountDeleted = _tryCleanupEmptyAccount(stakingToken, msg.sender);\n\n        emit UnstakeEvent(stakingToken, msg.sender, unstakeAmount, isAccountDeleted, currentTime);\n    }\n\n    /**\n     * @notice Claims rewards from a staking pool\n     * @param stakingToken The token associated with the staking pool\n     * @return rewardAmount The amount of HYPE claimed as rewards\n     */\n    function claimStakingPool(address stakingToken)\n        external\n        nonReentrant\n        returns (uint256 rewardAmount)\n    {\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\n\n        StakingPool storage pool = stakingPools[stakingToken];\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\n\n        // Update rewards before claiming\n        _updateRewards(pool.rewardIndex, account);\n\n        rewardAmount = account.earned;\n        if (rewardAmount == 0) revert RewardToClaimNotValid();\n\n        account.earned = 0;\n        pool.totalRewards -= rewardAmount;\n\n        payable(msg.sender).transfer(rewardAmount);\n\n        bool isAccountDeleted = _tryCleanupEmptyAccount(stakingToken, msg.sender);\n\n        emit ClaimStakingPoolEvent(\n            stakingToken, msg.sender, rewardAmount, isAccountDeleted, block.timestamp\n        );\n\n        return rewardAmount;\n    }\n\n    /**\n     * @notice Claims rewards from a creator pool\n     * @param stakingToken The token associated with the creator pool\n     * @return rewardAmount The amount of HYPE claimed from the creator pool\n     */\n    function claimCreatorPool(address stakingToken)\n        external\n        nonReentrant\n        returns (uint256 rewardAmount)\n    {\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\n\n        CreatorPool storage pool = creatorPools[stakingToken];\n        if (pool.creator != msg.sender) revert InvalidCreator();\n\n        rewardAmount = pool.totalRewards;\n        if (rewardAmount == 0) revert RewardToClaimNotValid();\n\n        pool.totalRewards = 0;\n\n        payable(msg.sender).transfer(rewardAmount);\n\n        emit ClaimCreatorPoolEvent(stakingToken, msg.sender, rewardAmount, block.timestamp);\n\n        return rewardAmount;\n    }\n\n    // === View Functions ===\n\n    /**\n     * @notice Check if a staking account exists for a user\n     * @param token The token address\n     * @param user The user address\n     * @return exists True if the staking account exists\n     */\n    function stakingAccountExists(address token, address user) public view returns (bool exists) {\n        return stakingAccounts[token][user].staker != address(0);\n    }\n\n    /**\n     * @notice Check if a staking pool exists for a token\n     * @param token The token address\n     * @return exists True if the staking pool exists\n     */\n    function stakingPoolExists(address token) public view returns (bool exists) {\n        return stakingPools[token].initializer != address(0);\n    }\n\n    /**\n     * @notice Check if a creator pool exists for a token\n     * @param token The token address\n     * @return exists True if the creator pool exists\n     */\n    function creatorPoolExists(address token) public view returns (bool exists) {\n        return creatorPools[token].initializer != address(0);\n    }\n\n    /**\n     * @notice Calculates the rewards earned by the caller for staking tokens\n     * @param stakingToken The token associated with the staking pool\n     * @return totalEarned The total amount of rewards earned\n     */\n    function calculateRewardsEarned(address stakingToken)\n        external\n        view\n        returns (uint256 totalEarned)\n    {\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\n\n        StakingPool storage pool = stakingPools[stakingToken];\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\n\n        uint256 pendingRewards = _calculateRewards(pool.rewardIndex, account);\n        return account.earned + pendingRewards;\n    }\n\n    /**\n     * @notice Get staking pool information\n     * @param stakingToken The token associated with the staking pool\n     * @return initializer Address that initialized the pool\n     * @return totalSupply Total amount of tokens staked\n     * @return rewardIndex Current reward index\n     * @return pendingInitialRewards Pending initial rewards\n     * @return totalRewards Total HYPE rewards in pool\n     */\n    function getStakingPoolInfo(address stakingToken)\n        external\n        view\n        returns (\n            address initializer,\n            uint256 totalSupply,\n            uint256 rewardIndex,\n            uint256 pendingInitialRewards,\n            uint256 totalRewards\n        )\n    {\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\n\n        StakingPool storage pool = stakingPools[stakingToken];\n        return (\n            pool.initializer,\n            pool.totalSupply,\n            pool.rewardIndex,\n            pool.pendingInitialRewards,\n            pool.totalRewards\n        );\n    }\n\n    /**\n     * @notice Get creator pool information\n     * @param stakingToken The token associated with the creator pool\n     * @return initializer Address that initialized the pool\n     * @return creator Address of the creator\n     * @return totalRewards Total HYPE rewards for creator\n     */\n    function getCreatorPoolInfo(address stakingToken)\n        external\n        view\n        returns (address initializer, address creator, uint256 totalRewards)\n    {\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\n\n        CreatorPool storage pool = creatorPools[stakingToken];\n        return (pool.initializer, pool.creator, pool.totalRewards);\n    }\n\n    /**\n     * @notice Get staking account information\n     * @param stakingToken The token associated with the staking pool\n     * @param staker Address of the staker\n     * @return balance Staked token balance\n     * @return rewardIndex Last reward index when rewards were updated\n     * @return earned Earned rewards ready to claim\n     * @return unstakeDeadline Timestamp when unstaking is allowed\n     */\n    function getStakingAccountInfo(\n        address stakingToken,\n        address staker\n    )\n        external\n        view\n        returns (uint256 balance, uint256 rewardIndex, uint256 earned, uint256 unstakeDeadline)\n    {\n        if (!stakingAccountExists(stakingToken, staker)) revert StakingAccountNotExist();\n\n        StakingAccount storage account = stakingAccounts[stakingToken][staker];\n        return (account.balance, account.rewardIndex, account.earned, account.unstakeDeadline);\n    }\n\n    // === Admin Functions ===\n\n    /**\n     * @notice Update configuration\n     * @param newDenyUnstakeDuration New deny unstake duration\n     */\n    function updateConfig(address newAdmin, uint256 newDenyUnstakeDuration) external onlyAdmin {\n        config.admin = newAdmin;\n        config.denyUnstakeDuration = newDenyUnstakeDuration;\n    }\n\n    // === Private Functions ===\n\n    /**\n     * @notice Calculates the pending rewards for a staking account\n     * @param stakingPoolRewardIndex Current reward index of the staking pool\n     * @param account The staking account to calculate rewards for\n     * @return reward The amount of pending rewards\n     */\n    function _calculateRewards(\n        uint256 stakingPoolRewardIndex,\n        StakingAccount storage account\n    )\n        private\n        view\n        returns (uint256 reward)\n    {\n        if (account.balance == 0) {\n            return 0;\n        }\n\n        uint256 rewardDiff = stakingPoolRewardIndex - account.rewardIndex;\n        reward = (account.balance * rewardDiff) / MULTIPLIER;\n\n        return reward;\n    }\n\n    /**\n     * @notice Updates the rewards earned by a staking account based on the current reward index\n     * @param stakingPoolRewardIndex Current reward index of the staking pool\n     * @param account The staking account to update rewards for\n     */\n    function _updateRewards(\n        uint256 stakingPoolRewardIndex,\n        StakingAccount storage account\n    )\n        private\n    {\n        uint256 pendingRewards = _calculateRewards(stakingPoolRewardIndex, account);\n        account.earned += pendingRewards;\n        account.rewardIndex = stakingPoolRewardIndex;\n    }\n\n    /**\n     * @notice Attempts to clean up a staking account if it has zero balance and zero earned rewards\n     * @param stakingToken The token associated with the staking pool\n     * @param staker The address of the staker whose account should be checked\n     * @return isDeleted Whether the account was successfully deleted\n     */\n    function _tryCleanupEmptyAccount(\n        address stakingToken,\n        address staker\n    )\n        private\n        returns (bool isDeleted)\n    {\n        if (!stakingAccountExists(stakingToken, staker)) {\n            return false;\n        }\n\n        StakingAccount storage account = stakingAccounts[stakingToken][staker];\n\n        if (account.balance == 0 && account.earned == 0) {\n            delete stakingAccounts[stakingToken][staker];\n            return true;\n        }\n\n        return false;\n    }\n}\n"}, "contracts/TokenLock.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.23;\n\nimport \"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\";\nimport \"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\";\nimport \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\nimport \"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\";\n\n/**\n * @title TokenLock\n */\ncontract TokenLock is Initializable, ReentrancyGuardUpgradeable, OwnableUpgradeable {\n    using SafeERC20 for IERC20;\n\n    struct Configuration {\n        address admin;\n    }\n\n    Configuration public config;\n\n    struct LockContract {\n        address token;\n        uint256 amount;\n        uint256 startTime;\n        uint256 endTime;\n        address recipient;\n        address locker;\n        bool closed;\n    }\n\n    mapping(uint256 => LockContract) public locks;\n    uint256 public nextLockId;\n\n    // Events\n    event LockCreated(\n        uint256 indexed lockId,\n        address indexed locker,\n        address indexed recipient,\n        address tokenAddress,\n        uint256 amount,\n        uint256 startTime,\n        uint256 endTime\n    );\n\n    event TokensWithdrawn(\n        uint256 indexed lockId, address indexed sender, address indexed recipient, uint256 amount\n    );\n\n    event ConfigUpdated(address oldAdmin, address newAdmin);\n\n    // Errors\n    error InvalidParams();\n    error Unauthorized();\n    error ContractClosed();\n    error InvalidLockId();\n\n    /**\n     * @notice Initialize the contract\n     */\n    function initialize() external initializer {\n        __ReentrancyGuard_init();\n        __Ownable_init(msg.sender);\n\n        config = Configuration({ admin: msg.sender });\n        nextLockId = 1;\n    }\n\n    /**\n     * @notice Create a new time-locked token contract\n     * @param token Address of the ERC20 token to lock\n     * @param amount Amount of tokens to lock\n     * @param endTime End time of the lock in seconds\n     * @param recipient Address that will be able to claim the tokens after the lock period\n     * @return lockId The ID of the created lock\n     */\n    function createLock(\n        address token,\n        uint256 amount,\n        uint256 endTime,\n        address recipient\n    )\n        external\n        nonReentrant\n        returns (uint256 lockId)\n    {\n        uint256 startTime = block.timestamp;\n\n        if (endTime <= startTime) revert InvalidParams();\n        if (token == address(0) || recipient == address(0)) revert InvalidParams();\n\n        lockId = nextLockId++;\n        locks[lockId] = LockContract({\n            token: token,\n            amount: amount,\n            startTime: startTime,\n            endTime: endTime,\n            recipient: recipient,\n            locker: msg.sender,\n            closed: false\n        });\n\n        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);\n\n        emit LockCreated(lockId, msg.sender, recipient, token, amount, startTime, endTime);\n\n        return lockId;\n    }\n\n    /**\n     * @notice Withdraw tokens from a lock after the lock period has ended\n     * @param lockId ID of the lock to withdraw from\n     */\n    function withdraw(uint256 lockId) external nonReentrant {\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\n\n        LockContract storage lockContract = locks[lockId];\n        address sender = msg.sender;\n\n        if (\n            sender != config.admin && sender != lockContract.recipient\n                && sender != lockContract.locker\n        ) {\n            revert Unauthorized();\n        }\n        if (lockContract.closed) revert ContractClosed();\n        if (block.timestamp < lockContract.endTime) revert Unauthorized();\n\n        lockContract.closed = true;\n        IERC20(lockContract.token).safeTransfer(lockContract.recipient, lockContract.amount);\n\n        emit TokensWithdrawn(lockId, sender, lockContract.recipient, lockContract.amount);\n    }\n\n    /**\n     * @notice Extend the lock duration of an existing time-locked token contract\n     * @param lockId ID of the lock to extend\n     * @param newEndTime New end time for the lock\n     */\n    function extendLock(uint256 lockId, uint256 newEndTime) external {\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\n\n        LockContract storage lockContract = locks[lockId];\n        address sender = msg.sender;\n\n        if (sender != lockContract.locker) revert Unauthorized();\n        if (lockContract.closed) revert ContractClosed();\n\n        uint256 currentTime = block.timestamp;\n        if (newEndTime <= currentTime || newEndTime <= lockContract.endTime) {\n            revert InvalidParams();\n        }\n\n        lockContract.endTime = newEndTime;\n\n        emit LockCreated(\n            lockId,\n            lockContract.locker,\n            lockContract.recipient,\n            lockContract.token,\n            lockContract.amount,\n            lockContract.startTime,\n            lockContract.endTime\n        );\n    }\n\n    /**\n     * @notice Update configuration\n     * @param newAdmin New admin address\n     */\n    function updateConfig(address newAdmin) external onlyOwner {\n        if (newAdmin == address(0)) revert InvalidParams();\n\n        address oldAdmin = config.admin;\n\n        config.admin = newAdmin;\n\n        emit ConfigUpdated(oldAdmin, newAdmin);\n    }\n\n    /**\n     * @notice Get lock information\n     * @param lockId ID of the lock\n     * @return lockContract The lock contract details\n     */\n    function getLock(uint256 lockId) external view returns (LockContract memory lockContract) {\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\n        return locks[lockId];\n    }\n\n    /**\n     * @notice Check if a lock is withdrawable\n     * @param lockId ID of the lock\n     * @return withdrawable True if the lock can be withdrawn\n     */\n    function isWithdrawable(uint256 lockId) external view returns (bool withdrawable) {\n        if (lockId == 0 || lockId >= nextLockId) return false;\n\n        LockContract memory lockContract = locks[lockId];\n        return !lockContract.closed && block.timestamp >= lockContract.endTime;\n    }\n\n    /**\n     * @notice Get the number of locks created\n     * @return count Total number of locks\n     */\n    function getLockCount() external view returns (uint256 count) {\n        return nextLockId - 1;\n    }\n\n    /**\n     * @notice Get current configuration\n     * @return admin Current admin address\n     */\n    function getConfig() external view returns (address admin) {\n        return config.admin;\n    }\n\n    /**\n     * @notice Emergency withdraw function (admin only)\n     * @param token Token address to withdraw\n     * @param amount Amount to withdraw\n     */\n    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {\n        IERC20(token).safeTransfer(config.admin, amount);\n    }\n}\n"}}, "settings": {"metadata": {"bytecodeHash": "none", "useLiteralContent": true}, "optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "viaIR": true, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "<PERSON>v<PERSON><PERSON>", "userdoc", "storageLayout", "evm.gasEstimates", "<PERSON>v<PERSON><PERSON>", "userdoc"], "": ["ast"]}}, "libraries": {"": {"__CACHE_BREAKER__": "******************************************"}}}}
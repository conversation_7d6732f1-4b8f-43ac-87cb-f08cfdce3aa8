{"address": "0x6afdDE6AD191a052D0eaD0EF5Ee8e729fc4312Cc", "abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}, {"internalType": "string", "name": "uri_", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "TokenTransfersBlocked", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isListed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "isListed_", "type": "bool"}], "name": "setListed", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenLock", "type": "address"}], "name": "setTokenLockContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "uri_", "type": "string"}], "name": "setTokenURI", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenLock", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0xa9c9c82b7cd96b93698f2809d5e98c47afde5eee506f2f070320ab58b3f9ebc9", "receipt": {"to": null, "from": "0xb6981f22cB9A48B12483B7Fcf512Abd0C8a2B3de", "contractAddress": "0x6afdDE6AD191a052D0eaD0EF5Ee8e729fc4312Cc", "transactionIndex": 0, "gasUsed": "1035852", "logsBloom": "0x00000000000000000000000000000000000000000000000000800000000000000000004000000000000000000000000000000000000000000000000280000000000000000000000000000000000000000001000000000000000000000000000000000000020000000000000000000800000001000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000020000000000000000000000000000000000000000001000000000000000000000000", "blockHash": "0x391754c77dffcb600d39466efdd13866332913c70982509e24d29b27e7583c9e", "transactionHash": "0xa9c9c82b7cd96b93698f2809d5e98c47afde5eee506f2f070320ab58b3f9ebc9", "logs": [{"transactionIndex": 0, "blockNumber": 25875596, "transactionHash": "0xa9c9c82b7cd96b93698f2809d5e98c47afde5eee506f2f070320ab58b3f9ebc9", "address": "0x6afdDE6AD191a052D0eaD0EF5Ee8e729fc4312Cc", "topics": ["0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000b6981f22cb9a48b12483b7fcf512abd0c8a2b3de"], "data": "0x", "logIndex": 0, "blockHash": "0x391754c77dffcb600d39466efdd13866332913c70982509e24d29b27e7583c9e"}], "blockNumber": 25875596, "cumulativeGasUsed": "1035852", "status": 1, "byzantium": true}, "args": ["SHRO Platform Token", "SHRO", 9, "https://moonbags.io/metadata/platform-token.json"], "numDeployments": 1, "solcInputHash": "2d791615bdbd29bde3ba98a0e9c3b3ee", "metadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol_\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"decimals_\",\"type\":\"uint8\"},{\"internalType\":\"string\",\"name\":\"uri_\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TokenTransfersBlocked\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isListed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"isListed_\",\"type\":\"bool\"}],\"name\":\"setListed\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_tokenLock\",\"type\":\"address\"}],\"name\":\"setTokenLockContract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"uri_\",\"type\":\"string\"}],\"name\":\"setTokenURI\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenLock\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"See {IERC20-allowance}.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"See {IERC20-balanceOf}.\"},\"burn(address,uint256)\":{\"params\":{\"amount\":\"Amount of tokens to burn\",\"from\":\"Address from which tokens will be burned\"}},\"constructor\":{\"params\":{\"decimals_\":\"Token decimals\",\"name_\":\"Token name\",\"symbol_\":\"Token symbol\",\"uri_\":\"Token metadata URI\"}},\"decimals()\":{\"returns\":{\"_0\":\"uint8 Number of decimals\"}},\"mint(address,uint256)\":{\"params\":{\"amount\":\"Amount of tokens to mint\",\"to\":\"Address to receive the minted tokens\"}},\"name()\":{\"details\":\"Returns the name of the token.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setListed(bool)\":{\"params\":{\"isListed_\":\"Boolean indicating if the token is listed\"}},\"setTokenLockContract(address)\":{\"params\":{\"_tokenLock\":\"Address of the token lock contract\"}},\"setTokenURI(string)\":{\"params\":{\"uri_\":\"New token URI\"}},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"tokenURI()\":{\"returns\":{\"_0\":\"string Token URI\"}},\"totalSupply()\":{\"details\":\"See {IERC20-totalSupply}.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"title\":\"LaunchpadToken\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"burn(address,uint256)\":{\"notice\":\"Burn tokens from a specified address\"},\"constructor\":{\"notice\":\"Constructor for LaunchpadToken\"},\"decimals()\":{\"notice\":\"Returns the number of decimals used for the token\"},\"mint(address,uint256)\":{\"notice\":\"Mint new tokens to a specified address\"},\"setListed(bool)\":{\"notice\":\"Set the listed status of the token\"},\"setTokenLockContract(address)\":{\"notice\":\"Set the address of the token lock contract\"},\"setTokenURI(string)\":{\"notice\":\"Set the token metadata URI\"},\"tokenURI()\":{\"notice\":\"Returns the token metadata URI\"}},\"notice\":\"ERC20 token deployed through the Moonbags launchpad\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/LaunchpadToken.sol\":\"LaunchpadToken\"},\"evmVersion\":\"paris\",\"libraries\":{\":__CACHE_BREAKER__\":\"******************************************\"},\"metadata\":{\"bytecodeHash\":\"none\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {Context} from \\\"../utils/Context.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * The initial owner is set to the address provided by the deployer. This can\\n * later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract Ownable is Context {\\n    address private _owner;\\n\\n    /**\\n     * @dev The caller account is not authorized to perform an operation.\\n     */\\n    error OwnableUnauthorizedAccount(address account);\\n\\n    /**\\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\\n     */\\n    error OwnableInvalidOwner(address owner);\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\\n     */\\n    constructor(address initialOwner) {\\n        if (initialOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(initialOwner);\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        return _owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        if (owner() != _msgSender()) {\\n            revert OwnableUnauthorizedAccount(_msgSender());\\n        }\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        if (newOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        address oldOwner = _owner;\\n        _owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n}\\n\",\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (interfaces/draft-IERC6093.sol)\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Standard ERC-20 Errors\\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-20 tokens.\\n */\\ninterface IERC20Errors {\\n    /**\\n     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     * @param balance Current balance for the interacting account.\\n     * @param needed Minimum amount required to perform a transfer.\\n     */\\n    error ERC20InsufficientBalance(address sender, uint256 balance, uint256 needed);\\n\\n    /**\\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     */\\n    error ERC20InvalidSender(address sender);\\n\\n    /**\\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\\n     * @param receiver Address to which tokens are being transferred.\\n     */\\n    error ERC20InvalidReceiver(address receiver);\\n\\n    /**\\n     * @dev Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\\n     * @param spender Address that may be allowed to operate on tokens without being their owner.\\n     * @param allowance Amount of tokens a `spender` is allowed to operate with.\\n     * @param needed Minimum amount required to perform a transfer.\\n     */\\n    error ERC20InsufficientAllowance(address spender, uint256 allowance, uint256 needed);\\n\\n    /**\\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\\n     * @param approver Address initiating an approval operation.\\n     */\\n    error ERC20InvalidApprover(address approver);\\n\\n    /**\\n     * @dev Indicates a failure with the `spender` to be approved. Used in approvals.\\n     * @param spender Address that may be allowed to operate on tokens without being their owner.\\n     */\\n    error ERC20InvalidSpender(address spender);\\n}\\n\\n/**\\n * @dev Standard ERC-721 Errors\\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-721 tokens.\\n */\\ninterface IERC721Errors {\\n    /**\\n     * @dev Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in ERC-20.\\n     * Used in balance queries.\\n     * @param owner Address of the current owner of a token.\\n     */\\n    error ERC721InvalidOwner(address owner);\\n\\n    /**\\n     * @dev Indicates a `tokenId` whose `owner` is the zero address.\\n     * @param tokenId Identifier number of a token.\\n     */\\n    error ERC721NonexistentToken(uint256 tokenId);\\n\\n    /**\\n     * @dev Indicates an error related to the ownership over a particular token. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     * @param tokenId Identifier number of a token.\\n     * @param owner Address of the current owner of a token.\\n     */\\n    error ERC721IncorrectOwner(address sender, uint256 tokenId, address owner);\\n\\n    /**\\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     */\\n    error ERC721InvalidSender(address sender);\\n\\n    /**\\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\\n     * @param receiver Address to which tokens are being transferred.\\n     */\\n    error ERC721InvalidReceiver(address receiver);\\n\\n    /**\\n     * @dev Indicates a failure with the `operator`\\u2019s approval. Used in transfers.\\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\\n     * @param tokenId Identifier number of a token.\\n     */\\n    error ERC721InsufficientApproval(address operator, uint256 tokenId);\\n\\n    /**\\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\\n     * @param approver Address initiating an approval operation.\\n     */\\n    error ERC721InvalidApprover(address approver);\\n\\n    /**\\n     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.\\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\\n     */\\n    error ERC721InvalidOperator(address operator);\\n}\\n\\n/**\\n * @dev Standard ERC-1155 Errors\\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-1155 tokens.\\n */\\ninterface IERC1155Errors {\\n    /**\\n     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     * @param balance Current balance for the interacting account.\\n     * @param needed Minimum amount required to perform a transfer.\\n     * @param tokenId Identifier number of a token.\\n     */\\n    error ERC1155InsufficientBalance(address sender, uint256 balance, uint256 needed, uint256 tokenId);\\n\\n    /**\\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     */\\n    error ERC1155InvalidSender(address sender);\\n\\n    /**\\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\\n     * @param receiver Address to which tokens are being transferred.\\n     */\\n    error ERC1155InvalidReceiver(address receiver);\\n\\n    /**\\n     * @dev Indicates a failure with the `operator`\\u2019s approval. Used in transfers.\\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\\n     * @param owner Address of the current owner of a token.\\n     */\\n    error ERC1155MissingApprovalForAll(address operator, address owner);\\n\\n    /**\\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\\n     * @param approver Address initiating an approval operation.\\n     */\\n    error ERC1155InvalidApprover(address approver);\\n\\n    /**\\n     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.\\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\\n     */\\n    error ERC1155InvalidOperator(address operator);\\n\\n    /**\\n     * @dev Indicates an array length mismatch between ids and values in a safeBatchTransferFrom operation.\\n     * Used in batch transfers.\\n     * @param idsLength Length of the array of token identifiers\\n     * @param valuesLength Length of the array of token amounts\\n     */\\n    error ERC1155InvalidArrayLength(uint256 idsLength, uint256 valuesLength);\\n}\\n\",\"keccak256\":\"0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (token/ERC20/ERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"./IERC20.sol\\\";\\nimport {IERC20Metadata} from \\\"./extensions/IERC20Metadata.sol\\\";\\nimport {Context} from \\\"../../utils/Context.sol\\\";\\nimport {IERC20Errors} from \\\"../../interfaces/draft-IERC6093.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC20} interface.\\n *\\n * This implementation is agnostic to the way tokens are created. This means\\n * that a supply mechanism has to be added in a derived contract using {_mint}.\\n *\\n * TIP: For a detailed writeup see our guide\\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\\n * to implement supply mechanisms].\\n *\\n * The default value of {decimals} is 18. To change this, you should override\\n * this function so it returns a different value.\\n *\\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\\n * instead returning `false` on failure. This behavior is nonetheless\\n * conventional and does not conflict with the expectations of ERC-20\\n * applications.\\n */\\nabstract contract ERC20 is Context, IERC20, IERC20Metadata, IERC20Errors {\\n    mapping(address account => uint256) private _balances;\\n\\n    mapping(address account => mapping(address spender => uint256)) private _allowances;\\n\\n    uint256 private _totalSupply;\\n\\n    string private _name;\\n    string private _symbol;\\n\\n    /**\\n     * @dev Sets the values for {name} and {symbol}.\\n     *\\n     * Both values are immutable: they can only be set once during construction.\\n     */\\n    constructor(string memory name_, string memory symbol_) {\\n        _name = name_;\\n        _symbol = symbol_;\\n    }\\n\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() public view virtual returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev Returns the symbol of the token, usually a shorter version of the\\n     * name.\\n     */\\n    function symbol() public view virtual returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev Returns the number of decimals used to get its user representation.\\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n     * be displayed to a user as `5.05` (`505 / 10 ** 2`).\\n     *\\n     * Tokens usually opt for a value of 18, imitating the relationship between\\n     * Ether and Wei. This is the default value returned by this function, unless\\n     * it's overridden.\\n     *\\n     * NOTE: This information is only used for _display_ purposes: it in\\n     * no way affects any of the arithmetic of the contract, including\\n     * {IERC20-balanceOf} and {IERC20-transfer}.\\n     */\\n    function decimals() public view virtual returns (uint8) {\\n        return 18;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-totalSupply}.\\n     */\\n    function totalSupply() public view virtual returns (uint256) {\\n        return _totalSupply;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-balanceOf}.\\n     */\\n    function balanceOf(address account) public view virtual returns (uint256) {\\n        return _balances[account];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transfer}.\\n     *\\n     * Requirements:\\n     *\\n     * - `to` cannot be the zero address.\\n     * - the caller must have a balance of at least `value`.\\n     */\\n    function transfer(address to, uint256 value) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        _transfer(owner, to, value);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-allowance}.\\n     */\\n    function allowance(address owner, address spender) public view virtual returns (uint256) {\\n        return _allowances[owner][spender];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-approve}.\\n     *\\n     * NOTE: If `value` is the maximum `uint256`, the allowance is not updated on\\n     * `transferFrom`. This is semantically equivalent to an infinite approval.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     */\\n    function approve(address spender, uint256 value) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        _approve(owner, spender, value);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transferFrom}.\\n     *\\n     * Skips emitting an {Approval} event indicating an allowance update. This is not\\n     * required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve].\\n     *\\n     * NOTE: Does not update the allowance if the current allowance\\n     * is the maximum `uint256`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` and `to` cannot be the zero address.\\n     * - `from` must have a balance of at least `value`.\\n     * - the caller must have allowance for ``from``'s tokens of at least\\n     * `value`.\\n     */\\n    function transferFrom(address from, address to, uint256 value) public virtual returns (bool) {\\n        address spender = _msgSender();\\n        _spendAllowance(from, spender, value);\\n        _transfer(from, to, value);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to`.\\n     *\\n     * This internal function is equivalent to {transfer}, and can be used to\\n     * e.g. implement automatic token fees, slashing mechanisms, etc.\\n     *\\n     * Emits a {Transfer} event.\\n     *\\n     * NOTE: This function is not virtual, {_update} should be overridden instead.\\n     */\\n    function _transfer(address from, address to, uint256 value) internal {\\n        if (from == address(0)) {\\n            revert ERC20InvalidSender(address(0));\\n        }\\n        if (to == address(0)) {\\n            revert ERC20InvalidReceiver(address(0));\\n        }\\n        _update(from, to, value);\\n    }\\n\\n    /**\\n     * @dev Transfers a `value` amount of tokens from `from` to `to`, or alternatively mints (or burns) if `from`\\n     * (or `to`) is the zero address. All customizations to transfers, mints, and burns should be done by overriding\\n     * this function.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _update(address from, address to, uint256 value) internal virtual {\\n        if (from == address(0)) {\\n            // Overflow check required: The rest of the code assumes that totalSupply never overflows\\n            _totalSupply += value;\\n        } else {\\n            uint256 fromBalance = _balances[from];\\n            if (fromBalance < value) {\\n                revert ERC20InsufficientBalance(from, fromBalance, value);\\n            }\\n            unchecked {\\n                // Overflow not possible: value <= fromBalance <= totalSupply.\\n                _balances[from] = fromBalance - value;\\n            }\\n        }\\n\\n        if (to == address(0)) {\\n            unchecked {\\n                // Overflow not possible: value <= totalSupply or value <= fromBalance <= totalSupply.\\n                _totalSupply -= value;\\n            }\\n        } else {\\n            unchecked {\\n                // Overflow not possible: balance + value is at most totalSupply, which we know fits into a uint256.\\n                _balances[to] += value;\\n            }\\n        }\\n\\n        emit Transfer(from, to, value);\\n    }\\n\\n    /**\\n     * @dev Creates a `value` amount of tokens and assigns them to `account`, by transferring it from address(0).\\n     * Relies on the `_update` mechanism\\n     *\\n     * Emits a {Transfer} event with `from` set to the zero address.\\n     *\\n     * NOTE: This function is not virtual, {_update} should be overridden instead.\\n     */\\n    function _mint(address account, uint256 value) internal {\\n        if (account == address(0)) {\\n            revert ERC20InvalidReceiver(address(0));\\n        }\\n        _update(address(0), account, value);\\n    }\\n\\n    /**\\n     * @dev Destroys a `value` amount of tokens from `account`, lowering the total supply.\\n     * Relies on the `_update` mechanism.\\n     *\\n     * Emits a {Transfer} event with `to` set to the zero address.\\n     *\\n     * NOTE: This function is not virtual, {_update} should be overridden instead\\n     */\\n    function _burn(address account, uint256 value) internal {\\n        if (account == address(0)) {\\n            revert ERC20InvalidSender(address(0));\\n        }\\n        _update(account, address(0), value);\\n    }\\n\\n    /**\\n     * @dev Sets `value` as the allowance of `spender` over the `owner`'s tokens.\\n     *\\n     * This internal function is equivalent to `approve`, and can be used to\\n     * e.g. set automatic allowances for certain subsystems, etc.\\n     *\\n     * Emits an {Approval} event.\\n     *\\n     * Requirements:\\n     *\\n     * - `owner` cannot be the zero address.\\n     * - `spender` cannot be the zero address.\\n     *\\n     * Overrides to this logic should be done to the variant with an additional `bool emitEvent` argument.\\n     */\\n    function _approve(address owner, address spender, uint256 value) internal {\\n        _approve(owner, spender, value, true);\\n    }\\n\\n    /**\\n     * @dev Variant of {_approve} with an optional flag to enable or disable the {Approval} event.\\n     *\\n     * By default (when calling {_approve}) the flag is set to true. On the other hand, approval changes made by\\n     * `_spendAllowance` during the `transferFrom` operation set the flag to false. This saves gas by not emitting any\\n     * `Approval` event during `transferFrom` operations.\\n     *\\n     * Anyone who wishes to continue emitting `Approval` events on the`transferFrom` operation can force the flag to\\n     * true using the following override:\\n     *\\n     * ```solidity\\n     * function _approve(address owner, address spender, uint256 value, bool) internal virtual override {\\n     *     super._approve(owner, spender, value, true);\\n     * }\\n     * ```\\n     *\\n     * Requirements are the same as {_approve}.\\n     */\\n    function _approve(address owner, address spender, uint256 value, bool emitEvent) internal virtual {\\n        if (owner == address(0)) {\\n            revert ERC20InvalidApprover(address(0));\\n        }\\n        if (spender == address(0)) {\\n            revert ERC20InvalidSpender(address(0));\\n        }\\n        _allowances[owner][spender] = value;\\n        if (emitEvent) {\\n            emit Approval(owner, spender, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Updates `owner`'s allowance for `spender` based on spent `value`.\\n     *\\n     * Does not update the allowance value in case of infinite allowance.\\n     * Revert if not enough allowance is available.\\n     *\\n     * Does not emit an {Approval} event.\\n     */\\n    function _spendAllowance(address owner, address spender, uint256 value) internal virtual {\\n        uint256 currentAllowance = allowance(owner, spender);\\n        if (currentAllowance < type(uint256).max) {\\n            if (currentAllowance < value) {\\n                revert ERC20InsufficientAllowance(spender, currentAllowance, value);\\n            }\\n            unchecked {\\n                _approve(owner, spender, currentAllowance - value, false);\\n            }\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x41f6b3b9e030561e7896dbef372b499cc8d418a80c3884a4d65a68f2fdc7493a\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC-20 standard as defined in the ERC.\\n */\\ninterface IERC20 {\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n    /**\\n     * @dev Returns the value of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the value of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\\n     * allowance mechanism. `value` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\\n}\\n\",\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"../IERC20.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC-20 standard.\\n */\\ninterface IERC20Metadata is IERC20 {\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the symbol of the token.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the decimals places of the token.\\n     */\\n    function decimals() external view returns (uint8);\\n}\\n\",\"keccak256\":\"0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    function _contextSuffixLength() internal view virtual returns (uint256) {\\n        return 0;\\n    }\\n}\\n\",\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\"},\"contracts/LaunchpadToken.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport { ERC20 } from \\\"@openzeppelin/contracts/token/ERC20/ERC20.sol\\\";\\nimport { Ownable } from \\\"@openzeppelin/contracts/access/Ownable.sol\\\";\\n\\n/**\\n * @title LaunchpadToken\\n * @notice ERC20 token deployed through the Moonbags launchpad\\n */\\ncontract LaunchpadToken is ERC20, Ownable {\\n    error TokenTransfersBlocked();\\n\\n    bool public isListed;\\n    uint8 private _decimals;\\n    string private _tokenURI;\\n    address public tokenLock;\\n\\n    /**\\n     * @notice Constructor for LaunchpadToken\\n     * @param name_ Token name\\n     * @param symbol_ Token symbol\\n     * @param decimals_ Token decimals\\n     * @param uri_ Token metadata URI\\n     */\\n    constructor(\\n        string memory name_,\\n        string memory symbol_,\\n        uint8 decimals_,\\n        string memory uri_\\n    )\\n        ERC20(name_, symbol_)\\n        Ownable(msg.sender)\\n    {\\n        _decimals = decimals_;\\n        _tokenURI = uri_;\\n        isListed = false; // Initially set to false\\n    }\\n\\n    /**\\n     * @notice Mint new tokens to a specified address\\n     * @param to Address to receive the minted tokens\\n     * @param amount Amount of tokens to mint\\n     */\\n    function mint(address to, uint256 amount) external onlyOwner {\\n        _mint(to, amount);\\n    }\\n\\n    /**\\n     * @notice Set the listed status of the token\\n     * @param isListed_ Boolean indicating if the token is listed\\n     */\\n    function setListed(bool isListed_) external onlyOwner {\\n        isListed = isListed_;\\n    }\\n\\n    /**\\n     * @notice Set the address of the token lock contract\\n     * @param _tokenLock Address of the token lock contract\\n     */\\n    function setTokenLockContract(address _tokenLock) external onlyOwner {\\n        tokenLock = _tokenLock;\\n    }\\n\\n    /**\\n     * @notice Burn tokens from a specified address\\n     * @param from Address from which tokens will be burned\\n     * @param amount Amount of tokens to burn\\n     */\\n    function burn(address from, uint256 amount) external onlyOwner {\\n        _burn(from, amount);\\n    }\\n\\n    /**\\n     * @dev Internal function to handle token transfers with transfer restrictions\\n     * @param from Sender address\\n     * @param to Recipient address\\n     * @param value Amount of tokens to transfer\\n     */\\n    function _update(address from, address to, uint256 value) internal override {\\n        if (\\n            !isListed && from != address(0) && to != address(0) && from != owner() && to != owner()\\n                && from != tokenLock\\n        ) {\\n            revert TokenTransfersBlocked();\\n        }\\n        super._update(from, to, value);\\n    }\\n\\n    /**\\n     * @notice Returns the number of decimals used for the token\\n     * @return uint8 Number of decimals\\n     */\\n    function decimals() public view override returns (uint8) {\\n        return _decimals;\\n    }\\n\\n    /**\\n     * @notice Returns the token metadata URI\\n     * @return string Token URI\\n     */\\n    function tokenURI() public view returns (string memory) {\\n        return _tokenURI;\\n    }\\n\\n    /**\\n     * @notice Set the token metadata URI\\n     * @param uri_ New token URI\\n     */\\n    function setTokenURI(string memory uri_) external onlyOwner {\\n        _tokenURI = uri_;\\n    }\\n}\\n\",\"keccak256\":\"0x3a35a193c26f219a717f0813981aabd60766f6216e8b3e8fdc9b86305cae6d20\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"errors": {"ERC20InsufficientAllowance(address,uint256,uint256)": [{"details": "Indicates a failure with the `spender`’s `allowance`. Used in transfers.", "params": {"allowance": "Amount of tokens a `spender` is allowed to operate with.", "needed": "Minimum amount required to perform a transfer.", "spender": "Address that may be allowed to operate on tokens without being their owner."}}], "ERC20InsufficientBalance(address,uint256,uint256)": [{"details": "Indicates an error related to the current `balance` of a `sender`. Used in transfers.", "params": {"balance": "Current balance for the interacting account.", "needed": "Minimum amount required to perform a transfer.", "sender": "Address whose tokens are being transferred."}}], "ERC20InvalidApprover(address)": [{"details": "Indicates a failure with the `approver` of a token to be approved. Used in approvals.", "params": {"approver": "Address initiating an approval operation."}}], "ERC20InvalidReceiver(address)": [{"details": "Indicates a failure with the token `receiver`. Used in transfers.", "params": {"receiver": "Address to which tokens are being transferred."}}], "ERC20InvalidSender(address)": [{"details": "Indicates a failure with the token `sender`. Used in transfers.", "params": {"sender": "Address whose tokens are being transferred."}}], "ERC20InvalidSpender(address)": [{"details": "Indicates a failure with the `spender` to be approved. Used in approvals.", "params": {"spender": "Address that may be allowed to operate on tokens without being their owner."}}], "OwnableInvalidOwner(address)": [{"details": "The owner is not a valid owner account. (eg. `address(0)`)"}], "OwnableUnauthorizedAccount(address)": [{"details": "The caller account is not authorized to perform an operation."}]}, "events": {"Approval(address,address,uint256)": {"details": "Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance."}, "Transfer(address,address,uint256)": {"details": "Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero."}}, "kind": "dev", "methods": {"allowance(address,address)": {"details": "See {IERC20-allowance}."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "See {IERC20-balanceOf}."}, "burn(address,uint256)": {"params": {"amount": "Amount of tokens to burn", "from": "Address from which tokens will be burned"}}, "constructor": {"params": {"decimals_": "Token decimals", "name_": "Token name", "symbol_": "Token symbol", "uri_": "Token metadata URI"}}, "decimals()": {"returns": {"_0": "uint8 Number of decimals"}}, "mint(address,uint256)": {"params": {"amount": "Amount of tokens to mint", "to": "Address to receive the minted tokens"}}, "name()": {"details": "Returns the name of the token."}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "setListed(bool)": {"params": {"isListed_": "Boolean indicating if the token is listed"}}, "setTokenLockContract(address)": {"params": {"_tokenLock": "Address of the token lock contract"}}, "setTokenURI(string)": {"params": {"uri_": "New token URI"}}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "tokenURI()": {"returns": {"_0": "string Token URI"}}, "totalSupply()": {"details": "See {IERC20-totalSupply}."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "title": "LaunchpadToken", "version": 1}, "userdoc": {"kind": "user", "methods": {"burn(address,uint256)": {"notice": "Burn tokens from a specified address"}, "constructor": {"notice": "Constructor for LaunchpadToken"}, "decimals()": {"notice": "Returns the number of decimals used for the token"}, "mint(address,uint256)": {"notice": "Mint new tokens to a specified address"}, "setListed(bool)": {"notice": "Set the listed status of the token"}, "setTokenLockContract(address)": {"notice": "Set the address of the token lock contract"}, "setTokenURI(string)": {"notice": "Set the token metadata URI"}, "tokenURI()": {"notice": "Returns the token metadata URI"}}, "notice": "ERC20 token deployed through the Moonbags launchpad", "version": 1}, "storageLayout": {"storage": [{"astId": 1035, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "_balances", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 1041, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "_allowances", "offset": 0, "slot": "1", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))"}, {"astId": 1043, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "_totalSupply", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 1045, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "_name", "offset": 0, "slot": "3", "type": "t_string_storage"}, {"astId": 1047, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "_symbol", "offset": 0, "slot": "4", "type": "t_string_storage"}, {"astId": 646, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "_owner", "offset": 0, "slot": "5", "type": "t_address"}, {"astId": 5608, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "isListed", "offset": 20, "slot": "5", "type": "t_bool"}, {"astId": 5610, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "_decimals", "offset": 21, "slot": "5", "type": "t_uint8"}, {"astId": 5612, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "_tokenURI", "offset": 0, "slot": "6", "type": "t_string_storage"}, {"astId": 5614, "contract": "contracts/LaunchpadToken.sol:LaunchpadToken", "label": "tokenLock", "offset": 0, "slot": "7", "type": "t_address"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_uint256)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"encoding": "inplace", "label": "uint8", "numberOfBytes": "1"}}}}
{"address": "******************************************", "abi": [{"inputs": [], "name": "InsufficientAmount", "type": "error"}, {"inputs": [], "name": "InsufficientHypeReserves", "type": "error"}, {"inputs": [], "name": "InsufficientReserves", "type": "error"}, {"inputs": [], "name": "InsufficientTokenReserves", "type": "error"}, {"inputs": [], "name": "InvalidConfiguration", "type": "error"}, {"inputs": [], "name": "InvalidDistributionTime", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidWithdrawalAmount", "type": "error"}, {"inputs": [], "name": "LPValueDecreased", "type": "error"}, {"inputs": [], "name": "NoPositionFound", "type": "error"}, {"inputs": [], "name": "NotEnoughThreshold", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "PoolAlreadyCompleted", "type": "error"}, {"inputs": [], "name": "PoolNotCompleted", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "TokenNotExists", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPlatformFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newInitialVirtualTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newRemainTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "newTokenDecimals", "type": "uint8"}, {"indexed": false, "internalType": "uint16", "name": "newInitPlatformFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "newInitCreatorFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "newInitStakeFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "newInitPlatformStakeFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "address", "name": "newPlatformTokenAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "ConfigurationUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldMoonbagsStake", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newMoonbagsStake", "type": "address"}], "name": "MoonbagsStakeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "string", "name": "lp", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PoolCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "symbol", "type": "string"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "string", "name": "twitter", "type": "string"}, {"indexed": false, "internalType": "string", "name": "telegram", "type": "string"}, {"indexed": false, "internalType": "string", "name": "website", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "virtualHypeReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "virtualTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "realHypeReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "realTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint16", "name": "platformFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "stakeFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "platformStakeFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint256", "name": "threshold", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "TokenCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldTokenLock", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newTokenLock", "type": "address"}], "name": "TokenLockContractUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "bool", "name": "isBuy", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "hypeAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "virtualHypeReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "virtualTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "realHypeReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "realTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "Trade", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "v3Pool", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "V3PoolCreated", "type": "event"}, {"inputs": [], "name": "DEFAULT_CREATOR_FEE_WITHDRAW", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_FEE_TIER", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_PLATFORM_FEE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_PLATFORM_FEE_WITHDRAW", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_REMAIN_TOKEN_RESERVES", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_STAKE_FEE_WITHDRAW", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_THRESHOLD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_TOKEN_DECIMALS", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DISTRIBUTE_FEE_LOCK_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FEE_DENOMINATOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FEE_HIGH", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FEE_LOW", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FEE_MEDIUM", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_DESCRIPTION_LENGTH", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SOCIAL_LENGTH", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_URI_LENGTH", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINIMUM_THRESHOLD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_LOCK_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ONE_CHECKPOINT_TIMESTAMP", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PLATFORM_TOKEN_BUYER", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "POOL_CREATION_FEE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_SCALE_192", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "VERSION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "activeFeeTier", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"}], "name": "buyExactIn", "outputs": [{"internalType": "uint256", "name": "tokenAmountOut", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "calculateActualVirtualTokenReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256"}], "name": "calculateInitialVirtualHypeReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "calculateVirtualRemainTokenReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "collectHyperSwapFees", "outputs": [{"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "config", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "treasury", "type": "address"}, {"internalType": "address", "name": "feePlatformRecipient", "type": "address"}, {"internalType": "uint256", "name": "platformFee", "type": "uint256"}, {"internalType": "uint256", "name": "initialVirtualTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "remainTokenReserves", "type": "uint256"}, {"internalType": "uint8", "name": "tokenDecimals", "type": "uint8"}, {"internalType": "uint16", "name": "initPlatformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "initCreatorFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "initStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "initPlatformStakeFeeWithdraw", "type": "uint16"}, {"internalType": "address", "name": "platformTokenAddress", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "twitter", "type": "string"}, {"internalType": "string", "name": "telegram", "type": "string"}, {"internalType": "string", "name": "website", "type": "string"}, {"internalType": "uint256", "name": "customThreshold", "type": "uint256"}, {"internalType": "uint256", "name": "amountOut", "type": "uint256"}, {"internalType": "uint256", "name": "lockDuration", "type": "uint256"}], "name": "createAndLockFirstBuy", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "twitter", "type": "string"}, {"internalType": "string", "name": "telegram", "type": "string"}, {"internalType": "string", "name": "website", "type": "string"}, {"internalType": "uint256", "name": "customThreshold", "type": "uint256"}], "name": "createPool", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "name": "createThresholdConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "distributeBondingCurveFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "earlyCompletePool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}], "name": "estimateBuyExactInCost", "outputs": [{"internalType": "uint256", "name": "totalCost", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}], "name": "estimateBuyExactInTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenAmountOut", "type": "uint256"}], "name": "estimateBuyExactOutCost", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenAmountIn", "type": "uint256"}], "name": "estimateSellTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "getPool", "outputs": [{"components": [{"internalType": "uint256", "name": "realHypeReserves", "type": "uint256"}, {"internalType": "uint256", "name": "realTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualHypeReserves", "type": "uint256"}, {"internalType": "uint256", "name": "remainTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualRemainTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "feeRecipient", "type": "uint256"}, {"internalType": "bool", "name": "isCompleted", "type": "bool"}, {"internalType": "uint256", "name": "threshold", "type": "uint256"}, {"internalType": "uint16", "name": "platformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"internalType": "uint16", "name": "stakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "platformStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint256", "name": "creationTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "feeDistributionUnlockTime", "type": "uint256"}], "internalType": "struct MoonbagsLaunchpad.Pool", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_nonfungiblePositionManager", "type": "address"}, {"internalType": "address", "name": "_weth9", "type": "address"}, {"internalType": "uint24", "name": "_poolFee", "type": "uint24"}, {"internalType": "address", "name": "_platformTokenAddress", "type": "address"}, {"internalType": "address", "name": "_moonbagsStake", "type": "address"}, {"internalType": "address", "name": "_tokenLock", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "isToken", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "moonbagsStake", "outputs": [{"internalType": "contract MoonbagsStake", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nonfungiblePositionManager", "outputs": [{"internalType": "contract INonfungiblePositionManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC721Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "pools", "outputs": [{"internalType": "uint256", "name": "realHypeReserves", "type": "uint256"}, {"internalType": "uint256", "name": "realTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualHypeReserves", "type": "uint256"}, {"internalType": "uint256", "name": "remainTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualRemainTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "feeRecipient", "type": "uint256"}, {"internalType": "bool", "name": "isCompleted", "type": "bool"}, {"internalType": "uint256", "name": "threshold", "type": "uint256"}, {"internalType": "uint16", "name": "platformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"internalType": "uint16", "name": "stakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "platformStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint256", "name": "creationTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "feeDistributionUnlockTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenAmountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"}], "name": "sellExactIn", "outputs": [{"internalType": "uint256", "name": "hypeAmountOut", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_moonbagsStake", "type": "address"}], "name": "setMoonbagsStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenLock", "type": "address"}], "name": "setTokenLock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "skim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "thresholdConfig", "outputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenLock", "outputs": [{"internalType": "contract TokenLock", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokenToPositionId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint24", "name": "newFeeTier", "type": "uint24"}], "name": "updateActiveFeeTier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "_newInitPlatformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_newInitCreatorFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_newInitStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_newInitPlatformStakeFeeWithdraw", "type": "uint16"}], "name": "updateConfigWithdrawFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_platformFee", "type": "uint256"}, {"internalType": "uint256", "name": "_initialVirtualTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "_remainTokenReserves", "type": "uint256"}, {"internalType": "uint8", "name": "_tokenDecimals", "type": "uint8"}, {"internalType": "uint16", "name": "_initPlatformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_initCreatorFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_initStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_initPlatformStakeFeeWithdraw", "type": "uint16"}, {"internalType": "address", "name": "_platformTokenAddress", "type": "address"}], "name": "updateConfiguration", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_treasury", "type": "address"}, {"internalType": "address", "name": "_feePlatformRecipient", "type": "address"}], "name": "updateFeeRecipients", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_initialVirtualTokenReserves", "type": "uint256"}], "name": "updateInitialVirtualTokenReserves", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newThreshold", "type": "uint256"}], "name": "updateThresholdConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "weth9", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "transactionHash": "0xaabfcfa254a1e24029dba7c7884cb281151b2840bb0d9d6f0e80d10514439f4b", "receipt": {"to": null, "from": "******************************************", "contractAddress": "******************************************", "transactionIndex": 0, "gasUsed": "5130713", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xe8fd1c22cc5f136f5c347653dc7a6722c783b485d3d2e1b345ffa4c6c2aa0d04", "transactionHash": "0xaabfcfa254a1e24029dba7c7884cb281151b2840bb0d9d6f0e80d10514439f4b", "logs": [], "blockNumber": 25876084, "cumulativeGasUsed": "5130713", "status": 1, "byzantium": true}, "args": [], "numDeployments": 1, "solcInputHash": "2d791615bdbd29bde3ba98a0e9c3b3ee", "metadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InsufficientAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientHypeReserves\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientReserves\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InsufficientTokenReserves\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidConfiguration\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidDistributionTime\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInput\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInput\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidWithdrawalAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LPValueDecreased\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoPositionFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotEnoughThreshold\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PoolAlreadyCompleted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PoolNotCompleted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TokenNotExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Unauthorized\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newPlatformFee\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newInitialVirtualTokenReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newRemainTokenReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"newTokenDecimals\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"newInitPlatformFeeWithdraw\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"newInitCreatorFeeWithdraw\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"newInitStakeFeeWithdraw\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"newInitPlatformStakeFeeWithdraw\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newPlatformTokenAddress\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"ConfigurationUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldMoonbagsStake\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newMoonbagsStake\",\"type\":\"address\"}],\"name\":\"MoonbagsStakeUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"lp\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"PoolCompleted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"uri\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"twitter\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"telegram\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"website\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"virtualHypeReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"virtualTokenReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"realHypeReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"realTokenReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"platformFeeWithdraw\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"creatorFeeWithdraw\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"stakeFeeWithdraw\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"platformStakeFeeWithdraw\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"TokenCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"oldTokenLock\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newTokenLock\",\"type\":\"address\"}],\"name\":\"TokenLockContractUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"bool\",\"name\":\"isBuy\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"hypeAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"tokenAmount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"virtualHypeReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"virtualTokenReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"realHypeReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"realTokenReserves\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"fee\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"Trade\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"v3Pool\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"V3PoolCreated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DEFAULT_CREATOR_FEE_WITHDRAW\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_FEE_TIER\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_PLATFORM_FEE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_PLATFORM_FEE_WITHDRAW\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_REMAIN_TOKEN_RESERVES\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_STAKE_FEE_WITHDRAW\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_THRESHOLD\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_TOKEN_DECIMALS\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DISTRIBUTE_FEE_LOCK_DURATION\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"FEE_DENOMINATOR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"FEE_HIGH\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"FEE_LOW\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"FEE_MEDIUM\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_DESCRIPTION_LENGTH\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_SOCIAL_LENGTH\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_URI_LENGTH\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MINIMUM_THRESHOLD\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MIN_LOCK_DURATION\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"ONE_CHECKPOINT_TIMESTAMP\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PLATFORM_TOKEN_BUYER\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"POOL_CREATION_FEE\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PRICE_SCALE_192\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"VERSION\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"activeFeeTier\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountOutMin\",\"type\":\"uint256\"}],\"name\":\"buyExactIn\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenAmountOut\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"calculateActualVirtualTokenReserves\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"}],\"name\":\"calculateInitialVirtualHypeReserves\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"calculateVirtualRemainTokenReserves\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"collectHyperSwapFees\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amount0\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount1\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"config\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"treasury\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"feePlatformRecipient\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"platformFee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"initialVirtualTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"remainTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"tokenDecimals\",\"type\":\"uint8\"},{\"internalType\":\"uint16\",\"name\":\"initPlatformFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"initCreatorFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"initStakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"initPlatformStakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"address\",\"name\":\"platformTokenAddress\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"uri\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"twitter\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"telegram\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"website\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"customThreshold\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountOut\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lockDuration\",\"type\":\"uint256\"}],\"name\":\"createAndLockFirstBuy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"uri\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"twitter\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"telegram\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"website\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"customThreshold\",\"type\":\"uint256\"}],\"name\":\"createPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"tokenAddress\",\"type\":\"address\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_threshold\",\"type\":\"uint256\"}],\"name\":\"createThresholdConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"distributeBondingCurveFees\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"earlyCompletePool\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"}],\"name\":\"estimateBuyExactInCost\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalCost\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"}],\"name\":\"estimateBuyExactInTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenAmountOut\",\"type\":\"uint256\"}],\"name\":\"estimateBuyExactOutCost\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenAmountIn\",\"type\":\"uint256\"}],\"name\":\"estimateSellTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"getPool\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"realHypeReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"realTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"virtualTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"virtualHypeReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"remainTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"virtualRemainTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feeRecipient\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"isCompleted\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"},{\"internalType\":\"uint16\",\"name\":\"platformFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"creatorFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"stakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"platformStakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint256\",\"name\":\"creationTimestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feeDistributionUnlockTime\",\"type\":\"uint256\"}],\"internalType\":\"struct MoonbagsLaunchpad.Pool\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_nonfungiblePositionManager\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_weth9\",\"type\":\"address\"},{\"internalType\":\"uint24\",\"name\":\"_poolFee\",\"type\":\"uint24\"},{\"internalType\":\"address\",\"name\":\"_platformTokenAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_moonbagsStake\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_tokenLock\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"isToken\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"exists\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"moonbagsStake\",\"outputs\":[{\"internalType\":\"contract MoonbagsStake\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"nonfungiblePositionManager\",\"outputs\":[{\"internalType\":\"contract INonfungiblePositionManager\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"onERC721Received\",\"outputs\":[{\"internalType\":\"bytes4\",\"name\":\"\",\"type\":\"bytes4\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"pools\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"realHypeReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"realTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"virtualTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"virtualHypeReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"remainTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"virtualRemainTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feeRecipient\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"isCompleted\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"},{\"internalType\":\"uint16\",\"name\":\"platformFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"creatorFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"stakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"platformStakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint256\",\"name\":\"creationTimestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feeDistributionUnlockTime\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenAmountIn\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountOutMin\",\"type\":\"uint256\"}],\"name\":\"sellExactIn\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"hypeAmountOut\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_moonbagsStake\",\"type\":\"address\"}],\"name\":\"setMoonbagsStake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_tokenLock\",\"type\":\"address\"}],\"name\":\"setTokenLock\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"skim\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"thresholdConfig\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenLock\",\"outputs\":[{\"internalType\":\"contract TokenLock\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"tokenToPositionId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"newFeeTier\",\"type\":\"uint24\"}],\"name\":\"updateActiveFeeTier\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"_newInitPlatformFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"_newInitCreatorFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"_newInitStakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"_newInitPlatformStakeFeeWithdraw\",\"type\":\"uint16\"}],\"name\":\"updateConfigWithdrawFee\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_platformFee\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_initialVirtualTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_remainTokenReserves\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"_tokenDecimals\",\"type\":\"uint8\"},{\"internalType\":\"uint16\",\"name\":\"_initPlatformFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"_initCreatorFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"_initStakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"_initPlatformStakeFeeWithdraw\",\"type\":\"uint16\"},{\"internalType\":\"address\",\"name\":\"_platformTokenAddress\",\"type\":\"address\"}],\"name\":\"updateConfiguration\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_treasury\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_feePlatformRecipient\",\"type\":\"address\"}],\"name\":\"updateFeeRecipients\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_initialVirtualTokenReserves\",\"type\":\"uint256\"}],\"name\":\"updateInitialVirtualTokenReserves\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newThreshold\",\"type\":\"uint256\"}],\"name\":\"updateThresholdConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"weth9\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"buyExactIn(address,uint256,uint256)\":{\"params\":{\"amountIn\":\"Exact amount of ETH to spend\",\"amountOutMin\":\"Minimum amount of tokens to receive\",\"token\":\"Token address\"}},\"collectHyperSwapFees(address)\":{\"params\":{\"token\":\"Token address associated with the position\"},\"returns\":{\"amount0\":\"The amount of fees collected in token0\",\"amount1\":\"The amount of fees collected in token1\"}},\"createAndLockFirstBuy(string,string,string,string,string,string,string,uint256,uint256,uint256)\":{\"params\":{\"amountOut\":\"Amount of tokens to buy and lock\",\"customThreshold\":\"Custom threshold (0 for default)\",\"description\":\"Token description (max 1000 characters)\",\"lockDuration\":\"Duration to lock the purchased tokens (minimum 1 hour)\",\"name\":\"Token name\",\"symbol\":\"Token symbol\",\"telegram\":\"Telegram link (max 500 characters)\",\"twitter\":\"Twitter handle (max 500 characters)\",\"uri\":\"Token metadata URI (max 300 characters)\",\"website\":\"Website URL (max 500 characters)\"}},\"createPool(string,string,string,string,string,string,string,uint256)\":{\"params\":{\"customThreshold\":\"Custom threshold (0 for default)\",\"description\":\"Token description (max 1000 characters)\",\"name\":\"Token name\",\"symbol\":\"Token symbol\",\"telegram\":\"Telegram link (max 500 characters)\",\"twitter\":\"Twitter handle (max 500 characters)\",\"uri\":\"Token metadata URI (max 300 characters)\",\"website\":\"Website URL (max 500 characters)\"}},\"distributeBondingCurveFees(address)\":{\"params\":{\"token\":\"Token address\"}},\"initialize(address,address,uint24,address,address,address)\":{\"params\":{\"_moonbagsStake\":\"MoonbagsStake contract address\",\"_nonfungiblePositionManager\":\"Position Manager address\",\"_platformTokenAddress\":\"Platform token address\",\"_poolFee\":\"Pool fee (e.g., 3000 for 0.3%)\",\"_weth9\":\"Weth9 address\"}},\"isToken(address)\":{\"params\":{\"token\":\"The token address to check\"},\"returns\":{\"exists\":\"True if the token exists\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setMoonbagsStake(address)\":{\"params\":{\"_moonbagsStake\":\"Address of the MoonbagsStake contract\"}},\"setTokenLock(address)\":{\"params\":{\"_tokenLock\":\"Address of the new TokenLock contract\"}},\"skim(address)\":{\"params\":{\"token\":\"Token address to skim\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"updateActiveFeeTier(uint24)\":{\"params\":{\"newFeeTier\":\"The new fee tier (500, 3000, or 10000)\"}},\"updateInitialVirtualTokenReserves(uint256)\":{\"params\":{\"_initialVirtualTokenReserves\":\"New initial virtual token reserves\"}}},\"title\":\"Moonbags Launchpad\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"buyExactIn(address,uint256,uint256)\":{\"notice\":\"Buy exact amount in and lock tokens for specified duration\"},\"collectHyperSwapFees(address)\":{\"notice\":\"Collect all fees from a HyperSwap V3 position\"},\"createAndLockFirstBuy(string,string,string,string,string,string,string,uint256,uint256,uint256)\":{\"notice\":\"Create a new pool with bonding curve and lock the first buy\"},\"createPool(string,string,string,string,string,string,string,uint256)\":{\"notice\":\"Create a new pool with bonding curve\"},\"distributeBondingCurveFees(address)\":{\"notice\":\"Distribute accumulated bonding curve fees for a token\"},\"initialize(address,address,uint24,address,address,address)\":{\"notice\":\"Initialize the contract\"},\"isToken(address)\":{\"notice\":\"Check if a token exists (has been created through the launchpad)\"},\"onERC721Received(address,address,uint256,bytes)\":{\"notice\":\"Handle NFT transfers (required for IERC721Receiver)\"},\"setMoonbagsStake(address)\":{\"notice\":\"Set the MoonbagsStake contract address (only owner)\"},\"setTokenLock(address)\":{\"notice\":\"Set TokenLock contract (only owner)\"},\"skim(address)\":{\"notice\":\"skim\"},\"updateActiveFeeTier(uint24)\":{\"notice\":\"Update the active fee tier for new pools (only owner)\"},\"updateConfigWithdrawFee(uint16,uint16,uint16,uint16)\":{\"notice\":\"Update the initial withdraw fees (only owner)\"},\"updateConfiguration(uint256,uint256,uint256,uint8,uint16,uint16,uint16,uint16,address)\":{\"notice\":\"Update configuration (only owner)\"},\"updateFeeRecipients(address,address)\":{\"notice\":\"Update fee recipients (only owner)\"},\"updateInitialVirtualTokenReserves(uint256)\":{\"notice\":\"Update the initial virtual token reserves (only owner)\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/MoonbagsLaunchpad.sol\":\"MoonbagsLaunchpad\"},\"evmVersion\":\"paris\",\"libraries\":{\":__CACHE_BREAKER__\":\"0x00000000d41867734bbee4c6863d9255b2b06ac1\"},\"metadata\":{\"bytecodeHash\":\"none\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {ContextUpgradeable} from \\\"../utils/ContextUpgradeable.sol\\\";\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * The initial owner is set to the address provided by the deployer. This can\\n * later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract OwnableUpgradeable is Initializable, ContextUpgradeable {\\n    /// @custom:storage-location erc7201:openzeppelin.storage.Ownable\\n    struct OwnableStorage {\\n        address _owner;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.Ownable\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant OwnableStorageLocation = 0x9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300;\\n\\n    function _getOwnableStorage() private pure returns (OwnableStorage storage $) {\\n        assembly {\\n            $.slot := OwnableStorageLocation\\n        }\\n    }\\n\\n    /**\\n     * @dev The caller account is not authorized to perform an operation.\\n     */\\n    error OwnableUnauthorizedAccount(address account);\\n\\n    /**\\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\\n     */\\n    error OwnableInvalidOwner(address owner);\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\\n     */\\n    function __Ownable_init(address initialOwner) internal onlyInitializing {\\n        __Ownable_init_unchained(initialOwner);\\n    }\\n\\n    function __Ownable_init_unchained(address initialOwner) internal onlyInitializing {\\n        if (initialOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(initialOwner);\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        OwnableStorage storage $ = _getOwnableStorage();\\n        return $._owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        if (owner() != _msgSender()) {\\n            revert OwnableUnauthorizedAccount(_msgSender());\\n        }\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        if (newOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        OwnableStorage storage $ = _getOwnableStorage();\\n        address oldOwner = $._owner;\\n        $._owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n}\\n\",\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (proxy/utils/Initializable.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\\n * behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\\n * external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\\n * function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\\n *\\n * The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\\n * reused. This mechanism prevents re-execution of each \\\"step\\\" but allows the creation of new initialization steps in\\n * case an upgrade adds a module that needs to be initialized.\\n *\\n * For example:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```solidity\\n * contract MyToken is ERC20Upgradeable {\\n *     function initialize() initializer public {\\n *         __ERC20_init(\\\"MyToken\\\", \\\"MTK\\\");\\n *     }\\n * }\\n *\\n * contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\\n *     function initializeV2() reinitializer(2) public {\\n *         __ERC20Permit_init(\\\"MyToken\\\");\\n *     }\\n * }\\n * ```\\n *\\n * TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\\n * possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\\n *\\n * CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\\n * that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\\n *\\n * [CAUTION]\\n * ====\\n * Avoid leaving a contract uninitialized.\\n *\\n * An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\\n * contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\\n * the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```\\n * /// @custom:oz-upgrades-unsafe-allow constructor\\n * constructor() {\\n *     _disableInitializers();\\n * }\\n * ```\\n * ====\\n */\\nabstract contract Initializable {\\n    /**\\n     * @dev Storage of the initializable contract.\\n     *\\n     * It's implemented on a custom ERC-7201 namespace to reduce the risk of storage collisions\\n     * when using with upgradeable contracts.\\n     *\\n     * @custom:storage-location erc7201:openzeppelin.storage.Initializable\\n     */\\n    struct InitializableStorage {\\n        /**\\n         * @dev Indicates that the contract has been initialized.\\n         */\\n        uint64 _initialized;\\n        /**\\n         * @dev Indicates that the contract is in the process of being initialized.\\n         */\\n        bool _initializing;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.Initializable\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant INITIALIZABLE_STORAGE = 0xf0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00;\\n\\n    /**\\n     * @dev The contract is already initialized.\\n     */\\n    error InvalidInitialization();\\n\\n    /**\\n     * @dev The contract is not initializing.\\n     */\\n    error NotInitializing();\\n\\n    /**\\n     * @dev Triggered when the contract has been initialized or reinitialized.\\n     */\\n    event Initialized(uint64 version);\\n\\n    /**\\n     * @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\\n     * `onlyInitializing` functions can be used to initialize parent contracts.\\n     *\\n     * Similar to `reinitializer(1)`, except that in the context of a constructor an `initializer` may be invoked any\\n     * number of times. This behavior in the constructor can be useful during testing and is not expected to be used in\\n     * production.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier initializer() {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        // Cache values to avoid duplicated sloads\\n        bool isTopLevelCall = !$._initializing;\\n        uint64 initialized = $._initialized;\\n\\n        // Allowed calls:\\n        // - initialSetup: the contract is not in the initializing state and no previous version was\\n        //                 initialized\\n        // - construction: the contract is initialized at version 1 (no reinitialization) and the\\n        //                 current contract is just being deployed\\n        bool initialSetup = initialized == 0 && isTopLevelCall;\\n        bool construction = initialized == 1 && address(this).code.length == 0;\\n\\n        if (!initialSetup && !construction) {\\n            revert InvalidInitialization();\\n        }\\n        $._initialized = 1;\\n        if (isTopLevelCall) {\\n            $._initializing = true;\\n        }\\n        _;\\n        if (isTopLevelCall) {\\n            $._initializing = false;\\n            emit Initialized(1);\\n        }\\n    }\\n\\n    /**\\n     * @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\\n     * contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\\n     * used to initialize parent contracts.\\n     *\\n     * A reinitializer may be used after the original initialization step. This is essential to configure modules that\\n     * are added through upgrades and that require initialization.\\n     *\\n     * When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\\n     * cannot be nested. If one is invoked in the context of another, execution will revert.\\n     *\\n     * Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\\n     * a contract, executing them in the right order is up to the developer or operator.\\n     *\\n     * WARNING: Setting the version to 2**64 - 1 will prevent any future reinitialization.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier reinitializer(uint64 version) {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        if ($._initializing || $._initialized >= version) {\\n            revert InvalidInitialization();\\n        }\\n        $._initialized = version;\\n        $._initializing = true;\\n        _;\\n        $._initializing = false;\\n        emit Initialized(version);\\n    }\\n\\n    /**\\n     * @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\\n     * {initializer} and {reinitializer} modifiers, directly or indirectly.\\n     */\\n    modifier onlyInitializing() {\\n        _checkInitializing();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Reverts if the contract is not in an initializing state. See {onlyInitializing}.\\n     */\\n    function _checkInitializing() internal view virtual {\\n        if (!_isInitializing()) {\\n            revert NotInitializing();\\n        }\\n    }\\n\\n    /**\\n     * @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\\n     * Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\\n     * to any version. It is recommended to use this to lock implementation contracts that are designed to be called\\n     * through proxies.\\n     *\\n     * Emits an {Initialized} event the first time it is successfully executed.\\n     */\\n    function _disableInitializers() internal virtual {\\n        // solhint-disable-next-line var-name-mixedcase\\n        InitializableStorage storage $ = _getInitializableStorage();\\n\\n        if ($._initializing) {\\n            revert InvalidInitialization();\\n        }\\n        if ($._initialized != type(uint64).max) {\\n            $._initialized = type(uint64).max;\\n            emit Initialized(type(uint64).max);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the highest version that has been initialized. See {reinitializer}.\\n     */\\n    function _getInitializedVersion() internal view returns (uint64) {\\n        return _getInitializableStorage()._initialized;\\n    }\\n\\n    /**\\n     * @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}.\\n     */\\n    function _isInitializing() internal view returns (bool) {\\n        return _getInitializableStorage()._initializing;\\n    }\\n\\n    /**\\n     * @dev Pointer to storage slot. Allows integrators to override it with a custom storage location.\\n     *\\n     * NOTE: Consider following the ERC-7201 formula to derive storage locations.\\n     */\\n    function _initializableStorageSlot() internal pure virtual returns (bytes32) {\\n        return INITIALIZABLE_STORAGE;\\n    }\\n\\n    /**\\n     * @dev Returns a pointer to the storage namespace.\\n     */\\n    // solhint-disable-next-line var-name-mixedcase\\n    function _getInitializableStorage() private pure returns (InitializableStorage storage $) {\\n        bytes32 slot = _initializableStorageSlot();\\n        assembly {\\n            $.slot := slot\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\\n\\npragma solidity ^0.8.20;\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract ContextUpgradeable is Initializable {\\n    function __Context_init() internal onlyInitializing {\\n    }\\n\\n    function __Context_init_unchained() internal onlyInitializing {\\n    }\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    function _contextSuffixLength() internal view virtual returns (uint256) {\\n        return 0;\\n    }\\n}\\n\",\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)\\n\\npragma solidity ^0.8.20;\\nimport {Initializable} from \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module that helps prevent reentrant calls to a function.\\n *\\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\\n * available, which can be applied to functions to make sure there are no nested\\n * (reentrant) calls to them.\\n *\\n * Note that because there is a single `nonReentrant` guard, functions marked as\\n * `nonReentrant` may not call one another. This can be worked around by making\\n * those functions `private`, and then adding `external` `nonReentrant` entry\\n * points to them.\\n *\\n * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\\n * consider using {ReentrancyGuardTransient} instead.\\n *\\n * TIP: If you would like to learn more about reentrancy and alternative ways\\n * to protect against it, check out our blog post\\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\\n */\\nabstract contract ReentrancyGuardUpgradeable is Initializable {\\n    // Booleans are more expensive than uint256 or any type that takes up a full\\n    // word because each write operation emits an extra SLOAD to first read the\\n    // slot's contents, replace the bits taken up by the boolean, and then write\\n    // back. This is the compiler's defense against contract upgrades and\\n    // pointer aliasing, and it cannot be disabled.\\n\\n    // The values being non-zero value makes deployment a bit more expensive,\\n    // but in exchange the refund on every call to nonReentrant will be lower in\\n    // amount. Since refunds are capped to a percentage of the total\\n    // transaction's gas, it is best to keep them low in cases like this one, to\\n    // increase the likelihood of the full refund coming into effect.\\n    uint256 private constant NOT_ENTERED = 1;\\n    uint256 private constant ENTERED = 2;\\n\\n    /// @custom:storage-location erc7201:openzeppelin.storage.ReentrancyGuard\\n    struct ReentrancyGuardStorage {\\n        uint256 _status;\\n    }\\n\\n    // keccak256(abi.encode(uint256(keccak256(\\\"openzeppelin.storage.ReentrancyGuard\\\")) - 1)) & ~bytes32(uint256(0xff))\\n    bytes32 private constant ReentrancyGuardStorageLocation = 0x9b779b17422d0df92223018b32b4d1fa46e071723d6817e2486d003becc55f00;\\n\\n    function _getReentrancyGuardStorage() private pure returns (ReentrancyGuardStorage storage $) {\\n        assembly {\\n            $.slot := ReentrancyGuardStorageLocation\\n        }\\n    }\\n\\n    /**\\n     * @dev Unauthorized reentrant call.\\n     */\\n    error ReentrancyGuardReentrantCall();\\n\\n    function __ReentrancyGuard_init() internal onlyInitializing {\\n        __ReentrancyGuard_init_unchained();\\n    }\\n\\n    function __ReentrancyGuard_init_unchained() internal onlyInitializing {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        $._status = NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and making it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        _nonReentrantBefore();\\n        _;\\n        _nonReentrantAfter();\\n    }\\n\\n    function _nonReentrantBefore() private {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        // On the first call to nonReentrant, _status will be NOT_ENTERED\\n        if ($._status == ENTERED) {\\n            revert ReentrancyGuardReentrantCall();\\n        }\\n\\n        // Any calls to nonReentrant after this point will fail\\n        $._status = ENTERED;\\n    }\\n\\n    function _nonReentrantAfter() private {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        $._status = NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Returns true if the reentrancy guard is currently set to \\\"entered\\\", which indicates there is a\\n     * `nonReentrant` function in the call stack.\\n     */\\n    function _reentrancyGuardEntered() internal view returns (bool) {\\n        ReentrancyGuardStorage storage $ = _getReentrancyGuardStorage();\\n        return $._status == ENTERED;\\n    }\\n}\\n\",\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\"},\"@openzeppelin/contracts/access/Ownable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {Context} from \\\"../utils/Context.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * The initial owner is set to the address provided by the deployer. This can\\n * later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract Ownable is Context {\\n    address private _owner;\\n\\n    /**\\n     * @dev The caller account is not authorized to perform an operation.\\n     */\\n    error OwnableUnauthorizedAccount(address account);\\n\\n    /**\\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\\n     */\\n    error OwnableInvalidOwner(address owner);\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\\n     */\\n    constructor(address initialOwner) {\\n        if (initialOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(initialOwner);\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        return _owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        if (owner() != _msgSender()) {\\n            revert OwnableUnauthorizedAccount(_msgSender());\\n        }\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        if (newOwner == address(0)) {\\n            revert OwnableInvalidOwner(address(0));\\n        }\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        address oldOwner = _owner;\\n        _owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n}\\n\",\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC1363.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (interfaces/IERC1363.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"./IERC20.sol\\\";\\nimport {IERC165} from \\\"./IERC165.sol\\\";\\n\\n/**\\n * @title IERC1363\\n * @dev Interface of the ERC-1363 standard as defined in the https://eips.ethereum.org/EIPS/eip-1363[ERC-1363].\\n *\\n * Defines an extension interface for ERC-20 tokens that supports executing code on a recipient contract\\n * after `transfer` or `transferFrom`, or code on a spender contract after `approve`, in a single transaction.\\n */\\ninterface IERC1363 is IERC20, IERC165 {\\n    /*\\n     * Note: the ERC-165 identifier for this interface is 0xb0202a11.\\n     * 0xb0202a11 ===\\n     *   bytes4(keccak256('transferAndCall(address,uint256)')) ^\\n     *   bytes4(keccak256('transferAndCall(address,uint256,bytes)')) ^\\n     *   bytes4(keccak256('transferFromAndCall(address,address,uint256)')) ^\\n     *   bytes4(keccak256('transferFromAndCall(address,address,uint256,bytes)')) ^\\n     *   bytes4(keccak256('approveAndCall(address,uint256)')) ^\\n     *   bytes4(keccak256('approveAndCall(address,uint256,bytes)'))\\n     */\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferAndCall(address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @param data Additional data with no specified format, sent in call to `to`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferAndCall(address to, uint256 value, bytes calldata data) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param from The address which you want to send tokens from.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferFromAndCall(address from, address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism\\n     * and then calls {IERC1363Receiver-onTransferReceived} on `to`.\\n     * @param from The address which you want to send tokens from.\\n     * @param to The address which you want to transfer to.\\n     * @param value The amount of tokens to be transferred.\\n     * @param data Additional data with no specified format, sent in call to `to`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function transferFromAndCall(address from, address to, uint256 value, bytes calldata data) external returns (bool);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens and then calls {IERC1363Spender-onApprovalReceived} on `spender`.\\n     * @param spender The address which will spend the funds.\\n     * @param value The amount of tokens to be spent.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function approveAndCall(address spender, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens and then calls {IERC1363Spender-onApprovalReceived} on `spender`.\\n     * @param spender The address which will spend the funds.\\n     * @param value The amount of tokens to be spent.\\n     * @param data Additional data with no specified format, sent in call to `spender`.\\n     * @return A boolean value indicating whether the operation succeeded unless throwing.\\n     */\\n    function approveAndCall(address spender, uint256 value, bytes calldata data) external returns (bool);\\n}\\n\",\"keccak256\":\"0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC165} from \\\"../utils/introspection/IERC165.sol\\\";\\n\",\"keccak256\":\"0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"../token/ERC20/IERC20.sol\\\";\\n\",\"keccak256\":\"0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c\",\"license\":\"MIT\"},\"@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (interfaces/draft-IERC6093.sol)\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Standard ERC-20 Errors\\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-20 tokens.\\n */\\ninterface IERC20Errors {\\n    /**\\n     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     * @param balance Current balance for the interacting account.\\n     * @param needed Minimum amount required to perform a transfer.\\n     */\\n    error ERC20InsufficientBalance(address sender, uint256 balance, uint256 needed);\\n\\n    /**\\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     */\\n    error ERC20InvalidSender(address sender);\\n\\n    /**\\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\\n     * @param receiver Address to which tokens are being transferred.\\n     */\\n    error ERC20InvalidReceiver(address receiver);\\n\\n    /**\\n     * @dev Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\\n     * @param spender Address that may be allowed to operate on tokens without being their owner.\\n     * @param allowance Amount of tokens a `spender` is allowed to operate with.\\n     * @param needed Minimum amount required to perform a transfer.\\n     */\\n    error ERC20InsufficientAllowance(address spender, uint256 allowance, uint256 needed);\\n\\n    /**\\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\\n     * @param approver Address initiating an approval operation.\\n     */\\n    error ERC20InvalidApprover(address approver);\\n\\n    /**\\n     * @dev Indicates a failure with the `spender` to be approved. Used in approvals.\\n     * @param spender Address that may be allowed to operate on tokens without being their owner.\\n     */\\n    error ERC20InvalidSpender(address spender);\\n}\\n\\n/**\\n * @dev Standard ERC-721 Errors\\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-721 tokens.\\n */\\ninterface IERC721Errors {\\n    /**\\n     * @dev Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in ERC-20.\\n     * Used in balance queries.\\n     * @param owner Address of the current owner of a token.\\n     */\\n    error ERC721InvalidOwner(address owner);\\n\\n    /**\\n     * @dev Indicates a `tokenId` whose `owner` is the zero address.\\n     * @param tokenId Identifier number of a token.\\n     */\\n    error ERC721NonexistentToken(uint256 tokenId);\\n\\n    /**\\n     * @dev Indicates an error related to the ownership over a particular token. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     * @param tokenId Identifier number of a token.\\n     * @param owner Address of the current owner of a token.\\n     */\\n    error ERC721IncorrectOwner(address sender, uint256 tokenId, address owner);\\n\\n    /**\\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     */\\n    error ERC721InvalidSender(address sender);\\n\\n    /**\\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\\n     * @param receiver Address to which tokens are being transferred.\\n     */\\n    error ERC721InvalidReceiver(address receiver);\\n\\n    /**\\n     * @dev Indicates a failure with the `operator`\\u2019s approval. Used in transfers.\\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\\n     * @param tokenId Identifier number of a token.\\n     */\\n    error ERC721InsufficientApproval(address operator, uint256 tokenId);\\n\\n    /**\\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\\n     * @param approver Address initiating an approval operation.\\n     */\\n    error ERC721InvalidApprover(address approver);\\n\\n    /**\\n     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.\\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\\n     */\\n    error ERC721InvalidOperator(address operator);\\n}\\n\\n/**\\n * @dev Standard ERC-1155 Errors\\n * Interface of the https://eips.ethereum.org/EIPS/eip-6093[ERC-6093] custom errors for ERC-1155 tokens.\\n */\\ninterface IERC1155Errors {\\n    /**\\n     * @dev Indicates an error related to the current `balance` of a `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     * @param balance Current balance for the interacting account.\\n     * @param needed Minimum amount required to perform a transfer.\\n     * @param tokenId Identifier number of a token.\\n     */\\n    error ERC1155InsufficientBalance(address sender, uint256 balance, uint256 needed, uint256 tokenId);\\n\\n    /**\\n     * @dev Indicates a failure with the token `sender`. Used in transfers.\\n     * @param sender Address whose tokens are being transferred.\\n     */\\n    error ERC1155InvalidSender(address sender);\\n\\n    /**\\n     * @dev Indicates a failure with the token `receiver`. Used in transfers.\\n     * @param receiver Address to which tokens are being transferred.\\n     */\\n    error ERC1155InvalidReceiver(address receiver);\\n\\n    /**\\n     * @dev Indicates a failure with the `operator`\\u2019s approval. Used in transfers.\\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\\n     * @param owner Address of the current owner of a token.\\n     */\\n    error ERC1155MissingApprovalForAll(address operator, address owner);\\n\\n    /**\\n     * @dev Indicates a failure with the `approver` of a token to be approved. Used in approvals.\\n     * @param approver Address initiating an approval operation.\\n     */\\n    error ERC1155InvalidApprover(address approver);\\n\\n    /**\\n     * @dev Indicates a failure with the `operator` to be approved. Used in approvals.\\n     * @param operator Address that may be allowed to operate on tokens without being their owner.\\n     */\\n    error ERC1155InvalidOperator(address operator);\\n\\n    /**\\n     * @dev Indicates an array length mismatch between ids and values in a safeBatchTransferFrom operation.\\n     * Used in batch transfers.\\n     * @param idsLength Length of the array of token identifiers\\n     * @param valuesLength Length of the array of token amounts\\n     */\\n    error ERC1155InvalidArrayLength(uint256 idsLength, uint256 valuesLength);\\n}\\n\",\"keccak256\":\"0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (token/ERC20/ERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"./IERC20.sol\\\";\\nimport {IERC20Metadata} from \\\"./extensions/IERC20Metadata.sol\\\";\\nimport {Context} from \\\"../../utils/Context.sol\\\";\\nimport {IERC20Errors} from \\\"../../interfaces/draft-IERC6093.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC20} interface.\\n *\\n * This implementation is agnostic to the way tokens are created. This means\\n * that a supply mechanism has to be added in a derived contract using {_mint}.\\n *\\n * TIP: For a detailed writeup see our guide\\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\\n * to implement supply mechanisms].\\n *\\n * The default value of {decimals} is 18. To change this, you should override\\n * this function so it returns a different value.\\n *\\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\\n * instead returning `false` on failure. This behavior is nonetheless\\n * conventional and does not conflict with the expectations of ERC-20\\n * applications.\\n */\\nabstract contract ERC20 is Context, IERC20, IERC20Metadata, IERC20Errors {\\n    mapping(address account => uint256) private _balances;\\n\\n    mapping(address account => mapping(address spender => uint256)) private _allowances;\\n\\n    uint256 private _totalSupply;\\n\\n    string private _name;\\n    string private _symbol;\\n\\n    /**\\n     * @dev Sets the values for {name} and {symbol}.\\n     *\\n     * Both values are immutable: they can only be set once during construction.\\n     */\\n    constructor(string memory name_, string memory symbol_) {\\n        _name = name_;\\n        _symbol = symbol_;\\n    }\\n\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() public view virtual returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev Returns the symbol of the token, usually a shorter version of the\\n     * name.\\n     */\\n    function symbol() public view virtual returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev Returns the number of decimals used to get its user representation.\\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n     * be displayed to a user as `5.05` (`505 / 10 ** 2`).\\n     *\\n     * Tokens usually opt for a value of 18, imitating the relationship between\\n     * Ether and Wei. This is the default value returned by this function, unless\\n     * it's overridden.\\n     *\\n     * NOTE: This information is only used for _display_ purposes: it in\\n     * no way affects any of the arithmetic of the contract, including\\n     * {IERC20-balanceOf} and {IERC20-transfer}.\\n     */\\n    function decimals() public view virtual returns (uint8) {\\n        return 18;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-totalSupply}.\\n     */\\n    function totalSupply() public view virtual returns (uint256) {\\n        return _totalSupply;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-balanceOf}.\\n     */\\n    function balanceOf(address account) public view virtual returns (uint256) {\\n        return _balances[account];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transfer}.\\n     *\\n     * Requirements:\\n     *\\n     * - `to` cannot be the zero address.\\n     * - the caller must have a balance of at least `value`.\\n     */\\n    function transfer(address to, uint256 value) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        _transfer(owner, to, value);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-allowance}.\\n     */\\n    function allowance(address owner, address spender) public view virtual returns (uint256) {\\n        return _allowances[owner][spender];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-approve}.\\n     *\\n     * NOTE: If `value` is the maximum `uint256`, the allowance is not updated on\\n     * `transferFrom`. This is semantically equivalent to an infinite approval.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     */\\n    function approve(address spender, uint256 value) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        _approve(owner, spender, value);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transferFrom}.\\n     *\\n     * Skips emitting an {Approval} event indicating an allowance update. This is not\\n     * required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve].\\n     *\\n     * NOTE: Does not update the allowance if the current allowance\\n     * is the maximum `uint256`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` and `to` cannot be the zero address.\\n     * - `from` must have a balance of at least `value`.\\n     * - the caller must have allowance for ``from``'s tokens of at least\\n     * `value`.\\n     */\\n    function transferFrom(address from, address to, uint256 value) public virtual returns (bool) {\\n        address spender = _msgSender();\\n        _spendAllowance(from, spender, value);\\n        _transfer(from, to, value);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to`.\\n     *\\n     * This internal function is equivalent to {transfer}, and can be used to\\n     * e.g. implement automatic token fees, slashing mechanisms, etc.\\n     *\\n     * Emits a {Transfer} event.\\n     *\\n     * NOTE: This function is not virtual, {_update} should be overridden instead.\\n     */\\n    function _transfer(address from, address to, uint256 value) internal {\\n        if (from == address(0)) {\\n            revert ERC20InvalidSender(address(0));\\n        }\\n        if (to == address(0)) {\\n            revert ERC20InvalidReceiver(address(0));\\n        }\\n        _update(from, to, value);\\n    }\\n\\n    /**\\n     * @dev Transfers a `value` amount of tokens from `from` to `to`, or alternatively mints (or burns) if `from`\\n     * (or `to`) is the zero address. All customizations to transfers, mints, and burns should be done by overriding\\n     * this function.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _update(address from, address to, uint256 value) internal virtual {\\n        if (from == address(0)) {\\n            // Overflow check required: The rest of the code assumes that totalSupply never overflows\\n            _totalSupply += value;\\n        } else {\\n            uint256 fromBalance = _balances[from];\\n            if (fromBalance < value) {\\n                revert ERC20InsufficientBalance(from, fromBalance, value);\\n            }\\n            unchecked {\\n                // Overflow not possible: value <= fromBalance <= totalSupply.\\n                _balances[from] = fromBalance - value;\\n            }\\n        }\\n\\n        if (to == address(0)) {\\n            unchecked {\\n                // Overflow not possible: value <= totalSupply or value <= fromBalance <= totalSupply.\\n                _totalSupply -= value;\\n            }\\n        } else {\\n            unchecked {\\n                // Overflow not possible: balance + value is at most totalSupply, which we know fits into a uint256.\\n                _balances[to] += value;\\n            }\\n        }\\n\\n        emit Transfer(from, to, value);\\n    }\\n\\n    /**\\n     * @dev Creates a `value` amount of tokens and assigns them to `account`, by transferring it from address(0).\\n     * Relies on the `_update` mechanism\\n     *\\n     * Emits a {Transfer} event with `from` set to the zero address.\\n     *\\n     * NOTE: This function is not virtual, {_update} should be overridden instead.\\n     */\\n    function _mint(address account, uint256 value) internal {\\n        if (account == address(0)) {\\n            revert ERC20InvalidReceiver(address(0));\\n        }\\n        _update(address(0), account, value);\\n    }\\n\\n    /**\\n     * @dev Destroys a `value` amount of tokens from `account`, lowering the total supply.\\n     * Relies on the `_update` mechanism.\\n     *\\n     * Emits a {Transfer} event with `to` set to the zero address.\\n     *\\n     * NOTE: This function is not virtual, {_update} should be overridden instead\\n     */\\n    function _burn(address account, uint256 value) internal {\\n        if (account == address(0)) {\\n            revert ERC20InvalidSender(address(0));\\n        }\\n        _update(account, address(0), value);\\n    }\\n\\n    /**\\n     * @dev Sets `value` as the allowance of `spender` over the `owner`'s tokens.\\n     *\\n     * This internal function is equivalent to `approve`, and can be used to\\n     * e.g. set automatic allowances for certain subsystems, etc.\\n     *\\n     * Emits an {Approval} event.\\n     *\\n     * Requirements:\\n     *\\n     * - `owner` cannot be the zero address.\\n     * - `spender` cannot be the zero address.\\n     *\\n     * Overrides to this logic should be done to the variant with an additional `bool emitEvent` argument.\\n     */\\n    function _approve(address owner, address spender, uint256 value) internal {\\n        _approve(owner, spender, value, true);\\n    }\\n\\n    /**\\n     * @dev Variant of {_approve} with an optional flag to enable or disable the {Approval} event.\\n     *\\n     * By default (when calling {_approve}) the flag is set to true. On the other hand, approval changes made by\\n     * `_spendAllowance` during the `transferFrom` operation set the flag to false. This saves gas by not emitting any\\n     * `Approval` event during `transferFrom` operations.\\n     *\\n     * Anyone who wishes to continue emitting `Approval` events on the`transferFrom` operation can force the flag to\\n     * true using the following override:\\n     *\\n     * ```solidity\\n     * function _approve(address owner, address spender, uint256 value, bool) internal virtual override {\\n     *     super._approve(owner, spender, value, true);\\n     * }\\n     * ```\\n     *\\n     * Requirements are the same as {_approve}.\\n     */\\n    function _approve(address owner, address spender, uint256 value, bool emitEvent) internal virtual {\\n        if (owner == address(0)) {\\n            revert ERC20InvalidApprover(address(0));\\n        }\\n        if (spender == address(0)) {\\n            revert ERC20InvalidSpender(address(0));\\n        }\\n        _allowances[owner][spender] = value;\\n        if (emitEvent) {\\n            emit Approval(owner, spender, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Updates `owner`'s allowance for `spender` based on spent `value`.\\n     *\\n     * Does not update the allowance value in case of infinite allowance.\\n     * Revert if not enough allowance is available.\\n     *\\n     * Does not emit an {Approval} event.\\n     */\\n    function _spendAllowance(address owner, address spender, uint256 value) internal virtual {\\n        uint256 currentAllowance = allowance(owner, spender);\\n        if (currentAllowance < type(uint256).max) {\\n            if (currentAllowance < value) {\\n                revert ERC20InsufficientAllowance(spender, currentAllowance, value);\\n            }\\n            unchecked {\\n                _approve(owner, spender, currentAllowance - value, false);\\n            }\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x41f6b3b9e030561e7896dbef372b499cc8d418a80c3884a4d65a68f2fdc7493a\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC-20 standard as defined in the ERC.\\n */\\ninterface IERC20 {\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n    /**\\n     * @dev Returns the value of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the value of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\\n     * allowance mechanism. `value` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\\n}\\n\",\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"../IERC20.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC-20 standard.\\n */\\ninterface IERC20Metadata is IERC20 {\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the symbol of the token.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the decimals places of the token.\\n     */\\n    function decimals() external view returns (uint8);\\n}\\n\",\"keccak256\":\"0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (token/ERC20/utils/SafeERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC20} from \\\"../IERC20.sol\\\";\\nimport {IERC1363} from \\\"../../../interfaces/IERC1363.sol\\\";\\n\\n/**\\n * @title SafeERC20\\n * @dev Wrappers around ERC-20 operations that throw on failure (when the token\\n * contract returns false). Tokens that return no value (and instead revert or\\n * throw on failure) are also supported, non-reverting calls are assumed to be\\n * successful.\\n * To use this library you can add a `using SafeERC20 for IERC20;` statement to your contract,\\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\\n */\\nlibrary SafeERC20 {\\n    /**\\n     * @dev An operation with an ERC-20 token failed.\\n     */\\n    error SafeERC20FailedOperation(address token);\\n\\n    /**\\n     * @dev Indicates a failed `decreaseAllowance` request.\\n     */\\n    error SafeERC20FailedDecreaseAllowance(address spender, uint256 currentAllowance, uint256 requestedDecrease);\\n\\n    /**\\n     * @dev Transfer `value` amount of `token` from the calling contract to `to`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful.\\n     */\\n    function safeTransfer(IERC20 token, address to, uint256 value) internal {\\n        _callOptionalReturn(token, abi.encodeCall(token.transfer, (to, value)));\\n    }\\n\\n    /**\\n     * @dev Transfer `value` amount of `token` from `from` to `to`, spending the approval given by `from` to the\\n     * calling contract. If `token` returns no value, non-reverting calls are assumed to be successful.\\n     */\\n    function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\\n        _callOptionalReturn(token, abi.encodeCall(token.transferFrom, (from, to, value)));\\n    }\\n\\n    /**\\n     * @dev Variant of {safeTransfer} that returns a bool instead of reverting if the operation is not successful.\\n     */\\n    function trySafeTransfer(IERC20 token, address to, uint256 value) internal returns (bool) {\\n        return _callOptionalReturnBool(token, abi.encodeCall(token.transfer, (to, value)));\\n    }\\n\\n    /**\\n     * @dev Variant of {safeTransferFrom} that returns a bool instead of reverting if the operation is not successful.\\n     */\\n    function trySafeTransferFrom(IERC20 token, address from, address to, uint256 value) internal returns (bool) {\\n        return _callOptionalReturnBool(token, abi.encodeCall(token.transferFrom, (from, to, value)));\\n    }\\n\\n    /**\\n     * @dev Increase the calling contract's allowance toward `spender` by `value`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful.\\n     *\\n     * IMPORTANT: If the token implements ERC-7674 (ERC-20 with temporary allowance), and if the \\\"client\\\"\\n     * smart contract uses ERC-7674 to set temporary allowances, then the \\\"client\\\" smart contract should avoid using\\n     * this function. Performing a {safeIncreaseAllowance} or {safeDecreaseAllowance} operation on a token contract\\n     * that has a non-zero temporary allowance (for that particular owner-spender) will result in unexpected behavior.\\n     */\\n    function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n        uint256 oldAllowance = token.allowance(address(this), spender);\\n        forceApprove(token, spender, oldAllowance + value);\\n    }\\n\\n    /**\\n     * @dev Decrease the calling contract's allowance toward `spender` by `requestedDecrease`. If `token` returns no\\n     * value, non-reverting calls are assumed to be successful.\\n     *\\n     * IMPORTANT: If the token implements ERC-7674 (ERC-20 with temporary allowance), and if the \\\"client\\\"\\n     * smart contract uses ERC-7674 to set temporary allowances, then the \\\"client\\\" smart contract should avoid using\\n     * this function. Performing a {safeIncreaseAllowance} or {safeDecreaseAllowance} operation on a token contract\\n     * that has a non-zero temporary allowance (for that particular owner-spender) will result in unexpected behavior.\\n     */\\n    function safeDecreaseAllowance(IERC20 token, address spender, uint256 requestedDecrease) internal {\\n        unchecked {\\n            uint256 currentAllowance = token.allowance(address(this), spender);\\n            if (currentAllowance < requestedDecrease) {\\n                revert SafeERC20FailedDecreaseAllowance(spender, currentAllowance, requestedDecrease);\\n            }\\n            forceApprove(token, spender, currentAllowance - requestedDecrease);\\n        }\\n    }\\n\\n    /**\\n     * @dev Set the calling contract's allowance toward `spender` to `value`. If `token` returns no value,\\n     * non-reverting calls are assumed to be successful. Meant to be used with tokens that require the approval\\n     * to be set to zero before setting it to a non-zero value, such as USDT.\\n     *\\n     * NOTE: If the token implements ERC-7674, this function will not modify any temporary allowance. This function\\n     * only sets the \\\"standard\\\" allowance. Any temporary allowance will remain active, in addition to the value being\\n     * set here.\\n     */\\n    function forceApprove(IERC20 token, address spender, uint256 value) internal {\\n        bytes memory approvalCall = abi.encodeCall(token.approve, (spender, value));\\n\\n        if (!_callOptionalReturnBool(token, approvalCall)) {\\n            _callOptionalReturn(token, abi.encodeCall(token.approve, (spender, 0)));\\n            _callOptionalReturn(token, approvalCall);\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} transferAndCall, with a fallback to the simple {ERC20} transfer if the target has no\\n     * code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function transferAndCallRelaxed(IERC1363 token, address to, uint256 value, bytes memory data) internal {\\n        if (to.code.length == 0) {\\n            safeTransfer(token, to, value);\\n        } else if (!token.transferAndCall(to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} transferFromAndCall, with a fallback to the simple {ERC20} transferFrom if the target\\n     * has no code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function transferFromAndCallRelaxed(\\n        IERC1363 token,\\n        address from,\\n        address to,\\n        uint256 value,\\n        bytes memory data\\n    ) internal {\\n        if (to.code.length == 0) {\\n            safeTransferFrom(token, from, to, value);\\n        } else if (!token.transferFromAndCall(from, to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Performs an {ERC1363} approveAndCall, with a fallback to the simple {ERC20} approve if the target has no\\n     * code. This can be used to implement an {ERC721}-like safe transfer that rely on {ERC1363} checks when\\n     * targeting contracts.\\n     *\\n     * NOTE: When the recipient address (`to`) has no code (i.e. is an EOA), this function behaves as {forceApprove}.\\n     * Opposedly, when the recipient address (`to`) has code, this function only attempts to call {ERC1363-approveAndCall}\\n     * once without retrying, and relies on the returned value to be true.\\n     *\\n     * Reverts if the returned value is other than `true`.\\n     */\\n    function approveAndCallRelaxed(IERC1363 token, address to, uint256 value, bytes memory data) internal {\\n        if (to.code.length == 0) {\\n            forceApprove(token, to, value);\\n        } else if (!token.approveAndCall(to, value, data)) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\\n     * @param token The token targeted by the call.\\n     * @param data The call data (encoded using abi.encode or one of its variants).\\n     *\\n     * This is a variant of {_callOptionalReturnBool} that reverts if call fails to meet the requirements.\\n     */\\n    function _callOptionalReturn(IERC20 token, bytes memory data) private {\\n        uint256 returnSize;\\n        uint256 returnValue;\\n        assembly (\\\"memory-safe\\\") {\\n            let success := call(gas(), token, 0, add(data, 0x20), mload(data), 0, 0x20)\\n            // bubble errors\\n            if iszero(success) {\\n                let ptr := mload(0x40)\\n                returndatacopy(ptr, 0, returndatasize())\\n                revert(ptr, returndatasize())\\n            }\\n            returnSize := returndatasize()\\n            returnValue := mload(0)\\n        }\\n\\n        if (returnSize == 0 ? address(token).code.length == 0 : returnValue != 1) {\\n            revert SafeERC20FailedOperation(address(token));\\n        }\\n    }\\n\\n    /**\\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\\n     * @param token The token targeted by the call.\\n     * @param data The call data (encoded using abi.encode or one of its variants).\\n     *\\n     * This is a variant of {_callOptionalReturn} that silently catches all reverts and returns a bool instead.\\n     */\\n    function _callOptionalReturnBool(IERC20 token, bytes memory data) private returns (bool) {\\n        bool success;\\n        uint256 returnSize;\\n        uint256 returnValue;\\n        assembly (\\\"memory-safe\\\") {\\n            success := call(gas(), token, 0, add(data, 0x20), mload(data), 0, 0x20)\\n            returnSize := returndatasize()\\n            returnValue := mload(0)\\n        }\\n        return success && (returnSize == 0 ? address(token).code.length > 0 : returnValue == 1);\\n    }\\n}\\n\",\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (token/ERC721/IERC721Receiver.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @title ERC-721 token receiver interface\\n * @dev Interface for any contract that wants to support safeTransfers\\n * from ERC-721 asset contracts.\\n */\\ninterface IERC721Receiver {\\n    /**\\n     * @dev Whenever an {IERC721} `tokenId` token is transferred to this contract via {IERC721-safeTransferFrom}\\n     * by `operator` from `from`, this function is called.\\n     *\\n     * It must return its Solidity selector to confirm the token transfer.\\n     * If any other value is returned or the interface is not implemented by the recipient, the transfer will be\\n     * reverted.\\n     *\\n     * The selector can be obtained in Solidity with `IERC721Receiver.onERC721Received.selector`.\\n     */\\n    function onERC721Received(\\n        address operator,\\n        address from,\\n        uint256 tokenId,\\n        bytes calldata data\\n    ) external returns (bytes4);\\n}\\n\",\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    function _contextSuffixLength() internal view virtual returns (uint256) {\\n        return 0;\\n    }\\n}\\n\",\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Panic.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/Panic.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Helper library for emitting standardized panic codes.\\n *\\n * ```solidity\\n * contract Example {\\n *      using Panic for uint256;\\n *\\n *      // Use any of the declared internal constants\\n *      function foo() { Panic.GENERIC.panic(); }\\n *\\n *      // Alternatively\\n *      function foo() { Panic.panic(Panic.GENERIC); }\\n * }\\n * ```\\n *\\n * Follows the list from https://github.com/ethereum/solidity/blob/v0.8.24/libsolutil/ErrorCodes.h[libsolutil].\\n *\\n * _Available since v5.1._\\n */\\n// slither-disable-next-line unused-state\\nlibrary Panic {\\n    /// @dev generic / unspecified error\\n    uint256 internal constant GENERIC = 0x00;\\n    /// @dev used by the assert() builtin\\n    uint256 internal constant ASSERT = 0x01;\\n    /// @dev arithmetic underflow or overflow\\n    uint256 internal constant UNDER_OVERFLOW = 0x11;\\n    /// @dev division or modulo by zero\\n    uint256 internal constant DIVISION_BY_ZERO = 0x12;\\n    /// @dev enum conversion error\\n    uint256 internal constant ENUM_CONVERSION_ERROR = 0x21;\\n    /// @dev invalid encoding in storage\\n    uint256 internal constant STORAGE_ENCODING_ERROR = 0x22;\\n    /// @dev empty array pop\\n    uint256 internal constant EMPTY_ARRAY_POP = 0x31;\\n    /// @dev array out of bounds access\\n    uint256 internal constant ARRAY_OUT_OF_BOUNDS = 0x32;\\n    /// @dev resource error (too large allocation or too large array)\\n    uint256 internal constant RESOURCE_ERROR = 0x41;\\n    /// @dev calling invalid internal function\\n    uint256 internal constant INVALID_INTERNAL_FUNCTION = 0x51;\\n\\n    /// @dev Reverts with a panic code. Recommended to use with\\n    /// the internal constants with predefined codes.\\n    function panic(uint256 code) internal pure {\\n        assembly (\\\"memory-safe\\\") {\\n            mstore(0x00, 0x4e487b71)\\n            mstore(0x20, code)\\n            revert(0x1c, 0x24)\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC-165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[ERC].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\",\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/math/Math.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.3.0) (utils/math/Math.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {Panic} from \\\"../Panic.sol\\\";\\nimport {SafeCast} from \\\"./SafeCast.sol\\\";\\n\\n/**\\n * @dev Standard math utilities missing in the Solidity language.\\n */\\nlibrary Math {\\n    enum Rounding {\\n        Floor, // Toward negative infinity\\n        Ceil, // Toward positive infinity\\n        Trunc, // Toward zero\\n        Expand // Away from zero\\n    }\\n\\n    /**\\n     * @dev Return the 512-bit addition of two uint256.\\n     *\\n     * The result is stored in two 256 variables such that sum = high * 2\\u00b2\\u2075\\u2076 + low.\\n     */\\n    function add512(uint256 a, uint256 b) internal pure returns (uint256 high, uint256 low) {\\n        assembly (\\\"memory-safe\\\") {\\n            low := add(a, b)\\n            high := lt(low, a)\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the 512-bit multiplication of two uint256.\\n     *\\n     * The result is stored in two 256 variables such that product = high * 2\\u00b2\\u2075\\u2076 + low.\\n     */\\n    function mul512(uint256 a, uint256 b) internal pure returns (uint256 high, uint256 low) {\\n        // 512-bit multiply [high low] = x * y. Compute the product mod 2\\u00b2\\u2075\\u2076 and mod 2\\u00b2\\u2075\\u2076 - 1, then use\\n        // the Chinese Remainder Theorem to reconstruct the 512 bit result. The result is stored in two 256\\n        // variables such that product = high * 2\\u00b2\\u2075\\u2076 + low.\\n        assembly (\\\"memory-safe\\\") {\\n            let mm := mulmod(a, b, not(0))\\n            low := mul(a, b)\\n            high := sub(sub(mm, low), lt(mm, low))\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the addition of two unsigned integers, with a success flag (no overflow).\\n     */\\n    function tryAdd(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            uint256 c = a + b;\\n            success = c >= a;\\n            result = c * SafeCast.toUint(success);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, with a success flag (no overflow).\\n     */\\n    function trySub(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            uint256 c = a - b;\\n            success = c <= a;\\n            result = c * SafeCast.toUint(success);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the multiplication of two unsigned integers, with a success flag (no overflow).\\n     */\\n    function tryMul(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            uint256 c = a * b;\\n            assembly (\\\"memory-safe\\\") {\\n                // Only true when the multiplication doesn't overflow\\n                // (c / a == b) || (a == 0)\\n                success := or(eq(div(c, a), b), iszero(a))\\n            }\\n            // equivalent to: success ? c : 0\\n            result = c * SafeCast.toUint(success);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the division of two unsigned integers, with a success flag (no division by zero).\\n     */\\n    function tryDiv(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            success = b > 0;\\n            assembly (\\\"memory-safe\\\") {\\n                // The `DIV` opcode returns zero when the denominator is 0.\\n                result := div(a, b)\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers, with a success flag (no division by zero).\\n     */\\n    function tryMod(uint256 a, uint256 b) internal pure returns (bool success, uint256 result) {\\n        unchecked {\\n            success = b > 0;\\n            assembly (\\\"memory-safe\\\") {\\n                // The `MOD` opcode returns zero when the denominator is 0.\\n                result := mod(a, b)\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Unsigned saturating addition, bounds to `2\\u00b2\\u2075\\u2076 - 1` instead of overflowing.\\n     */\\n    function saturatingAdd(uint256 a, uint256 b) internal pure returns (uint256) {\\n        (bool success, uint256 result) = tryAdd(a, b);\\n        return ternary(success, result, type(uint256).max);\\n    }\\n\\n    /**\\n     * @dev Unsigned saturating subtraction, bounds to zero instead of overflowing.\\n     */\\n    function saturatingSub(uint256 a, uint256 b) internal pure returns (uint256) {\\n        (, uint256 result) = trySub(a, b);\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Unsigned saturating multiplication, bounds to `2\\u00b2\\u2075\\u2076 - 1` instead of overflowing.\\n     */\\n    function saturatingMul(uint256 a, uint256 b) internal pure returns (uint256) {\\n        (bool success, uint256 result) = tryMul(a, b);\\n        return ternary(success, result, type(uint256).max);\\n    }\\n\\n    /**\\n     * @dev Branchless ternary evaluation for `a ? b : c`. Gas costs are constant.\\n     *\\n     * IMPORTANT: This function may reduce bytecode size and consume less gas when used standalone.\\n     * However, the compiler may optimize Solidity ternary operations (i.e. `a ? b : c`) to only compute\\n     * one branch when needed, making this function more expensive.\\n     */\\n    function ternary(bool condition, uint256 a, uint256 b) internal pure returns (uint256) {\\n        unchecked {\\n            // branchless ternary works because:\\n            // b ^ (a ^ b) == a\\n            // b ^ 0 == b\\n            return b ^ ((a ^ b) * SafeCast.toUint(condition));\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the largest of two numbers.\\n     */\\n    function max(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return ternary(a > b, a, b);\\n    }\\n\\n    /**\\n     * @dev Returns the smallest of two numbers.\\n     */\\n    function min(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return ternary(a < b, a, b);\\n    }\\n\\n    /**\\n     * @dev Returns the average of two numbers. The result is rounded towards\\n     * zero.\\n     */\\n    function average(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // (a + b) / 2 can overflow.\\n        return (a & b) + (a ^ b) / 2;\\n    }\\n\\n    /**\\n     * @dev Returns the ceiling of the division of two numbers.\\n     *\\n     * This differs from standard division with `/` in that it rounds towards infinity instead\\n     * of rounding towards zero.\\n     */\\n    function ceilDiv(uint256 a, uint256 b) internal pure returns (uint256) {\\n        if (b == 0) {\\n            // Guarantee the same behavior as in a regular Solidity division.\\n            Panic.panic(Panic.DIVISION_BY_ZERO);\\n        }\\n\\n        // The following calculation ensures accurate ceiling division without overflow.\\n        // Since a is non-zero, (a - 1) / b will not overflow.\\n        // The largest possible result occurs when (a - 1) / b is type(uint256).max,\\n        // but the largest value we can obtain is type(uint256).max - 1, which happens\\n        // when a = type(uint256).max and b = 1.\\n        unchecked {\\n            return SafeCast.toUint(a > 0) * ((a - 1) / b + 1);\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculates floor(x * y / denominator) with full precision. Throws if result overflows a uint256 or\\n     * denominator == 0.\\n     *\\n     * Original credit to Remco Bloemen under MIT license (https://xn--2-umb.com/21/muldiv) with further edits by\\n     * Uniswap Labs also under MIT license.\\n     */\\n    function mulDiv(uint256 x, uint256 y, uint256 denominator) internal pure returns (uint256 result) {\\n        unchecked {\\n            (uint256 high, uint256 low) = mul512(x, y);\\n\\n            // Handle non-overflow cases, 256 by 256 division.\\n            if (high == 0) {\\n                // Solidity will revert if denominator == 0, unlike the div opcode on its own.\\n                // The surrounding unchecked block does not change this fact.\\n                // See https://docs.soliditylang.org/en/latest/control-structures.html#checked-or-unchecked-arithmetic.\\n                return low / denominator;\\n            }\\n\\n            // Make sure the result is less than 2\\u00b2\\u2075\\u2076. Also prevents denominator == 0.\\n            if (denominator <= high) {\\n                Panic.panic(ternary(denominator == 0, Panic.DIVISION_BY_ZERO, Panic.UNDER_OVERFLOW));\\n            }\\n\\n            ///////////////////////////////////////////////\\n            // 512 by 256 division.\\n            ///////////////////////////////////////////////\\n\\n            // Make division exact by subtracting the remainder from [high low].\\n            uint256 remainder;\\n            assembly (\\\"memory-safe\\\") {\\n                // Compute remainder using mulmod.\\n                remainder := mulmod(x, y, denominator)\\n\\n                // Subtract 256 bit number from 512 bit number.\\n                high := sub(high, gt(remainder, low))\\n                low := sub(low, remainder)\\n            }\\n\\n            // Factor powers of two out of denominator and compute largest power of two divisor of denominator.\\n            // Always >= 1. See https://cs.stackexchange.com/q/138556/92363.\\n\\n            uint256 twos = denominator & (0 - denominator);\\n            assembly (\\\"memory-safe\\\") {\\n                // Divide denominator by twos.\\n                denominator := div(denominator, twos)\\n\\n                // Divide [high low] by twos.\\n                low := div(low, twos)\\n\\n                // Flip twos such that it is 2\\u00b2\\u2075\\u2076 / twos. If twos is zero, then it becomes one.\\n                twos := add(div(sub(0, twos), twos), 1)\\n            }\\n\\n            // Shift in bits from high into low.\\n            low |= high * twos;\\n\\n            // Invert denominator mod 2\\u00b2\\u2075\\u2076. Now that denominator is an odd number, it has an inverse modulo 2\\u00b2\\u2075\\u2076 such\\n            // that denominator * inv \\u2261 1 mod 2\\u00b2\\u2075\\u2076. Compute the inverse by starting with a seed that is correct for\\n            // four bits. That is, denominator * inv \\u2261 1 mod 2\\u2074.\\n            uint256 inverse = (3 * denominator) ^ 2;\\n\\n            // Use the Newton-Raphson iteration to improve the precision. Thanks to Hensel's lifting lemma, this also\\n            // works in modular arithmetic, doubling the correct bits in each step.\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u2078\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u00b9\\u2076\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u00b3\\u00b2\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u2076\\u2074\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u00b9\\u00b2\\u2078\\n            inverse *= 2 - denominator * inverse; // inverse mod 2\\u00b2\\u2075\\u2076\\n\\n            // Because the division is now exact we can divide by multiplying with the modular inverse of denominator.\\n            // This will give us the correct result modulo 2\\u00b2\\u2075\\u2076. Since the preconditions guarantee that the outcome is\\n            // less than 2\\u00b2\\u2075\\u2076, this is the final result. We don't need to compute the high bits of the result and high\\n            // is no longer required.\\n            result = low * inverse;\\n            return result;\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculates x * y / denominator with full precision, following the selected rounding direction.\\n     */\\n    function mulDiv(uint256 x, uint256 y, uint256 denominator, Rounding rounding) internal pure returns (uint256) {\\n        return mulDiv(x, y, denominator) + SafeCast.toUint(unsignedRoundsUp(rounding) && mulmod(x, y, denominator) > 0);\\n    }\\n\\n    /**\\n     * @dev Calculates floor(x * y >> n) with full precision. Throws if result overflows a uint256.\\n     */\\n    function mulShr(uint256 x, uint256 y, uint8 n) internal pure returns (uint256 result) {\\n        unchecked {\\n            (uint256 high, uint256 low) = mul512(x, y);\\n            if (high >= 1 << n) {\\n                Panic.panic(Panic.UNDER_OVERFLOW);\\n            }\\n            return (high << (256 - n)) | (low >> n);\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculates x * y >> n with full precision, following the selected rounding direction.\\n     */\\n    function mulShr(uint256 x, uint256 y, uint8 n, Rounding rounding) internal pure returns (uint256) {\\n        return mulShr(x, y, n) + SafeCast.toUint(unsignedRoundsUp(rounding) && mulmod(x, y, 1 << n) > 0);\\n    }\\n\\n    /**\\n     * @dev Calculate the modular multiplicative inverse of a number in Z/nZ.\\n     *\\n     * If n is a prime, then Z/nZ is a field. In that case all elements are inversible, except 0.\\n     * If n is not a prime, then Z/nZ is not a field, and some elements might not be inversible.\\n     *\\n     * If the input value is not inversible, 0 is returned.\\n     *\\n     * NOTE: If you know for sure that n is (big) a prime, it may be cheaper to use Fermat's little theorem and get the\\n     * inverse using `Math.modExp(a, n - 2, n)`. See {invModPrime}.\\n     */\\n    function invMod(uint256 a, uint256 n) internal pure returns (uint256) {\\n        unchecked {\\n            if (n == 0) return 0;\\n\\n            // The inverse modulo is calculated using the Extended Euclidean Algorithm (iterative version)\\n            // Used to compute integers x and y such that: ax + ny = gcd(a, n).\\n            // When the gcd is 1, then the inverse of a modulo n exists and it's x.\\n            // ax + ny = 1\\n            // ax = 1 + (-y)n\\n            // ax \\u2261 1 (mod n) # x is the inverse of a modulo n\\n\\n            // If the remainder is 0 the gcd is n right away.\\n            uint256 remainder = a % n;\\n            uint256 gcd = n;\\n\\n            // Therefore the initial coefficients are:\\n            // ax + ny = gcd(a, n) = n\\n            // 0a + 1n = n\\n            int256 x = 0;\\n            int256 y = 1;\\n\\n            while (remainder != 0) {\\n                uint256 quotient = gcd / remainder;\\n\\n                (gcd, remainder) = (\\n                    // The old remainder is the next gcd to try.\\n                    remainder,\\n                    // Compute the next remainder.\\n                    // Can't overflow given that (a % gcd) * (gcd // (a % gcd)) <= gcd\\n                    // where gcd is at most n (capped to type(uint256).max)\\n                    gcd - remainder * quotient\\n                );\\n\\n                (x, y) = (\\n                    // Increment the coefficient of a.\\n                    y,\\n                    // Decrement the coefficient of n.\\n                    // Can overflow, but the result is casted to uint256 so that the\\n                    // next value of y is \\\"wrapped around\\\" to a value between 0 and n - 1.\\n                    x - y * int256(quotient)\\n                );\\n            }\\n\\n            if (gcd != 1) return 0; // No inverse exists.\\n            return ternary(x < 0, n - uint256(-x), uint256(x)); // Wrap the result if it's negative.\\n        }\\n    }\\n\\n    /**\\n     * @dev Variant of {invMod}. More efficient, but only works if `p` is known to be a prime greater than `2`.\\n     *\\n     * From https://en.wikipedia.org/wiki/Fermat%27s_little_theorem[Fermat's little theorem], we know that if p is\\n     * prime, then `a**(p-1) \\u2261 1 mod p`. As a consequence, we have `a * a**(p-2) \\u2261 1 mod p`, which means that\\n     * `a**(p-2)` is the modular multiplicative inverse of a in Fp.\\n     *\\n     * NOTE: this function does NOT check that `p` is a prime greater than `2`.\\n     */\\n    function invModPrime(uint256 a, uint256 p) internal view returns (uint256) {\\n        unchecked {\\n            return Math.modExp(a, p - 2, p);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the modular exponentiation of the specified base, exponent and modulus (b ** e % m)\\n     *\\n     * Requirements:\\n     * - modulus can't be zero\\n     * - underlying staticcall to precompile must succeed\\n     *\\n     * IMPORTANT: The result is only valid if the underlying call succeeds. When using this function, make\\n     * sure the chain you're using it on supports the precompiled contract for modular exponentiation\\n     * at address 0x05 as specified in https://eips.ethereum.org/EIPS/eip-198[EIP-198]. Otherwise,\\n     * the underlying function will succeed given the lack of a revert, but the result may be incorrectly\\n     * interpreted as 0.\\n     */\\n    function modExp(uint256 b, uint256 e, uint256 m) internal view returns (uint256) {\\n        (bool success, uint256 result) = tryModExp(b, e, m);\\n        if (!success) {\\n            Panic.panic(Panic.DIVISION_BY_ZERO);\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Returns the modular exponentiation of the specified base, exponent and modulus (b ** e % m).\\n     * It includes a success flag indicating if the operation succeeded. Operation will be marked as failed if trying\\n     * to operate modulo 0 or if the underlying precompile reverted.\\n     *\\n     * IMPORTANT: The result is only valid if the success flag is true. When using this function, make sure the chain\\n     * you're using it on supports the precompiled contract for modular exponentiation at address 0x05 as specified in\\n     * https://eips.ethereum.org/EIPS/eip-198[EIP-198]. Otherwise, the underlying function will succeed given the lack\\n     * of a revert, but the result may be incorrectly interpreted as 0.\\n     */\\n    function tryModExp(uint256 b, uint256 e, uint256 m) internal view returns (bool success, uint256 result) {\\n        if (m == 0) return (false, 0);\\n        assembly (\\\"memory-safe\\\") {\\n            let ptr := mload(0x40)\\n            // | Offset    | Content    | Content (Hex)                                                      |\\n            // |-----------|------------|--------------------------------------------------------------------|\\n            // | 0x00:0x1f | size of b  | 0x0000000000000000000000000000000000000000000000000000000000000020 |\\n            // | 0x20:0x3f | size of e  | 0x0000000000000000000000000000000000000000000000000000000000000020 |\\n            // | 0x40:0x5f | size of m  | 0x0000000000000000000000000000000000000000000000000000000000000020 |\\n            // | 0x60:0x7f | value of b | 0x<.............................................................b> |\\n            // | 0x80:0x9f | value of e | 0x<.............................................................e> |\\n            // | 0xa0:0xbf | value of m | 0x<.............................................................m> |\\n            mstore(ptr, 0x20)\\n            mstore(add(ptr, 0x20), 0x20)\\n            mstore(add(ptr, 0x40), 0x20)\\n            mstore(add(ptr, 0x60), b)\\n            mstore(add(ptr, 0x80), e)\\n            mstore(add(ptr, 0xa0), m)\\n\\n            // Given the result < m, it's guaranteed to fit in 32 bytes,\\n            // so we can use the memory scratch space located at offset 0.\\n            success := staticcall(gas(), 0x05, ptr, 0xc0, 0x00, 0x20)\\n            result := mload(0x00)\\n        }\\n    }\\n\\n    /**\\n     * @dev Variant of {modExp} that supports inputs of arbitrary length.\\n     */\\n    function modExp(bytes memory b, bytes memory e, bytes memory m) internal view returns (bytes memory) {\\n        (bool success, bytes memory result) = tryModExp(b, e, m);\\n        if (!success) {\\n            Panic.panic(Panic.DIVISION_BY_ZERO);\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Variant of {tryModExp} that supports inputs of arbitrary length.\\n     */\\n    function tryModExp(\\n        bytes memory b,\\n        bytes memory e,\\n        bytes memory m\\n    ) internal view returns (bool success, bytes memory result) {\\n        if (_zeroBytes(m)) return (false, new bytes(0));\\n\\n        uint256 mLen = m.length;\\n\\n        // Encode call args in result and move the free memory pointer\\n        result = abi.encodePacked(b.length, e.length, mLen, b, e, m);\\n\\n        assembly (\\\"memory-safe\\\") {\\n            let dataPtr := add(result, 0x20)\\n            // Write result on top of args to avoid allocating extra memory.\\n            success := staticcall(gas(), 0x05, dataPtr, mload(result), dataPtr, mLen)\\n            // Overwrite the length.\\n            // result.length > returndatasize() is guaranteed because returndatasize() == m.length\\n            mstore(result, mLen)\\n            // Set the memory pointer after the returned data.\\n            mstore(0x40, add(dataPtr, mLen))\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns whether the provided byte array is zero.\\n     */\\n    function _zeroBytes(bytes memory byteArray) private pure returns (bool) {\\n        for (uint256 i = 0; i < byteArray.length; ++i) {\\n            if (byteArray[i] != 0) {\\n                return false;\\n            }\\n        }\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Returns the square root of a number. If the number is not a perfect square, the value is rounded\\n     * towards zero.\\n     *\\n     * This method is based on Newton's method for computing square roots; the algorithm is restricted to only\\n     * using integer operations.\\n     */\\n    function sqrt(uint256 a) internal pure returns (uint256) {\\n        unchecked {\\n            // Take care of easy edge cases when a == 0 or a == 1\\n            if (a <= 1) {\\n                return a;\\n            }\\n\\n            // In this function, we use Newton's method to get a root of `f(x) := x\\u00b2 - a`. It involves building a\\n            // sequence x_n that converges toward sqrt(a). For each iteration x_n, we also define the error between\\n            // the current value as `\\u03b5_n = | x_n - sqrt(a) |`.\\n            //\\n            // For our first estimation, we consider `e` the smallest power of 2 which is bigger than the square root\\n            // of the target. (i.e. `2**(e-1) \\u2264 sqrt(a) < 2**e`). We know that `e \\u2264 128` because `(2\\u00b9\\u00b2\\u2078)\\u00b2 = 2\\u00b2\\u2075\\u2076` is\\n            // bigger than any uint256.\\n            //\\n            // By noticing that\\n            // `2**(e-1) \\u2264 sqrt(a) < 2**e \\u2192 (2**(e-1))\\u00b2 \\u2264 a < (2**e)\\u00b2 \\u2192 2**(2*e-2) \\u2264 a < 2**(2*e)`\\n            // we can deduce that `e - 1` is `log2(a) / 2`. We can thus compute `x_n = 2**(e-1)` using a method similar\\n            // to the msb function.\\n            uint256 aa = a;\\n            uint256 xn = 1;\\n\\n            if (aa >= (1 << 128)) {\\n                aa >>= 128;\\n                xn <<= 64;\\n            }\\n            if (aa >= (1 << 64)) {\\n                aa >>= 64;\\n                xn <<= 32;\\n            }\\n            if (aa >= (1 << 32)) {\\n                aa >>= 32;\\n                xn <<= 16;\\n            }\\n            if (aa >= (1 << 16)) {\\n                aa >>= 16;\\n                xn <<= 8;\\n            }\\n            if (aa >= (1 << 8)) {\\n                aa >>= 8;\\n                xn <<= 4;\\n            }\\n            if (aa >= (1 << 4)) {\\n                aa >>= 4;\\n                xn <<= 2;\\n            }\\n            if (aa >= (1 << 2)) {\\n                xn <<= 1;\\n            }\\n\\n            // We now have x_n such that `x_n = 2**(e-1) \\u2264 sqrt(a) < 2**e = 2 * x_n`. This implies \\u03b5_n \\u2264 2**(e-1).\\n            //\\n            // We can refine our estimation by noticing that the middle of that interval minimizes the error.\\n            // If we move x_n to equal 2**(e-1) + 2**(e-2), then we reduce the error to \\u03b5_n \\u2264 2**(e-2).\\n            // This is going to be our x_0 (and \\u03b5_0)\\n            xn = (3 * xn) >> 1; // \\u03b5_0 := | x_0 - sqrt(a) | \\u2264 2**(e-2)\\n\\n            // From here, Newton's method give us:\\n            // x_{n+1} = (x_n + a / x_n) / 2\\n            //\\n            // One should note that:\\n            // x_{n+1}\\u00b2 - a = ((x_n + a / x_n) / 2)\\u00b2 - a\\n            //              = ((x_n\\u00b2 + a) / (2 * x_n))\\u00b2 - a\\n            //              = (x_n\\u2074 + 2 * a * x_n\\u00b2 + a\\u00b2) / (4 * x_n\\u00b2) - a\\n            //              = (x_n\\u2074 + 2 * a * x_n\\u00b2 + a\\u00b2 - 4 * a * x_n\\u00b2) / (4 * x_n\\u00b2)\\n            //              = (x_n\\u2074 - 2 * a * x_n\\u00b2 + a\\u00b2) / (4 * x_n\\u00b2)\\n            //              = (x_n\\u00b2 - a)\\u00b2 / (2 * x_n)\\u00b2\\n            //              = ((x_n\\u00b2 - a) / (2 * x_n))\\u00b2\\n            //              \\u2265 0\\n            // Which proves that for all n \\u2265 1, sqrt(a) \\u2264 x_n\\n            //\\n            // This gives us the proof of quadratic convergence of the sequence:\\n            // \\u03b5_{n+1} = | x_{n+1} - sqrt(a) |\\n            //         = | (x_n + a / x_n) / 2 - sqrt(a) |\\n            //         = | (x_n\\u00b2 + a - 2*x_n*sqrt(a)) / (2 * x_n) |\\n            //         = | (x_n - sqrt(a))\\u00b2 / (2 * x_n) |\\n            //         = | \\u03b5_n\\u00b2 / (2 * x_n) |\\n            //         = \\u03b5_n\\u00b2 / | (2 * x_n) |\\n            //\\n            // For the first iteration, we have a special case where x_0 is known:\\n            // \\u03b5_1 = \\u03b5_0\\u00b2 / | (2 * x_0) |\\n            //     \\u2264 (2**(e-2))\\u00b2 / (2 * (2**(e-1) + 2**(e-2)))\\n            //     \\u2264 2**(2*e-4) / (3 * 2**(e-1))\\n            //     \\u2264 2**(e-3) / 3\\n            //     \\u2264 2**(e-3-log2(3))\\n            //     \\u2264 2**(e-4.5)\\n            //\\n            // For the following iterations, we use the fact that, 2**(e-1) \\u2264 sqrt(a) \\u2264 x_n:\\n            // \\u03b5_{n+1} = \\u03b5_n\\u00b2 / | (2 * x_n) |\\n            //         \\u2264 (2**(e-k))\\u00b2 / (2 * 2**(e-1))\\n            //         \\u2264 2**(2*e-2*k) / 2**e\\n            //         \\u2264 2**(e-2*k)\\n            xn = (xn + a / xn) >> 1; // \\u03b5_1 := | x_1 - sqrt(a) | \\u2264 2**(e-4.5)  -- special case, see above\\n            xn = (xn + a / xn) >> 1; // \\u03b5_2 := | x_2 - sqrt(a) | \\u2264 2**(e-9)    -- general case with k = 4.5\\n            xn = (xn + a / xn) >> 1; // \\u03b5_3 := | x_3 - sqrt(a) | \\u2264 2**(e-18)   -- general case with k = 9\\n            xn = (xn + a / xn) >> 1; // \\u03b5_4 := | x_4 - sqrt(a) | \\u2264 2**(e-36)   -- general case with k = 18\\n            xn = (xn + a / xn) >> 1; // \\u03b5_5 := | x_5 - sqrt(a) | \\u2264 2**(e-72)   -- general case with k = 36\\n            xn = (xn + a / xn) >> 1; // \\u03b5_6 := | x_6 - sqrt(a) | \\u2264 2**(e-144)  -- general case with k = 72\\n\\n            // Because e \\u2264 128 (as discussed during the first estimation phase), we know have reached a precision\\n            // \\u03b5_6 \\u2264 2**(e-144) < 1. Given we're operating on integers, then we can ensure that xn is now either\\n            // sqrt(a) or sqrt(a) + 1.\\n            return xn - SafeCast.toUint(xn > a / xn);\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculates sqrt(a), following the selected rounding direction.\\n     */\\n    function sqrt(uint256 a, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = sqrt(a);\\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && result * result < a);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2 of a positive value rounded towards zero.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 x) internal pure returns (uint256 r) {\\n        // If value has upper 128 bits set, log2 result is at least 128\\n        r = SafeCast.toUint(x > 0xffffffffffffffffffffffffffffffff) << 7;\\n        // If upper 64 bits of 128-bit half set, add 64 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffffffffffffffff) << 6;\\n        // If upper 32 bits of 64-bit half set, add 32 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffffffff) << 5;\\n        // If upper 16 bits of 32-bit half set, add 16 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffff) << 4;\\n        // If upper 8 bits of 16-bit half set, add 8 to result\\n        r |= SafeCast.toUint((x >> r) > 0xff) << 3;\\n        // If upper 4 bits of 8-bit half set, add 4 to result\\n        r |= SafeCast.toUint((x >> r) > 0xf) << 2;\\n\\n        // Shifts value right by the current result and use it as an index into this lookup table:\\n        //\\n        // | x (4 bits) |  index  | table[index] = MSB position |\\n        // |------------|---------|-----------------------------|\\n        // |    0000    |    0    |        table[0] = 0         |\\n        // |    0001    |    1    |        table[1] = 0         |\\n        // |    0010    |    2    |        table[2] = 1         |\\n        // |    0011    |    3    |        table[3] = 1         |\\n        // |    0100    |    4    |        table[4] = 2         |\\n        // |    0101    |    5    |        table[5] = 2         |\\n        // |    0110    |    6    |        table[6] = 2         |\\n        // |    0111    |    7    |        table[7] = 2         |\\n        // |    1000    |    8    |        table[8] = 3         |\\n        // |    1001    |    9    |        table[9] = 3         |\\n        // |    1010    |   10    |        table[10] = 3        |\\n        // |    1011    |   11    |        table[11] = 3        |\\n        // |    1100    |   12    |        table[12] = 3        |\\n        // |    1101    |   13    |        table[13] = 3        |\\n        // |    1110    |   14    |        table[14] = 3        |\\n        // |    1111    |   15    |        table[15] = 3        |\\n        //\\n        // The lookup table is represented as a 32-byte value with the MSB positions for 0-15 in the last 16 bytes.\\n        assembly (\\\"memory-safe\\\") {\\n            r := or(r, byte(shr(r, x), 0x0000010102020202030303030303030300000000000000000000000000000000))\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log2(value);\\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 1 << result < value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10 of a positive value rounded towards zero.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >= 10 ** 64) {\\n                value /= 10 ** 64;\\n                result += 64;\\n            }\\n            if (value >= 10 ** 32) {\\n                value /= 10 ** 32;\\n                result += 32;\\n            }\\n            if (value >= 10 ** 16) {\\n                value /= 10 ** 16;\\n                result += 16;\\n            }\\n            if (value >= 10 ** 8) {\\n                value /= 10 ** 8;\\n                result += 8;\\n            }\\n            if (value >= 10 ** 4) {\\n                value /= 10 ** 4;\\n                result += 4;\\n            }\\n            if (value >= 10 ** 2) {\\n                value /= 10 ** 2;\\n                result += 2;\\n            }\\n            if (value >= 10 ** 1) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log10(value);\\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 10 ** result < value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 256 of a positive value rounded towards zero.\\n     * Returns 0 if given 0.\\n     *\\n     * Adding one to the result gives the number of pairs of hex symbols needed to represent `value` as a hex string.\\n     */\\n    function log256(uint256 x) internal pure returns (uint256 r) {\\n        // If value has upper 128 bits set, log2 result is at least 128\\n        r = SafeCast.toUint(x > 0xffffffffffffffffffffffffffffffff) << 7;\\n        // If upper 64 bits of 128-bit half set, add 64 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffffffffffffffff) << 6;\\n        // If upper 32 bits of 64-bit half set, add 32 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffffffff) << 5;\\n        // If upper 16 bits of 32-bit half set, add 16 to result\\n        r |= SafeCast.toUint((x >> r) > 0xffff) << 4;\\n        // Add 1 if upper 8 bits of 16-bit half set, and divide accumulated result by 8\\n        return (r >> 3) | SafeCast.toUint((x >> r) > 0xff);\\n    }\\n\\n    /**\\n     * @dev Return the log in base 256, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log256(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log256(value);\\n            return result + SafeCast.toUint(unsignedRoundsUp(rounding) && 1 << (result << 3) < value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns whether a provided rounding mode is considered rounding up for unsigned integers.\\n     */\\n    function unsignedRoundsUp(Rounding rounding) internal pure returns (bool) {\\n        return uint8(rounding) % 2 == 1;\\n    }\\n}\\n\",\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/math/SafeCast.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/math/SafeCast.sol)\\n// This file was procedurally generated from scripts/generate/templates/SafeCast.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Wrappers over Solidity's uintXX/intXX/bool casting operators with added overflow\\n * checks.\\n *\\n * Downcasting from uint256/int256 in Solidity does not revert on overflow. This can\\n * easily result in undesired exploitation or bugs, since developers usually\\n * assume that overflows raise errors. `SafeCast` restores this intuition by\\n * reverting the transaction when such an operation overflows.\\n *\\n * Using this library instead of the unchecked operations eliminates an entire\\n * class of bugs, so it's recommended to use it always.\\n */\\nlibrary SafeCast {\\n    /**\\n     * @dev Value doesn't fit in an uint of `bits` size.\\n     */\\n    error SafeCastOverflowedUintDowncast(uint8 bits, uint256 value);\\n\\n    /**\\n     * @dev An int value doesn't fit in an uint of `bits` size.\\n     */\\n    error SafeCastOverflowedIntToUint(int256 value);\\n\\n    /**\\n     * @dev Value doesn't fit in an int of `bits` size.\\n     */\\n    error SafeCastOverflowedIntDowncast(uint8 bits, int256 value);\\n\\n    /**\\n     * @dev An uint value doesn't fit in an int of `bits` size.\\n     */\\n    error SafeCastOverflowedUintToInt(uint256 value);\\n\\n    /**\\n     * @dev Returns the downcasted uint248 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint248).\\n     *\\n     * Counterpart to Solidity's `uint248` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 248 bits\\n     */\\n    function toUint248(uint256 value) internal pure returns (uint248) {\\n        if (value > type(uint248).max) {\\n            revert SafeCastOverflowedUintDowncast(248, value);\\n        }\\n        return uint248(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint240 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint240).\\n     *\\n     * Counterpart to Solidity's `uint240` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 240 bits\\n     */\\n    function toUint240(uint256 value) internal pure returns (uint240) {\\n        if (value > type(uint240).max) {\\n            revert SafeCastOverflowedUintDowncast(240, value);\\n        }\\n        return uint240(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint232 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint232).\\n     *\\n     * Counterpart to Solidity's `uint232` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 232 bits\\n     */\\n    function toUint232(uint256 value) internal pure returns (uint232) {\\n        if (value > type(uint232).max) {\\n            revert SafeCastOverflowedUintDowncast(232, value);\\n        }\\n        return uint232(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint224 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint224).\\n     *\\n     * Counterpart to Solidity's `uint224` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 224 bits\\n     */\\n    function toUint224(uint256 value) internal pure returns (uint224) {\\n        if (value > type(uint224).max) {\\n            revert SafeCastOverflowedUintDowncast(224, value);\\n        }\\n        return uint224(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint216 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint216).\\n     *\\n     * Counterpart to Solidity's `uint216` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 216 bits\\n     */\\n    function toUint216(uint256 value) internal pure returns (uint216) {\\n        if (value > type(uint216).max) {\\n            revert SafeCastOverflowedUintDowncast(216, value);\\n        }\\n        return uint216(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint208 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint208).\\n     *\\n     * Counterpart to Solidity's `uint208` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 208 bits\\n     */\\n    function toUint208(uint256 value) internal pure returns (uint208) {\\n        if (value > type(uint208).max) {\\n            revert SafeCastOverflowedUintDowncast(208, value);\\n        }\\n        return uint208(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint200 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint200).\\n     *\\n     * Counterpart to Solidity's `uint200` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 200 bits\\n     */\\n    function toUint200(uint256 value) internal pure returns (uint200) {\\n        if (value > type(uint200).max) {\\n            revert SafeCastOverflowedUintDowncast(200, value);\\n        }\\n        return uint200(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint192 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint192).\\n     *\\n     * Counterpart to Solidity's `uint192` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 192 bits\\n     */\\n    function toUint192(uint256 value) internal pure returns (uint192) {\\n        if (value > type(uint192).max) {\\n            revert SafeCastOverflowedUintDowncast(192, value);\\n        }\\n        return uint192(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint184 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint184).\\n     *\\n     * Counterpart to Solidity's `uint184` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 184 bits\\n     */\\n    function toUint184(uint256 value) internal pure returns (uint184) {\\n        if (value > type(uint184).max) {\\n            revert SafeCastOverflowedUintDowncast(184, value);\\n        }\\n        return uint184(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint176 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint176).\\n     *\\n     * Counterpart to Solidity's `uint176` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 176 bits\\n     */\\n    function toUint176(uint256 value) internal pure returns (uint176) {\\n        if (value > type(uint176).max) {\\n            revert SafeCastOverflowedUintDowncast(176, value);\\n        }\\n        return uint176(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint168 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint168).\\n     *\\n     * Counterpart to Solidity's `uint168` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 168 bits\\n     */\\n    function toUint168(uint256 value) internal pure returns (uint168) {\\n        if (value > type(uint168).max) {\\n            revert SafeCastOverflowedUintDowncast(168, value);\\n        }\\n        return uint168(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint160 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint160).\\n     *\\n     * Counterpart to Solidity's `uint160` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 160 bits\\n     */\\n    function toUint160(uint256 value) internal pure returns (uint160) {\\n        if (value > type(uint160).max) {\\n            revert SafeCastOverflowedUintDowncast(160, value);\\n        }\\n        return uint160(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint152 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint152).\\n     *\\n     * Counterpart to Solidity's `uint152` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 152 bits\\n     */\\n    function toUint152(uint256 value) internal pure returns (uint152) {\\n        if (value > type(uint152).max) {\\n            revert SafeCastOverflowedUintDowncast(152, value);\\n        }\\n        return uint152(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint144 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint144).\\n     *\\n     * Counterpart to Solidity's `uint144` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 144 bits\\n     */\\n    function toUint144(uint256 value) internal pure returns (uint144) {\\n        if (value > type(uint144).max) {\\n            revert SafeCastOverflowedUintDowncast(144, value);\\n        }\\n        return uint144(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint136 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint136).\\n     *\\n     * Counterpart to Solidity's `uint136` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 136 bits\\n     */\\n    function toUint136(uint256 value) internal pure returns (uint136) {\\n        if (value > type(uint136).max) {\\n            revert SafeCastOverflowedUintDowncast(136, value);\\n        }\\n        return uint136(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint128 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint128).\\n     *\\n     * Counterpart to Solidity's `uint128` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 128 bits\\n     */\\n    function toUint128(uint256 value) internal pure returns (uint128) {\\n        if (value > type(uint128).max) {\\n            revert SafeCastOverflowedUintDowncast(128, value);\\n        }\\n        return uint128(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint120 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint120).\\n     *\\n     * Counterpart to Solidity's `uint120` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 120 bits\\n     */\\n    function toUint120(uint256 value) internal pure returns (uint120) {\\n        if (value > type(uint120).max) {\\n            revert SafeCastOverflowedUintDowncast(120, value);\\n        }\\n        return uint120(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint112 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint112).\\n     *\\n     * Counterpart to Solidity's `uint112` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 112 bits\\n     */\\n    function toUint112(uint256 value) internal pure returns (uint112) {\\n        if (value > type(uint112).max) {\\n            revert SafeCastOverflowedUintDowncast(112, value);\\n        }\\n        return uint112(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint104 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint104).\\n     *\\n     * Counterpart to Solidity's `uint104` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 104 bits\\n     */\\n    function toUint104(uint256 value) internal pure returns (uint104) {\\n        if (value > type(uint104).max) {\\n            revert SafeCastOverflowedUintDowncast(104, value);\\n        }\\n        return uint104(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint96 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint96).\\n     *\\n     * Counterpart to Solidity's `uint96` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 96 bits\\n     */\\n    function toUint96(uint256 value) internal pure returns (uint96) {\\n        if (value > type(uint96).max) {\\n            revert SafeCastOverflowedUintDowncast(96, value);\\n        }\\n        return uint96(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint88 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint88).\\n     *\\n     * Counterpart to Solidity's `uint88` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 88 bits\\n     */\\n    function toUint88(uint256 value) internal pure returns (uint88) {\\n        if (value > type(uint88).max) {\\n            revert SafeCastOverflowedUintDowncast(88, value);\\n        }\\n        return uint88(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint80 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint80).\\n     *\\n     * Counterpart to Solidity's `uint80` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 80 bits\\n     */\\n    function toUint80(uint256 value) internal pure returns (uint80) {\\n        if (value > type(uint80).max) {\\n            revert SafeCastOverflowedUintDowncast(80, value);\\n        }\\n        return uint80(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint72 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint72).\\n     *\\n     * Counterpart to Solidity's `uint72` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 72 bits\\n     */\\n    function toUint72(uint256 value) internal pure returns (uint72) {\\n        if (value > type(uint72).max) {\\n            revert SafeCastOverflowedUintDowncast(72, value);\\n        }\\n        return uint72(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint64 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint64).\\n     *\\n     * Counterpart to Solidity's `uint64` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 64 bits\\n     */\\n    function toUint64(uint256 value) internal pure returns (uint64) {\\n        if (value > type(uint64).max) {\\n            revert SafeCastOverflowedUintDowncast(64, value);\\n        }\\n        return uint64(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint56 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint56).\\n     *\\n     * Counterpart to Solidity's `uint56` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 56 bits\\n     */\\n    function toUint56(uint256 value) internal pure returns (uint56) {\\n        if (value > type(uint56).max) {\\n            revert SafeCastOverflowedUintDowncast(56, value);\\n        }\\n        return uint56(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint48 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint48).\\n     *\\n     * Counterpart to Solidity's `uint48` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 48 bits\\n     */\\n    function toUint48(uint256 value) internal pure returns (uint48) {\\n        if (value > type(uint48).max) {\\n            revert SafeCastOverflowedUintDowncast(48, value);\\n        }\\n        return uint48(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint40 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint40).\\n     *\\n     * Counterpart to Solidity's `uint40` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 40 bits\\n     */\\n    function toUint40(uint256 value) internal pure returns (uint40) {\\n        if (value > type(uint40).max) {\\n            revert SafeCastOverflowedUintDowncast(40, value);\\n        }\\n        return uint40(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint32 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint32).\\n     *\\n     * Counterpart to Solidity's `uint32` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 32 bits\\n     */\\n    function toUint32(uint256 value) internal pure returns (uint32) {\\n        if (value > type(uint32).max) {\\n            revert SafeCastOverflowedUintDowncast(32, value);\\n        }\\n        return uint32(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint24 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint24).\\n     *\\n     * Counterpart to Solidity's `uint24` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 24 bits\\n     */\\n    function toUint24(uint256 value) internal pure returns (uint24) {\\n        if (value > type(uint24).max) {\\n            revert SafeCastOverflowedUintDowncast(24, value);\\n        }\\n        return uint24(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint16 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint16).\\n     *\\n     * Counterpart to Solidity's `uint16` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 16 bits\\n     */\\n    function toUint16(uint256 value) internal pure returns (uint16) {\\n        if (value > type(uint16).max) {\\n            revert SafeCastOverflowedUintDowncast(16, value);\\n        }\\n        return uint16(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted uint8 from uint256, reverting on\\n     * overflow (when the input is greater than largest uint8).\\n     *\\n     * Counterpart to Solidity's `uint8` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 8 bits\\n     */\\n    function toUint8(uint256 value) internal pure returns (uint8) {\\n        if (value > type(uint8).max) {\\n            revert SafeCastOverflowedUintDowncast(8, value);\\n        }\\n        return uint8(value);\\n    }\\n\\n    /**\\n     * @dev Converts a signed int256 into an unsigned uint256.\\n     *\\n     * Requirements:\\n     *\\n     * - input must be greater than or equal to 0.\\n     */\\n    function toUint256(int256 value) internal pure returns (uint256) {\\n        if (value < 0) {\\n            revert SafeCastOverflowedIntToUint(value);\\n        }\\n        return uint256(value);\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int248 from int256, reverting on\\n     * overflow (when the input is less than smallest int248 or\\n     * greater than largest int248).\\n     *\\n     * Counterpart to Solidity's `int248` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 248 bits\\n     */\\n    function toInt248(int256 value) internal pure returns (int248 downcasted) {\\n        downcasted = int248(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(248, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int240 from int256, reverting on\\n     * overflow (when the input is less than smallest int240 or\\n     * greater than largest int240).\\n     *\\n     * Counterpart to Solidity's `int240` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 240 bits\\n     */\\n    function toInt240(int256 value) internal pure returns (int240 downcasted) {\\n        downcasted = int240(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(240, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int232 from int256, reverting on\\n     * overflow (when the input is less than smallest int232 or\\n     * greater than largest int232).\\n     *\\n     * Counterpart to Solidity's `int232` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 232 bits\\n     */\\n    function toInt232(int256 value) internal pure returns (int232 downcasted) {\\n        downcasted = int232(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(232, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int224 from int256, reverting on\\n     * overflow (when the input is less than smallest int224 or\\n     * greater than largest int224).\\n     *\\n     * Counterpart to Solidity's `int224` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 224 bits\\n     */\\n    function toInt224(int256 value) internal pure returns (int224 downcasted) {\\n        downcasted = int224(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(224, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int216 from int256, reverting on\\n     * overflow (when the input is less than smallest int216 or\\n     * greater than largest int216).\\n     *\\n     * Counterpart to Solidity's `int216` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 216 bits\\n     */\\n    function toInt216(int256 value) internal pure returns (int216 downcasted) {\\n        downcasted = int216(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(216, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int208 from int256, reverting on\\n     * overflow (when the input is less than smallest int208 or\\n     * greater than largest int208).\\n     *\\n     * Counterpart to Solidity's `int208` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 208 bits\\n     */\\n    function toInt208(int256 value) internal pure returns (int208 downcasted) {\\n        downcasted = int208(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(208, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int200 from int256, reverting on\\n     * overflow (when the input is less than smallest int200 or\\n     * greater than largest int200).\\n     *\\n     * Counterpart to Solidity's `int200` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 200 bits\\n     */\\n    function toInt200(int256 value) internal pure returns (int200 downcasted) {\\n        downcasted = int200(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(200, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int192 from int256, reverting on\\n     * overflow (when the input is less than smallest int192 or\\n     * greater than largest int192).\\n     *\\n     * Counterpart to Solidity's `int192` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 192 bits\\n     */\\n    function toInt192(int256 value) internal pure returns (int192 downcasted) {\\n        downcasted = int192(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(192, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int184 from int256, reverting on\\n     * overflow (when the input is less than smallest int184 or\\n     * greater than largest int184).\\n     *\\n     * Counterpart to Solidity's `int184` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 184 bits\\n     */\\n    function toInt184(int256 value) internal pure returns (int184 downcasted) {\\n        downcasted = int184(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(184, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int176 from int256, reverting on\\n     * overflow (when the input is less than smallest int176 or\\n     * greater than largest int176).\\n     *\\n     * Counterpart to Solidity's `int176` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 176 bits\\n     */\\n    function toInt176(int256 value) internal pure returns (int176 downcasted) {\\n        downcasted = int176(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(176, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int168 from int256, reverting on\\n     * overflow (when the input is less than smallest int168 or\\n     * greater than largest int168).\\n     *\\n     * Counterpart to Solidity's `int168` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 168 bits\\n     */\\n    function toInt168(int256 value) internal pure returns (int168 downcasted) {\\n        downcasted = int168(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(168, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int160 from int256, reverting on\\n     * overflow (when the input is less than smallest int160 or\\n     * greater than largest int160).\\n     *\\n     * Counterpart to Solidity's `int160` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 160 bits\\n     */\\n    function toInt160(int256 value) internal pure returns (int160 downcasted) {\\n        downcasted = int160(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(160, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int152 from int256, reverting on\\n     * overflow (when the input is less than smallest int152 or\\n     * greater than largest int152).\\n     *\\n     * Counterpart to Solidity's `int152` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 152 bits\\n     */\\n    function toInt152(int256 value) internal pure returns (int152 downcasted) {\\n        downcasted = int152(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(152, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int144 from int256, reverting on\\n     * overflow (when the input is less than smallest int144 or\\n     * greater than largest int144).\\n     *\\n     * Counterpart to Solidity's `int144` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 144 bits\\n     */\\n    function toInt144(int256 value) internal pure returns (int144 downcasted) {\\n        downcasted = int144(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(144, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int136 from int256, reverting on\\n     * overflow (when the input is less than smallest int136 or\\n     * greater than largest int136).\\n     *\\n     * Counterpart to Solidity's `int136` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 136 bits\\n     */\\n    function toInt136(int256 value) internal pure returns (int136 downcasted) {\\n        downcasted = int136(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(136, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int128 from int256, reverting on\\n     * overflow (when the input is less than smallest int128 or\\n     * greater than largest int128).\\n     *\\n     * Counterpart to Solidity's `int128` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 128 bits\\n     */\\n    function toInt128(int256 value) internal pure returns (int128 downcasted) {\\n        downcasted = int128(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(128, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int120 from int256, reverting on\\n     * overflow (when the input is less than smallest int120 or\\n     * greater than largest int120).\\n     *\\n     * Counterpart to Solidity's `int120` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 120 bits\\n     */\\n    function toInt120(int256 value) internal pure returns (int120 downcasted) {\\n        downcasted = int120(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(120, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int112 from int256, reverting on\\n     * overflow (when the input is less than smallest int112 or\\n     * greater than largest int112).\\n     *\\n     * Counterpart to Solidity's `int112` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 112 bits\\n     */\\n    function toInt112(int256 value) internal pure returns (int112 downcasted) {\\n        downcasted = int112(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(112, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int104 from int256, reverting on\\n     * overflow (when the input is less than smallest int104 or\\n     * greater than largest int104).\\n     *\\n     * Counterpart to Solidity's `int104` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 104 bits\\n     */\\n    function toInt104(int256 value) internal pure returns (int104 downcasted) {\\n        downcasted = int104(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(104, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int96 from int256, reverting on\\n     * overflow (when the input is less than smallest int96 or\\n     * greater than largest int96).\\n     *\\n     * Counterpart to Solidity's `int96` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 96 bits\\n     */\\n    function toInt96(int256 value) internal pure returns (int96 downcasted) {\\n        downcasted = int96(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(96, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int88 from int256, reverting on\\n     * overflow (when the input is less than smallest int88 or\\n     * greater than largest int88).\\n     *\\n     * Counterpart to Solidity's `int88` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 88 bits\\n     */\\n    function toInt88(int256 value) internal pure returns (int88 downcasted) {\\n        downcasted = int88(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(88, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int80 from int256, reverting on\\n     * overflow (when the input is less than smallest int80 or\\n     * greater than largest int80).\\n     *\\n     * Counterpart to Solidity's `int80` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 80 bits\\n     */\\n    function toInt80(int256 value) internal pure returns (int80 downcasted) {\\n        downcasted = int80(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(80, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int72 from int256, reverting on\\n     * overflow (when the input is less than smallest int72 or\\n     * greater than largest int72).\\n     *\\n     * Counterpart to Solidity's `int72` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 72 bits\\n     */\\n    function toInt72(int256 value) internal pure returns (int72 downcasted) {\\n        downcasted = int72(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(72, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int64 from int256, reverting on\\n     * overflow (when the input is less than smallest int64 or\\n     * greater than largest int64).\\n     *\\n     * Counterpart to Solidity's `int64` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 64 bits\\n     */\\n    function toInt64(int256 value) internal pure returns (int64 downcasted) {\\n        downcasted = int64(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(64, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int56 from int256, reverting on\\n     * overflow (when the input is less than smallest int56 or\\n     * greater than largest int56).\\n     *\\n     * Counterpart to Solidity's `int56` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 56 bits\\n     */\\n    function toInt56(int256 value) internal pure returns (int56 downcasted) {\\n        downcasted = int56(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(56, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int48 from int256, reverting on\\n     * overflow (when the input is less than smallest int48 or\\n     * greater than largest int48).\\n     *\\n     * Counterpart to Solidity's `int48` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 48 bits\\n     */\\n    function toInt48(int256 value) internal pure returns (int48 downcasted) {\\n        downcasted = int48(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(48, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int40 from int256, reverting on\\n     * overflow (when the input is less than smallest int40 or\\n     * greater than largest int40).\\n     *\\n     * Counterpart to Solidity's `int40` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 40 bits\\n     */\\n    function toInt40(int256 value) internal pure returns (int40 downcasted) {\\n        downcasted = int40(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(40, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int32 from int256, reverting on\\n     * overflow (when the input is less than smallest int32 or\\n     * greater than largest int32).\\n     *\\n     * Counterpart to Solidity's `int32` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 32 bits\\n     */\\n    function toInt32(int256 value) internal pure returns (int32 downcasted) {\\n        downcasted = int32(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(32, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int24 from int256, reverting on\\n     * overflow (when the input is less than smallest int24 or\\n     * greater than largest int24).\\n     *\\n     * Counterpart to Solidity's `int24` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 24 bits\\n     */\\n    function toInt24(int256 value) internal pure returns (int24 downcasted) {\\n        downcasted = int24(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(24, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int16 from int256, reverting on\\n     * overflow (when the input is less than smallest int16 or\\n     * greater than largest int16).\\n     *\\n     * Counterpart to Solidity's `int16` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 16 bits\\n     */\\n    function toInt16(int256 value) internal pure returns (int16 downcasted) {\\n        downcasted = int16(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(16, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the downcasted int8 from int256, reverting on\\n     * overflow (when the input is less than smallest int8 or\\n     * greater than largest int8).\\n     *\\n     * Counterpart to Solidity's `int8` operator.\\n     *\\n     * Requirements:\\n     *\\n     * - input must fit into 8 bits\\n     */\\n    function toInt8(int256 value) internal pure returns (int8 downcasted) {\\n        downcasted = int8(value);\\n        if (downcasted != value) {\\n            revert SafeCastOverflowedIntDowncast(8, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Converts an unsigned uint256 into a signed int256.\\n     *\\n     * Requirements:\\n     *\\n     * - input must be less than or equal to maxInt256.\\n     */\\n    function toInt256(uint256 value) internal pure returns (int256) {\\n        // Note: Unsafe cast below is okay because `type(int256).max` is guaranteed to be positive\\n        if (value > uint256(type(int256).max)) {\\n            revert SafeCastOverflowedUintToInt(value);\\n        }\\n        return int256(value);\\n    }\\n\\n    /**\\n     * @dev Cast a boolean (false or true) to a uint256 (0 or 1) with no jump.\\n     */\\n    function toUint(bool b) internal pure returns (uint256 u) {\\n        assembly (\\\"memory-safe\\\") {\\n            u := iszero(iszero(b))\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\"},\"contracts/LaunchpadToken.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport { ERC20 } from \\\"@openzeppelin/contracts/token/ERC20/ERC20.sol\\\";\\nimport { Ownable } from \\\"@openzeppelin/contracts/access/Ownable.sol\\\";\\n\\n/**\\n * @title LaunchpadToken\\n * @notice ERC20 token deployed through the Moonbags launchpad\\n */\\ncontract LaunchpadToken is ERC20, Ownable {\\n    error TokenTransfersBlocked();\\n\\n    bool public isListed;\\n    uint8 private _decimals;\\n    string private _tokenURI;\\n    address public tokenLock;\\n\\n    /**\\n     * @notice Constructor for LaunchpadToken\\n     * @param name_ Token name\\n     * @param symbol_ Token symbol\\n     * @param decimals_ Token decimals\\n     * @param uri_ Token metadata URI\\n     */\\n    constructor(\\n        string memory name_,\\n        string memory symbol_,\\n        uint8 decimals_,\\n        string memory uri_\\n    )\\n        ERC20(name_, symbol_)\\n        Ownable(msg.sender)\\n    {\\n        _decimals = decimals_;\\n        _tokenURI = uri_;\\n        isListed = false; // Initially set to false\\n    }\\n\\n    /**\\n     * @notice Mint new tokens to a specified address\\n     * @param to Address to receive the minted tokens\\n     * @param amount Amount of tokens to mint\\n     */\\n    function mint(address to, uint256 amount) external onlyOwner {\\n        _mint(to, amount);\\n    }\\n\\n    /**\\n     * @notice Set the listed status of the token\\n     * @param isListed_ Boolean indicating if the token is listed\\n     */\\n    function setListed(bool isListed_) external onlyOwner {\\n        isListed = isListed_;\\n    }\\n\\n    /**\\n     * @notice Set the address of the token lock contract\\n     * @param _tokenLock Address of the token lock contract\\n     */\\n    function setTokenLockContract(address _tokenLock) external onlyOwner {\\n        tokenLock = _tokenLock;\\n    }\\n\\n    /**\\n     * @notice Burn tokens from a specified address\\n     * @param from Address from which tokens will be burned\\n     * @param amount Amount of tokens to burn\\n     */\\n    function burn(address from, uint256 amount) external onlyOwner {\\n        _burn(from, amount);\\n    }\\n\\n    /**\\n     * @dev Internal function to handle token transfers with transfer restrictions\\n     * @param from Sender address\\n     * @param to Recipient address\\n     * @param value Amount of tokens to transfer\\n     */\\n    function _update(address from, address to, uint256 value) internal override {\\n        if (\\n            !isListed && from != address(0) && to != address(0) && from != owner() && to != owner()\\n                && from != tokenLock\\n        ) {\\n            revert TokenTransfersBlocked();\\n        }\\n        super._update(from, to, value);\\n    }\\n\\n    /**\\n     * @notice Returns the number of decimals used for the token\\n     * @return uint8 Number of decimals\\n     */\\n    function decimals() public view override returns (uint8) {\\n        return _decimals;\\n    }\\n\\n    /**\\n     * @notice Returns the token metadata URI\\n     * @return string Token URI\\n     */\\n    function tokenURI() public view returns (string memory) {\\n        return _tokenURI;\\n    }\\n\\n    /**\\n     * @notice Set the token metadata URI\\n     * @param uri_ New token URI\\n     */\\n    function setTokenURI(string memory uri_) external onlyOwner {\\n        _tokenURI = uri_;\\n    }\\n}\\n\",\"keccak256\":\"0x3a35a193c26f219a717f0813981aabd60766f6216e8b3e8fdc9b86305cae6d20\",\"license\":\"MIT\"},\"contracts/MoonbagsLaunchpad.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\\\";\\nimport \\\"./interfaces/INonfungiblePositionManager.sol\\\";\\nimport \\\"./MoonbagsStake.sol\\\";\\nimport \\\"./libraries/uniswap/TickMath.sol\\\";\\nimport \\\"./libraries/uniswap/FullMath.sol\\\";\\nimport \\\"./LaunchpadToken.sol\\\";\\nimport \\\"./TokenLock.sol\\\";\\nimport \\\"./libraries/BondingCurveMath.sol\\\";\\nimport \\\"./interfaces/IWETH9.sol\\\";\\n\\n/**\\n * @title Moonbags Launchpad\\n */\\ncontract MoonbagsLaunchpad is\\n    Initializable,\\n    ReentrancyGuardUpgradeable,\\n    OwnableUpgradeable,\\n    IERC721Receiver\\n{\\n    using SafeERC20 for IERC20;\\n    using BondingCurveMath for uint256;\\n\\n    // Constants\\n    uint256 public constant VERSION = 1;\\n    uint256 public constant FEE_DENOMINATOR = 10_000; // 100% = 10000\\n    uint256 public constant DEFAULT_THRESHOLD = 0.03 ether; // 3 SUI\\n    uint256 public constant MINIMUM_THRESHOLD = 0.02 ether; // 2 SUI\\n    uint256 public constant MIN_LOCK_DURATION = 1 hours;\\n    uint256 public constant ONE_CHECKPOINT_TIMESTAMP = 1 seconds;\\n    uint256 public constant DISTRIBUTE_FEE_LOCK_DURATION = 5 minutes;\\n\\n    uint24 public constant FEE_LOW = 500; // 0.05%\\n    uint24 public constant FEE_MEDIUM = 3000; // 0.3%\\n    uint24 public constant FEE_HIGH = 10_000; // 1%\\n    uint24 public constant DEFAULT_FEE_TIER = FEE_MEDIUM; // Use 0.3% as default (most common)\\n    uint256 public constant PRICE_SCALE_192 = 2 ** 192;\\n    uint256 public constant POOL_CREATION_FEE = 0.001 ether; // 0.01 SUI\\n\\n    uint256 public constant DEFAULT_PLATFORM_FEE = 100; // 1%\\n    uint256 public constant DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES = 8_000_000 * 10 ** 6; // 8M\\n    // tokens (6 decimals)\\n    uint256 public constant DEFAULT_REMAIN_TOKEN_RESERVES = 2_000_000 * 10 ** 6; // 2M tokens (6\\n    // decimals)\\n    uint8 public constant DEFAULT_TOKEN_DECIMALS = 6;\\n    uint16 public constant DEFAULT_PLATFORM_FEE_WITHDRAW = 1500; // 15%\\n    uint16 public constant DEFAULT_CREATOR_FEE_WITHDRAW = 3000; // 30%\\n    uint16 public constant DEFAULT_STAKE_FEE_WITHDRAW = 2500; // 25%\\n    uint16 public constant DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW = 2000; // 20%\\n\\n    // address public constant DEAD_ADDRESS = ******************************************;\\n    address public constant PLATFORM_TOKEN_BUYER = ******************************************;\\n\\n    uint256 public constant MAX_URI_LENGTH = 300;\\n    uint256 public constant MAX_DESCRIPTION_LENGTH = 1000;\\n    uint256 public constant MAX_SOCIAL_LENGTH = 500;\\n\\n    struct Configuration {\\n        address admin;\\n        address treasury;\\n        address feePlatformRecipient;\\n        uint256 platformFee;\\n        uint256 initialVirtualTokenReserves;\\n        uint256 remainTokenReserves;\\n        uint8 tokenDecimals;\\n        uint16 initPlatformFeeWithdraw; // 15% to platform\\n        uint16 initCreatorFeeWithdraw; // 30% to creator\\n        uint16 initStakeFeeWithdraw; // 25% to stakers\\n        uint16 initPlatformStakeFeeWithdraw; // 20% to platform stakers\\n        address platformTokenAddress;\\n    }\\n\\n    struct Pool {\\n        uint256 realHypeReserves;\\n        uint256 realTokenReserves;\\n        uint256 virtualTokenReserves;\\n        uint256 virtualHypeReserves;\\n        uint256 remainTokenReserves;\\n        uint256 virtualRemainTokenReserves;\\n        uint256 feeRecipient;\\n        bool isCompleted;\\n        uint256 threshold;\\n        uint16 platformFeeWithdraw;\\n        uint16 creatorFeeWithdraw;\\n        uint16 stakeFeeWithdraw;\\n        uint16 platformStakeFeeWithdraw;\\n        uint256 creationTimestamp;\\n        uint256 feeDistributionUnlockTime;\\n    }\\n\\n    struct ThresholdConfig {\\n        uint256 threshold;\\n    }\\n\\n    Configuration public config;\\n    ThresholdConfig public thresholdConfig;\\n    TokenLock public tokenLock;\\n    MoonbagsStake public moonbagsStake;\\n    mapping(address => Pool) public pools;\\n    mapping(address => uint256) public tokenToPositionId;\\n\\n    // HyperSwap V3 Integration Variables\\n    INonfungiblePositionManager public nonfungiblePositionManager;\\n    address public weth9;\\n    uint24 public activeFeeTier;\\n\\n    event ConfigurationUpdated(\\n        uint256 newPlatformFee,\\n        uint256 newInitialVirtualTokenReserves,\\n        uint256 newRemainTokenReserves,\\n        uint8 newTokenDecimals,\\n        uint16 newInitPlatformFeeWithdraw,\\n        uint16 newInitCreatorFeeWithdraw,\\n        uint16 newInitStakeFeeWithdraw,\\n        uint16 newInitPlatformStakeFeeWithdraw,\\n        address newPlatformTokenAddress,\\n        uint256 timestamp\\n    );\\n\\n    event TokenCreated(\\n        address indexed token,\\n        address indexed creator,\\n        string name,\\n        string symbol,\\n        string uri,\\n        string description,\\n        string twitter,\\n        string telegram,\\n        string website,\\n        uint256 virtualHypeReserves,\\n        uint256 virtualTokenReserves,\\n        uint256 realHypeReserves,\\n        uint256 realTokenReserves,\\n        uint16 platformFeeWithdraw,\\n        uint16 creatorFeeWithdraw,\\n        uint16 stakeFeeWithdraw,\\n        uint16 platformStakeFeeWithdraw,\\n        uint256 threshold,\\n        uint256 timestamp\\n    );\\n\\n    event Trade(\\n        address indexed token,\\n        address indexed user,\\n        bool indexed isBuy,\\n        uint256 hypeAmount,\\n        uint256 tokenAmount,\\n        uint256 virtualHypeReserves,\\n        uint256 virtualTokenReserves,\\n        uint256 realHypeReserves,\\n        uint256 realTokenReserves,\\n        uint256 fee,\\n        uint256 timestamp\\n    );\\n\\n    event TokenLockContractUpdated(address indexed oldTokenLock, address indexed newTokenLock);\\n    event MoonbagsStakeUpdated(address indexed oldMoonbagsStake, address indexed newMoonbagsStake);\\n\\n    event PoolCompleted(address indexed token, string lp, uint256 timestamp);\\n\\n    event V3PoolCreated(address indexed token, address indexed v3Pool, uint256 timestamp);\\n\\n    // Custom errors\\n    error InvalidInput();\\n    error InsufficientAmount();\\n    error PoolAlreadyCompleted();\\n    error PoolNotCompleted();\\n    error TokenNotExists();\\n    error Unauthorized();\\n    error InvalidConfiguration();\\n    error InsufficientTokenReserves();\\n    error InsufficientHypeReserves();\\n    error LPValueDecreased();\\n    error NoPositionFound();\\n    error InvalidDistributionTime();\\n    error InvalidWithdrawalAmount();\\n    error NotEnoughThreshold();\\n\\n    /**\\n     * @notice Initialize the contract\\n     * @param _nonfungiblePositionManager Position Manager address\\n     * @param _weth9 Weth9 address\\n     * @param _poolFee Pool fee (e.g., 3000 for 0.3%)\\n     * @param _platformTokenAddress Platform token address\\n     * @param _moonbagsStake MoonbagsStake contract address\\n     */\\n    function initialize(\\n        address _nonfungiblePositionManager,\\n        address _weth9,\\n        uint24 _poolFee,\\n        address _platformTokenAddress,\\n        address _moonbagsStake,\\n        address _tokenLock\\n    )\\n        public\\n        initializer\\n    {\\n        if (\\n            _nonfungiblePositionManager == address(0) || _weth9 == address(0)\\n                || _platformTokenAddress == address(0) || _moonbagsStake == address(0)\\n                || _tokenLock == address(0)\\n        ) {\\n            revert InvalidInput();\\n        }\\n\\n        __ReentrancyGuard_init();\\n        __Ownable_init(msg.sender);\\n\\n        config = Configuration({\\n            admin: msg.sender,\\n            treasury: msg.sender,\\n            feePlatformRecipient: msg.sender,\\n            platformTokenAddress: _platformTokenAddress,\\n            platformFee: DEFAULT_PLATFORM_FEE,\\n            initialVirtualTokenReserves: DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES,\\n            remainTokenReserves: DEFAULT_REMAIN_TOKEN_RESERVES,\\n            tokenDecimals: DEFAULT_TOKEN_DECIMALS,\\n            initPlatformFeeWithdraw: DEFAULT_PLATFORM_FEE_WITHDRAW,\\n            initCreatorFeeWithdraw: DEFAULT_CREATOR_FEE_WITHDRAW,\\n            initStakeFeeWithdraw: DEFAULT_STAKE_FEE_WITHDRAW,\\n            initPlatformStakeFeeWithdraw: DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW\\n        });\\n\\n        nonfungiblePositionManager = INonfungiblePositionManager(_nonfungiblePositionManager);\\n        weth9 = _weth9;\\n        activeFeeTier = _poolFee;\\n        moonbagsStake = MoonbagsStake(_moonbagsStake);\\n        tokenLock = TokenLock(_tokenLock);\\n\\n        emit ConfigurationUpdated(\\n            DEFAULT_PLATFORM_FEE,\\n            DEFAULT_INITIAL_VIRTUAL_TOKEN_RESERVES,\\n            DEFAULT_REMAIN_TOKEN_RESERVES,\\n            DEFAULT_TOKEN_DECIMALS,\\n            DEFAULT_PLATFORM_FEE_WITHDRAW,\\n            DEFAULT_CREATOR_FEE_WITHDRAW,\\n            DEFAULT_STAKE_FEE_WITHDRAW,\\n            DEFAULT_PLATFORM_STAKE_FEE_WITHDRAW,\\n            _platformTokenAddress,\\n            block.timestamp\\n        );\\n    }\\n\\n    /**\\n     * @notice Create a new pool with bonding curve\\n     * @param name Token name\\n     * @param symbol Token symbol\\n     * @param uri Token metadata URI (max 300 characters)\\n     * @param description Token description (max 1000 characters)\\n     * @param twitter Twitter handle (max 500 characters)\\n     * @param telegram Telegram link (max 500 characters)\\n     * @param website Website URL (max 500 characters)\\n     * @param customThreshold Custom threshold (0 for default)\\n     */\\n    function createPool(\\n        string memory name,\\n        string memory symbol,\\n        string memory uri,\\n        string memory description,\\n        string memory twitter,\\n        string memory telegram,\\n        string memory website,\\n        uint256 customThreshold\\n    )\\n        external\\n        payable\\n        nonReentrant\\n        returns (address tokenAddress)\\n    {\\n        if (msg.value < POOL_CREATION_FEE) revert InvalidInput();\\n\\n        tokenAddress = _createPoolInternal(\\n            name, symbol, uri, description, twitter, telegram, website, customThreshold\\n        );\\n\\n        payable(msg.sender).transfer(msg.value - POOL_CREATION_FEE);\\n        payable(config.feePlatformRecipient).transfer(POOL_CREATION_FEE);\\n\\n        return tokenAddress;\\n    }\\n\\n    /**\\n     * @notice Create a new pool with bonding curve and lock the first buy\\n     * @param name Token name\\n     * @param symbol Token symbol\\n     * @param uri Token metadata URI (max 300 characters)\\n     * @param description Token description (max 1000 characters)\\n     * @param twitter Twitter handle (max 500 characters)\\n     * @param telegram Telegram link (max 500 characters)\\n     * @param website Website URL (max 500 characters)\\n     * @param customThreshold Custom threshold (0 for default)\\n     * @param amountOut Amount of tokens to buy and lock\\n     * @param lockDuration Duration to lock the purchased tokens (minimum 1 hour)\\n     */\\n    function createAndLockFirstBuy(\\n        string memory name,\\n        string memory symbol,\\n        string memory uri,\\n        string memory description,\\n        string memory twitter,\\n        string memory telegram,\\n        string memory website,\\n        uint256 customThreshold,\\n        uint256 amountOut,\\n        uint256 lockDuration\\n    )\\n        external\\n        payable\\n        nonReentrant\\n        returns (address tokenAddress)\\n    {\\n        // Enhanced validation based on Sui implementation\\n        if (address(tokenLock) == address(0)) revert InvalidConfiguration();\\n        if (lockDuration < MIN_LOCK_DURATION) revert InvalidInput();\\n        if (msg.value < POOL_CREATION_FEE) revert InvalidInput();\\n\\n        tokenAddress = _createPoolInternal(\\n            name, symbol, uri, description, twitter, telegram, website, customThreshold\\n        );\\n\\n        uint256 amountIn = msg.value - POOL_CREATION_FEE;\\n\\n        if (amountIn > 0) {\\n            _buyDirectAndLock(tokenAddress, amountIn, amountOut, lockDuration, msg.sender);\\n        }\\n\\n        payable(config.feePlatformRecipient).transfer(POOL_CREATION_FEE);\\n\\n        return tokenAddress;\\n    }\\n\\n    function _createPoolInternal(\\n        string memory name,\\n        string memory symbol,\\n        string memory uri,\\n        string memory description,\\n        string memory twitter,\\n        string memory telegram,\\n        string memory website,\\n        uint256 customThreshold\\n    )\\n        internal\\n        returns (address tokenAddress)\\n    {\\n        if (bytes(name).length == 0 || bytes(symbol).length == 0) revert InvalidInput();\\n        if (bytes(uri).length > MAX_URI_LENGTH) revert InvalidInput();\\n        if (bytes(description).length > MAX_DESCRIPTION_LENGTH) revert InvalidInput();\\n        if (bytes(twitter).length > MAX_SOCIAL_LENGTH) revert InvalidInput();\\n        if (bytes(telegram).length > MAX_SOCIAL_LENGTH) revert InvalidInput();\\n        if (bytes(website).length > MAX_SOCIAL_LENGTH) revert InvalidInput();\\n\\n        uint256 threshold = customThreshold == 0 ? DEFAULT_THRESHOLD : customThreshold;\\n        if (threshold < MINIMUM_THRESHOLD) revert InvalidInput();\\n\\n        uint256 initialVirtualHypeReserves = calculateInitialVirtualHypeReserves(threshold);\\n        uint256 actualVirtualTokenReserves = calculateActualVirtualTokenReserves();\\n        uint256 virtualRemainTokenReserves = calculateVirtualRemainTokenReserves();\\n\\n        LaunchpadToken token = new LaunchpadToken(name, symbol, config.tokenDecimals, uri);\\n        tokenAddress = address(token);\\n\\n        if (address(tokenLock) != address(0)) {\\n            token.setTokenLockContract(address(tokenLock));\\n        }\\n\\n        pools[tokenAddress] = Pool({\\n            realHypeReserves: 0,\\n            realTokenReserves: config.initialVirtualTokenReserves,\\n            virtualTokenReserves: actualVirtualTokenReserves,\\n            virtualHypeReserves: initialVirtualHypeReserves,\\n            remainTokenReserves: config.remainTokenReserves,\\n            virtualRemainTokenReserves: virtualRemainTokenReserves,\\n            feeRecipient: 0,\\n            isCompleted: false,\\n            threshold: threshold,\\n            platformFeeWithdraw: config.initPlatformFeeWithdraw,\\n            creatorFeeWithdraw: config.initCreatorFeeWithdraw,\\n            stakeFeeWithdraw: config.initStakeFeeWithdraw,\\n            platformStakeFeeWithdraw: config.initPlatformStakeFeeWithdraw,\\n            creationTimestamp: block.timestamp,\\n            feeDistributionUnlockTime: block.timestamp + DISTRIBUTE_FEE_LOCK_DURATION\\n        });\\n\\n        Pool storage pool = pools[tokenAddress];\\n        emit TokenCreated(\\n            tokenAddress,\\n            msg.sender,\\n            name,\\n            symbol,\\n            uri,\\n            description,\\n            twitter,\\n            telegram,\\n            website,\\n            pool.virtualHypeReserves,\\n            pool.virtualTokenReserves,\\n            pool.realHypeReserves,\\n            pool.realTokenReserves,\\n            pool.platformFeeWithdraw,\\n            pool.creatorFeeWithdraw,\\n            pool.stakeFeeWithdraw,\\n            pool.platformStakeFeeWithdraw,\\n            threshold,\\n            block.timestamp\\n        );\\n\\n        _initializeStakingPools(tokenAddress, msg.sender);\\n\\n        return tokenAddress;\\n    }\\n\\n    /**\\n     * @notice Initialize staking pools for a newly created token\\n     * @param tokenAddress The address of the newly created token\\n     * @param creator The address of the token creator\\n     */\\n    function _initializeStakingPools(address tokenAddress, address creator) internal {\\n        if (address(moonbagsStake) == address(0)) revert InvalidInput();\\n\\n        moonbagsStake.initializeStakingPool(tokenAddress);\\n        moonbagsStake.initializeCreatorPool(tokenAddress, creator);\\n    }\\n\\n    /**\\n     * @notice Buy exact amount in and lock tokens for specified duration\\n     * @param token Token address\\n     * @param amountIn Exact amount of ETH to spend\\n     * @param amountOutMin Minimum amount of tokens to receive\\n     */\\n    function buyExactIn(\\n        address token,\\n        uint256 amountIn,\\n        uint256 amountOutMin\\n    )\\n        external\\n        payable\\n        nonReentrant\\n        returns (uint256 tokenAmountOut)\\n    {\\n        if (!isToken(token)) revert TokenNotExists();\\n        if (address(tokenLock) == address(0)) revert InvalidConfiguration();\\n        if (amountIn == 0) revert InvalidInput();\\n        if (msg.value < amountIn) revert InsufficientAmount();\\n\\n        Pool storage pool = pools[token];\\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\\n\\n        uint256 tokenReservesInPool = pool.virtualTokenReserves > pool.virtualRemainTokenReserves\\n            ? pool.virtualTokenReserves - pool.virtualRemainTokenReserves\\n            : 0;\\n\\n        (uint256 actualAmountOut, uint256 hypeAmountInSwap, uint256 fee) =\\n        _calculateBuyExactInAmounts(\\n            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn, tokenReservesInPool\\n        );\\n\\n        if (actualAmountOut < amountOutMin) revert InvalidInput();\\n        if (msg.value < hypeAmountInSwap + fee) revert InsufficientAmount();\\n\\n        _executeSwap(pool, 0, hypeAmountInSwap, actualAmountOut, 0);\\n        pool.virtualTokenReserves = pool.virtualTokenReserves - actualAmountOut;\\n        pool.feeRecipient += fee;\\n\\n        emit Trade(\\n            token,\\n            msg.sender,\\n            true,\\n            hypeAmountInSwap,\\n            actualAmountOut,\\n            pool.virtualHypeReserves,\\n            pool.virtualTokenReserves,\\n            pool.realHypeReserves,\\n            pool.realTokenReserves,\\n            fee,\\n            block.timestamp\\n        );\\n\\n        if (actualAmountOut == tokenReservesInPool) {\\n            _completePool(token, pool);\\n        }\\n\\n        if (block.timestamp < pool.creationTimestamp + ONE_CHECKPOINT_TIMESTAMP) {\\n            LaunchpadToken(token).mint(address(this), actualAmountOut);\\n            IERC20(token).safeIncreaseAllowance(address(tokenLock), actualAmountOut);\\n            uint256 lockEndTime = block.timestamp + 1 hours;\\n            tokenLock.createLock(token, actualAmountOut, lockEndTime, msg.sender);\\n        } else {\\n            LaunchpadToken(token).mint(msg.sender, actualAmountOut);\\n        }\\n\\n        uint256 totalUsed = hypeAmountInSwap + fee;\\n        uint256 refundAmount = msg.value - totalUsed;\\n        if (refundAmount > 0) {\\n            payable(msg.sender).transfer(refundAmount);\\n        }\\n\\n        return actualAmountOut;\\n    }\\n\\n    function _calculateBuyExactInAmounts(\\n        uint256 virtualHypeReserves,\\n        uint256 virtualTokenReserves,\\n        uint256 amountIn,\\n        uint256 tokenReservesInPool\\n    )\\n        internal\\n        view\\n        returns (uint256 actualAmountOut, uint256 hypeAmountInSwap, uint256 fee)\\n    {\\n        uint256 initialTokenOut = BondingCurveMath.calculateTokensForExactBase(\\n            virtualHypeReserves, virtualTokenReserves, amountIn\\n        );\\n        if (initialTokenOut > tokenReservesInPool) {\\n            actualAmountOut = tokenReservesInPool;\\n            hypeAmountInSwap = BondingCurveMath.calculateBaseCostForExactTokens(\\n                virtualHypeReserves, virtualTokenReserves, actualAmountOut\\n            ) + 1;\\n        } else {\\n            actualAmountOut = initialTokenOut;\\n            hypeAmountInSwap = amountIn;\\n        }\\n\\n        fee = (hypeAmountInSwap * config.platformFee) / FEE_DENOMINATOR;\\n    }\\n\\n    // /**\\n    //  * @notice Buy exact amount out with automatic token locking\\n    //  * @param token Token address\\n    //  * @param tokenAmountOut Amount of tokens to buy and lock\\n    //  * @return actualAmountOut Amount of tokens purchased and locked\\n    //  */\\n    // function buyExactOut(\\n    //     address token,\\n    //     uint256 tokenAmountOut\\n    // )\\n    //     external\\n    //     payable\\n    //     nonReentrant\\n    //     returns (uint256 actualAmountOut)\\n    // {\\n    //     if (!isToken(token)) revert TokenNotExists();\\n    //     if (address(tokenLock) == address(0)) revert InvalidConfiguration();\\n\\n    //     Pool storage pool = pools[token];\\n    //     if (pool.isCompleted) revert PoolAlreadyCompleted();\\n    //     if (tokenAmountOut == 0) revert InvalidInput();\\n\\n    //     uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;\\n    //     uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves\\n    //         ? pool.virtualTokenReserves - virtualRemainTokenReserves\\n    //         : 0;\\n\\n    //     uint256 hypeCostBeforeFee;\\n    //     uint256 fee;\\n    //     (actualAmountOut, hypeCostBeforeFee, fee) = _calculateBuyExactOutAmounts(\\n    //         pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut,\\n    // tokenReservesInPool\\n    //     );\\n\\n    //     uint256 totalCost = hypeCostBeforeFee + fee;\\n    //     if (msg.value < totalCost) revert InsufficientAmount();\\n\\n    //     _executeSwap(pool, 0, msg.value - fee, actualAmountOut, msg.value - totalCost);\\n\\n    //     pool.feeRecipient += fee;\\n    //     pool.virtualTokenReserves = pool.virtualTokenReserves - actualAmountOut;\\n\\n    //     LaunchpadToken(token).mint(msg.sender, actualAmountOut);\\n\\n    //     _handleFeesAndRefund(fee, msg.value - totalCost);\\n\\n    //     emit Trade(\\n    //         token,\\n    //         msg.sender,\\n    //         true,\\n    //         hypeCostBeforeFee,\\n    //         actualAmountOut,\\n    //         pool.virtualHypeReserves,\\n    //         pool.virtualTokenReserves,\\n    //         pool.realHypeReserves,\\n    //         pool.realTokenReserves,\\n    //         fee,\\n    //         block.timestamp\\n    //     );\\n\\n    //     if (actualAmountOut == tokenReservesInPool) {\\n    //         _completePool(token, pool);\\n    //     }\\n\\n    //     return actualAmountOut;\\n    // }\\n\\n    function _calculateBuyExactOutAmounts(\\n        uint256 virtualHypeReserves,\\n        uint256 virtualTokenReserves,\\n        uint256 tokenAmountOut,\\n        uint256 tokenReservesInPool\\n    )\\n        internal\\n        view\\n        returns (uint256 actualAmountOut, uint256 hypeCostBeforeFee, uint256 fee)\\n    {\\n        actualAmountOut =\\n            tokenAmountOut > tokenReservesInPool ? tokenReservesInPool : tokenAmountOut;\\n        hypeCostBeforeFee = BondingCurveMath.calculateBaseCostForExactTokens(\\n            virtualHypeReserves, virtualTokenReserves, actualAmountOut\\n        ) + 1;\\n\\n        fee = (hypeCostBeforeFee * config.platformFee) / FEE_DENOMINATOR;\\n    }\\n\\n    function sellExactIn(\\n        address token,\\n        uint256 tokenAmountIn,\\n        uint256 amountOutMin\\n    )\\n        external\\n        nonReentrant\\n        returns (uint256 hypeAmountOut)\\n    {\\n        if (!isToken(token)) revert TokenNotExists();\\n        Pool storage pool = pools[token];\\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\\n        if (tokenAmountIn == 0) revert InvalidInput();\\n\\n        LaunchpadToken(token).burn(msg.sender, tokenAmountIn);\\n\\n        uint256 netAmount;\\n        uint256 fee;\\n        (hypeAmountOut, netAmount, fee) = _calculateSellExactInAmounts(\\n            pool.virtualHypeReserves,\\n            pool.virtualTokenReserves,\\n            tokenAmountIn,\\n            pool.realHypeReserves\\n        );\\n\\n        if (netAmount < amountOutMin) revert InsufficientAmount();\\n\\n        _executeSwap(pool, tokenAmountIn, 0, 0, hypeAmountOut);\\n        pool.virtualHypeReserves = pool.virtualHypeReserves - hypeAmountOut;\\n        pool.feeRecipient += fee;\\n\\n        payable(msg.sender).transfer(netAmount);\\n\\n        emit Trade(\\n            token,\\n            msg.sender,\\n            false,\\n            hypeAmountOut,\\n            tokenAmountIn,\\n            pool.virtualHypeReserves,\\n            pool.virtualTokenReserves,\\n            pool.realHypeReserves,\\n            pool.realTokenReserves,\\n            fee,\\n            block.timestamp\\n        );\\n\\n        return netAmount;\\n    }\\n\\n    function _calculateSellExactInAmounts(\\n        uint256 virtualHypeReserves,\\n        uint256 virtualTokenReserves,\\n        uint256 tokenAmountIn,\\n        uint256 realHypeReserves\\n    )\\n        internal\\n        view\\n        returns (uint256 hypeAmountOut, uint256 netAmount, uint256 fee)\\n    {\\n        hypeAmountOut = BondingCurveMath.calculateBaseForExactTokens(\\n            virtualHypeReserves, virtualTokenReserves, tokenAmountIn\\n        );\\n        if (hypeAmountOut > realHypeReserves) {\\n            hypeAmountOut = realHypeReserves;\\n        }\\n        fee = (hypeAmountOut * config.platformFee) / FEE_DENOMINATOR;\\n        netAmount = hypeAmountOut - fee;\\n    }\\n\\n    function _executeSwap(\\n        Pool storage pool,\\n        uint256 tokenAmountIn,\\n        uint256 hypeAmountIn,\\n        uint256 tokenAmountOut,\\n        uint256 hypeAmountOut\\n    )\\n        internal\\n        returns (uint256 actualTokenOut, uint256 actualHypeOut)\\n    {\\n        uint256 beforeVirtualTokenReserves = pool.virtualTokenReserves;\\n        uint256 beforeVirtualHypeReserves = pool.virtualHypeReserves;\\n\\n        if (tokenAmountIn == 0 && hypeAmountIn == 0) revert InvalidInput();\\n\\n        pool.virtualTokenReserves = pool.virtualTokenReserves + tokenAmountIn;\\n        pool.virtualHypeReserves = pool.virtualHypeReserves + hypeAmountIn;\\n\\n        if (tokenAmountIn > 0) {\\n            pool.virtualTokenReserves = pool.virtualTokenReserves - tokenAmountOut;\\n        }\\n        if (hypeAmountIn > 0) {\\n            pool.virtualHypeReserves = pool.virtualHypeReserves - hypeAmountOut;\\n        }\\n\\n        if (\\n            beforeVirtualTokenReserves * beforeVirtualHypeReserves\\n                > pool.virtualTokenReserves * pool.virtualHypeReserves\\n        ) revert LPValueDecreased();\\n\\n        pool.realTokenReserves += tokenAmountIn;\\n        pool.realHypeReserves += hypeAmountIn;\\n\\n        if (pool.realTokenReserves < tokenAmountOut) revert InsufficientTokenReserves();\\n        if (pool.realHypeReserves < hypeAmountOut) revert InsufficientHypeReserves();\\n\\n        pool.realTokenReserves -= tokenAmountOut;\\n        pool.realHypeReserves -= hypeAmountOut;\\n\\n        return (tokenAmountOut, hypeAmountOut);\\n    }\\n\\n    /**\\n     * @notice Complete pool when all tokens are sold\\n     */\\n    function _completePool(address token, Pool storage pool) internal {\\n        pool.isCompleted = true;\\n        _migrateToHyperSwapV3(token, pool);\\n    }\\n\\n    function createThresholdConfig(uint256 _threshold) external onlyOwner {\\n        thresholdConfig = ThresholdConfig(_threshold);\\n    }\\n\\n    function earlyCompletePool(address token) external onlyOwner nonReentrant {\\n        if (!isToken(token)) revert TokenNotExists();\\n\\n        Pool storage pool = pools[token];\\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\\n\\n        pool.isCompleted = true;\\n\\n        uint256 realHypeReserves = pool.realHypeReserves;\\n        uint256 realTokenReserves = pool.realTokenReserves;\\n        uint256 remainTokenReserves = pool.remainTokenReserves;\\n\\n        if (realHypeReserves < thresholdConfig.threshold) {\\n            revert NotEnoughThreshold();\\n        }\\n\\n        if (thresholdConfig.threshold > 0) {\\n            payable(config.admin).transfer(thresholdConfig.threshold);\\n        }\\n\\n        if (config.remainTokenReserves > 0) {\\n            LaunchpadToken(token).mint(config.admin, config.remainTokenReserves);\\n        }\\n\\n        uint256 remainingHype = realHypeReserves - thresholdConfig.threshold;\\n        if (remainingHype > 0) {\\n            payable(msg.sender).transfer(remainingHype);\\n        }\\n\\n        uint256 remainingTokens =\\n            realTokenReserves + remainTokenReserves - config.remainTokenReserves;\\n        if (remainingTokens > 0) {\\n            LaunchpadToken(token).mint(msg.sender, remainingTokens);\\n        }\\n\\n        pool.realHypeReserves = 0;\\n        pool.realTokenReserves = 0;\\n\\n        emit PoolCompleted(token, \\\"0x0\\\", block.timestamp);\\n    }\\n\\n    /**\\n     * @notice Distribute fees for a token\\n     * @param token Token address\\n     */\\n    function _distributeFees(address token) internal {\\n        if (!isToken(token)) revert TokenNotExists();\\n        Pool storage pool = pools[token];\\n\\n        if (block.timestamp < pool.feeDistributionUnlockTime) {\\n            revert InvalidDistributionTime();\\n        }\\n\\n        uint256 totalFees = pool.feeRecipient;\\n        if (totalFees == 0) return;\\n\\n        if (address(this).balance < totalFees) {\\n            return;\\n        }\\n\\n        uint256 platformShare = (totalFees * pool.platformFeeWithdraw) / FEE_DENOMINATOR;\\n        uint256 creatorShare = (totalFees * pool.creatorFeeWithdraw) / FEE_DENOMINATOR;\\n        uint256 stakeShare = (totalFees * pool.stakeFeeWithdraw) / FEE_DENOMINATOR;\\n        uint256 platformStakeShare = (totalFees * pool.platformStakeFeeWithdraw) / FEE_DENOMINATOR;\\n\\n        uint256 totalDistribution = platformShare + creatorShare + stakeShare + platformStakeShare;\\n        if (totalDistribution > totalFees) {\\n            revert InvalidWithdrawalAmount();\\n        }\\n\\n        if (platformShare > 0) {\\n            payable(config.feePlatformRecipient).transfer(platformShare);\\n        }\\n\\n        if (stakeShare > 0) {\\n            moonbagsStake.updateRewardIndex{ value: stakeShare }(token);\\n        }\\n\\n        if (creatorShare > 0) {\\n            moonbagsStake.depositCreatorPool{ value: creatorShare }(token);\\n        }\\n\\n        if (platformStakeShare > 0) {\\n            moonbagsStake.updateRewardIndex{ value: platformStakeShare }(\\n                config.platformTokenAddress\\n            );\\n        }\\n\\n        pool.feeDistributionUnlockTime = block.timestamp + DISTRIBUTE_FEE_LOCK_DURATION;\\n\\n        uint256 remainingBalance = totalFees - totalDistribution;\\n        pool.feeRecipient -= totalFees;\\n\\n        if (remainingBalance > 0) {\\n            payable(PLATFORM_TOKEN_BUYER).transfer(remainingBalance);\\n        }\\n    }\\n\\n    /**\\n     * @notice Distribute accumulated bonding curve fees for a token\\n     * @param token Token address\\n     */\\n    function distributeBondingCurveFees(address token) external nonReentrant {\\n        _distributeFees(token);\\n    }\\n\\n    /**\\n     * @notice Collect all fees from a HyperSwap V3 position\\n     * @param token Token address associated with the position\\n     * @return amount0 The amount of fees collected in token0\\n     * @return amount1 The amount of fees collected in token1\\n     */\\n    function collectHyperSwapFees(address token)\\n        external\\n        nonReentrant\\n        returns (uint256 amount0, uint256 amount1)\\n    {\\n        if (!isToken(token)) revert TokenNotExists();\\n\\n        Pool storage pool = pools[token];\\n        if (!pool.isCompleted) revert PoolNotCompleted();\\n\\n        uint256 tokenId = tokenToPositionId[token];\\n        if (tokenId == 0) revert NoPositionFound();\\n\\n        INonfungiblePositionManager.CollectParams memory params = INonfungiblePositionManager\\n            .CollectParams({\\n            tokenId: tokenId,\\n            recipient: address(this),\\n            amount0Max: type(uint128).max,\\n            amount1Max: type(uint128).max\\n        });\\n\\n        (amount0, amount1) = nonfungiblePositionManager.collect(params);\\n        (,, address token0,,,,,,,,,) = nonfungiblePositionManager.positions(tokenId);\\n        (uint256 bondedTokenAmount, uint256 whypeTokenAmount) =\\n            token0 == weth9 ? (amount1, amount0) : (amount0, amount1);\\n\\n        if (bondedTokenAmount > 0) {\\n            IERC20(token).safeTransfer(config.treasury, bondedTokenAmount);\\n        }\\n\\n        if (whypeTokenAmount > 0) {\\n            IWETH9(weth9).withdraw(whypeTokenAmount);\\n            pool.feeRecipient += whypeTokenAmount;\\n        }\\n\\n        _distributeFees(token);\\n\\n        return (amount0, amount1);\\n    }\\n\\n    /**\\n     * @notice Update configuration (only owner)\\n     */\\n    function updateConfiguration(\\n        uint256 _platformFee,\\n        uint256 _initialVirtualTokenReserves,\\n        uint256 _remainTokenReserves,\\n        uint8 _tokenDecimals,\\n        uint16 _initPlatformFeeWithdraw,\\n        uint16 _initCreatorFeeWithdraw,\\n        uint16 _initStakeFeeWithdraw,\\n        uint16 _initPlatformStakeFeeWithdraw,\\n        address _platformTokenAddress\\n    )\\n        external\\n        onlyOwner\\n    {\\n        if (\\n            _initPlatformFeeWithdraw + _initCreatorFeeWithdraw + _initStakeFeeWithdraw\\n                + _initPlatformStakeFeeWithdraw > FEE_DENOMINATOR\\n        ) revert InvalidInput();\\n\\n        config.platformFee = _platformFee;\\n        config.initialVirtualTokenReserves = _initialVirtualTokenReserves;\\n        config.remainTokenReserves = _remainTokenReserves;\\n        config.tokenDecimals = _tokenDecimals;\\n        config.initPlatformFeeWithdraw = _initPlatformFeeWithdraw;\\n        config.initCreatorFeeWithdraw = _initCreatorFeeWithdraw;\\n        config.initStakeFeeWithdraw = _initStakeFeeWithdraw;\\n        config.initPlatformStakeFeeWithdraw = _initPlatformStakeFeeWithdraw;\\n        config.platformTokenAddress = _platformTokenAddress;\\n\\n        emit ConfigurationUpdated(\\n            _platformFee,\\n            _initialVirtualTokenReserves,\\n            _remainTokenReserves,\\n            _tokenDecimals,\\n            _initPlatformFeeWithdraw,\\n            _initCreatorFeeWithdraw,\\n            _initStakeFeeWithdraw,\\n            _initPlatformStakeFeeWithdraw,\\n            _platformTokenAddress,\\n            block.timestamp\\n        );\\n    }\\n\\n    /**\\n     * @notice skim\\n     * @param token Token address to skim\\n     */\\n    function skim(address token) external onlyOwner {\\n        if (token == address(0)) revert InvalidInput();\\n        if (!isToken(token)) revert TokenNotExists();\\n        Pool storage pool = pools[token];\\n\\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\\n\\n        (uint256 realHypeReserves, uint256 realTokenReserves) =\\n            (pool.realHypeReserves, pool.realTokenReserves);\\n\\n        if (realHypeReserves > 0) {\\n            payable(msg.sender).transfer(realHypeReserves);\\n            pool.realHypeReserves = 0;\\n        }\\n\\n        if (realTokenReserves > 0) {\\n            LaunchpadToken(token).mint(msg.sender, realTokenReserves);\\n            pool.realTokenReserves = 0;\\n        }\\n    }\\n\\n    function updateThresholdConfig(uint256 newThreshold) external onlyOwner {\\n        thresholdConfig.threshold = newThreshold;\\n    }\\n\\n    /**\\n     * @notice Update the initial virtual token reserves (only owner)\\n     * @param _initialVirtualTokenReserves New initial virtual token reserves\\n     */\\n    function updateInitialVirtualTokenReserves(uint256 _initialVirtualTokenReserves)\\n        external\\n        onlyOwner\\n    {\\n        config.initialVirtualTokenReserves = _initialVirtualTokenReserves;\\n    }\\n\\n    /**\\n     * @notice Update fee recipients (only owner)\\n     */\\n    function updateFeeRecipients(\\n        address _treasury,\\n        address _feePlatformRecipient\\n    )\\n        external\\n        onlyOwner\\n    {\\n        config.treasury = _treasury;\\n        config.feePlatformRecipient = _feePlatformRecipient;\\n    }\\n\\n    /**\\n     * @notice Update the initial withdraw fees (only owner)\\n     */\\n    function updateConfigWithdrawFee(\\n        uint16 _newInitPlatformFeeWithdraw,\\n        uint16 _newInitCreatorFeeWithdraw,\\n        uint16 _newInitStakeFeeWithdraw,\\n        uint16 _newInitPlatformStakeFeeWithdraw\\n    )\\n        external\\n        onlyOwner\\n    {\\n        config.initPlatformFeeWithdraw = _newInitPlatformFeeWithdraw;\\n        config.initCreatorFeeWithdraw = _newInitCreatorFeeWithdraw;\\n        config.initStakeFeeWithdraw = _newInitStakeFeeWithdraw;\\n        config.initPlatformStakeFeeWithdraw = _newInitPlatformStakeFeeWithdraw;\\n    }\\n\\n    /**\\n     * @notice Set TokenLock contract (only owner)\\n     * @param _tokenLock Address of the new TokenLock contract\\n     */\\n    function setTokenLock(address _tokenLock) external onlyOwner {\\n        if (_tokenLock == address(0)) revert InvalidInput();\\n\\n        address oldTokenLock = address(tokenLock);\\n        tokenLock = TokenLock(_tokenLock);\\n\\n        emit TokenLockContractUpdated(oldTokenLock, _tokenLock);\\n    }\\n\\n    /**\\n     * @notice Set the MoonbagsStake contract address (only owner)\\n     * @param _moonbagsStake Address of the MoonbagsStake contract\\n     */\\n    function setMoonbagsStake(address _moonbagsStake) external onlyOwner {\\n        if (_moonbagsStake == address(0)) revert InvalidInput();\\n\\n        address oldMoonbagsStake = address(moonbagsStake);\\n        moonbagsStake = MoonbagsStake(_moonbagsStake);\\n\\n        emit MoonbagsStakeUpdated(oldMoonbagsStake, _moonbagsStake);\\n    }\\n\\n    /**\\n     * @notice Update the active fee tier for new pools (only owner)\\n     * @param newFeeTier The new fee tier (500, 3000, or 10000)\\n     */\\n    function updateActiveFeeTier(uint24 newFeeTier) external onlyOwner {\\n        _getTickSpacingInternal(newFeeTier); // Will revert if invalid\\n        activeFeeTier = newFeeTier;\\n    }\\n\\n    function calculateInitialVirtualHypeReserves(uint256 threshold) public view returns (uint256) {\\n        uint256 remainTokenReserves = config.remainTokenReserves;\\n        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;\\n\\n        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();\\n\\n        return\\n            (threshold * remainTokenReserves) / (initialVirtualTokenReserves - remainTokenReserves);\\n    }\\n\\n    function calculateVirtualRemainTokenReserves() public view returns (uint256) {\\n        uint256 remainTokenReserves = config.remainTokenReserves;\\n        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;\\n\\n        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();\\n\\n        return (remainTokenReserves * initialVirtualTokenReserves)\\n            / (initialVirtualTokenReserves - remainTokenReserves);\\n    }\\n\\n    function calculateActualVirtualTokenReserves() public view returns (uint256) {\\n        uint256 remainTokenReserves = config.remainTokenReserves;\\n        uint256 initialVirtualTokenReserves = config.initialVirtualTokenReserves;\\n\\n        if (initialVirtualTokenReserves <= remainTokenReserves) revert InvalidInput();\\n\\n        return (initialVirtualTokenReserves * initialVirtualTokenReserves)\\n            / (initialVirtualTokenReserves - remainTokenReserves);\\n    }\\n\\n    function getPool(address token) external view returns (Pool memory) {\\n        return pools[token];\\n    }\\n\\n    /**\\n     * @notice Check if a token exists (has been created through the launchpad)\\n     * @param token The token address to check\\n     * @return exists True if the token exists\\n     */\\n    function isToken(address token) public view returns (bool exists) {\\n        return pools[token].creationTimestamp != 0;\\n    }\\n\\n    function estimateBuyExactInTokens(\\n        address token,\\n        uint256 amountIn\\n    )\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        if (!isToken(token)) return 0;\\n        Pool memory pool = pools[token];\\n        if (pool.isCompleted) return 0;\\n\\n        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;\\n        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves\\n            ? pool.virtualTokenReserves - virtualRemainTokenReserves\\n            : 0;\\n\\n        uint256 initialTokenOut = BondingCurveMath.calculateTokensForExactBase(\\n            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn\\n        );\\n\\n        return initialTokenOut > tokenReservesInPool ? tokenReservesInPool : initialTokenOut;\\n    }\\n\\n    function estimateBuyExactInCost(\\n        address token,\\n        uint256 amountIn\\n    )\\n        external\\n        view\\n        returns (uint256 totalCost)\\n    {\\n        if (!isToken(token)) return 0;\\n        Pool memory pool = pools[token];\\n        if (pool.isCompleted) return 0;\\n\\n        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;\\n        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves\\n            ? pool.virtualTokenReserves - virtualRemainTokenReserves\\n            : 0;\\n\\n        (, uint256 hypeAmountInSwap, uint256 fee) = _calculateBuyExactInAmounts(\\n            pool.virtualHypeReserves, pool.virtualTokenReserves, amountIn, tokenReservesInPool\\n        );\\n\\n        return hypeAmountInSwap + fee;\\n    }\\n\\n    function estimateBuyExactOutCost(\\n        address token,\\n        uint256 tokenAmountOut\\n    )\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        if (!isToken(token)) return 0;\\n        Pool memory pool = pools[token];\\n        if (pool.isCompleted) return 0;\\n\\n        uint256 virtualRemainTokenReserves = pool.virtualRemainTokenReserves;\\n        uint256 tokenReservesInPool = pool.virtualTokenReserves > virtualRemainTokenReserves\\n            ? pool.virtualTokenReserves - virtualRemainTokenReserves\\n            : 0;\\n\\n        (, uint256 hypeCostBeforeFee, uint256 fee) = _calculateBuyExactOutAmounts(\\n            pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut, tokenReservesInPool\\n        );\\n\\n        return hypeCostBeforeFee + fee;\\n    }\\n\\n    function estimateSellTokens(\\n        address token,\\n        uint256 tokenAmountIn\\n    )\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        if (!isToken(token)) return 0;\\n        Pool memory pool = pools[token];\\n        if (pool.isCompleted) return 0;\\n\\n        (, uint256 netAmount,) = _calculateSellExactInAmounts(\\n            pool.virtualHypeReserves,\\n            pool.virtualTokenReserves,\\n            tokenAmountIn,\\n            pool.realHypeReserves\\n        );\\n\\n        return netAmount;\\n    }\\n\\n    function _buyDirectAndLock(\\n        address token,\\n        uint256 amountIn,\\n        uint256 amountOut,\\n        uint256 lockDuration,\\n        address recipient\\n    )\\n        internal\\n        returns (uint256 tokenAmountOut)\\n    {\\n        Pool storage pool = pools[token];\\n        if (pool.isCompleted) revert PoolAlreadyCompleted();\\n        if (amountOut == 0) revert InvalidInput();\\n        if (lockDuration < MIN_LOCK_DURATION) revert InvalidInput();\\n\\n        uint256 tokenReservesInPool = pool.virtualTokenReserves - pool.virtualRemainTokenReserves;\\n        tokenAmountOut = amountOut > tokenReservesInPool ? tokenReservesInPool : amountOut;\\n\\n        uint256 amountInSwap = BondingCurveMath.calculateBaseCostForExactTokens(\\n            pool.virtualHypeReserves, pool.virtualTokenReserves, tokenAmountOut\\n        ) + 1;\\n\\n        uint256 swapFee = (amountInSwap * config.platformFee) / FEE_DENOMINATOR;\\n        uint256 totalSwapCost = amountInSwap + swapFee;\\n\\n        if (amountIn < totalSwapCost) revert InsufficientAmount();\\n\\n        _executeSwap(pool, 0, amountInSwap, tokenAmountOut, 0);\\n        pool.virtualTokenReserves = pool.virtualTokenReserves - tokenAmountOut;\\n        pool.feeRecipient += swapFee;\\n\\n        LaunchpadToken(token).mint(address(this), tokenAmountOut);\\n        IERC20(token).safeIncreaseAllowance(address(tokenLock), tokenAmountOut);\\n        uint256 endTime = block.timestamp + lockDuration;\\n        tokenLock.createLock(token, tokenAmountOut, endTime, recipient);\\n\\n        uint256 refundAmount = amountIn - totalSwapCost;\\n        if (refundAmount > 0) {\\n            payable(msg.sender).transfer(refundAmount);\\n        }\\n\\n        emit Trade(\\n            token,\\n            msg.sender,\\n            true,\\n            amountInSwap,\\n            tokenAmountOut,\\n            pool.virtualHypeReserves,\\n            pool.virtualTokenReserves,\\n            pool.realHypeReserves,\\n            pool.realTokenReserves,\\n            swapFee,\\n            block.timestamp\\n        );\\n\\n        // Check if pool should be completed\\n        if (tokenAmountOut == tokenReservesInPool) {\\n            _completePool(token, pool);\\n        }\\n\\n        return tokenAmountOut;\\n    }\\n\\n    /**\\n     * @notice Migrate liquidity to HyperSwap V3 pool using Uniswap V3 standard parameters\\n     * @param token The token address\\n     * @param pool The pool data\\n     */\\n    function _migrateToHyperSwapV3(address token, Pool storage pool) internal virtual {\\n        (uint256 tokenAmount, uint256 hypeAmount) =\\n            (pool.realTokenReserves + pool.remainTokenReserves, pool.realHypeReserves);\\n        pool.realHypeReserves = 0;\\n        pool.realTokenReserves = 0;\\n        pool.remainTokenReserves = 0;\\n\\n        (address token0, address token1) = token < weth9 ? (token, weth9) : (weth9, token);\\n        (uint256 amount0Desired, uint256 amount1Desired) =\\n            token < weth9 ? (tokenAmount, hypeAmount) : (hypeAmount, tokenAmount);\\n\\n        uint160 sqrtPriceX96 = _calculateInitialSqrtPrice(amount0Desired, amount1Desired);\\n\\n        address v3Pool = nonfungiblePositionManager.createAndInitializePoolIfNecessary(\\n            token0, token1, activeFeeTier, sqrtPriceX96\\n        );\\n\\n        emit V3PoolCreated(token, v3Pool, block.timestamp);\\n\\n        LaunchpadToken(token).setListed(true);\\n        LaunchpadToken(token).mint(address(this), tokenAmount);\\n\\n        IWETH9(weth9).deposit{ value: hypeAmount }();\\n        IERC20(weth9).safeIncreaseAllowance(address(nonfungiblePositionManager), hypeAmount);\\n        IERC20(token).safeIncreaseAllowance(address(nonfungiblePositionManager), tokenAmount);\\n\\n        (uint256 tokenId,,,) = _mintV3Position(token0, token1, amount0Desired, amount1Desired);\\n\\n        tokenToPositionId[token] = tokenId;\\n    }\\n\\n    /**\\n     * @notice Mint V3 liquidity position\\n     * @dev Uses standard fee tiers and full-range tick calculations based on tick spacing\\n     */\\n    function _mintV3Position(\\n        address token0,\\n        address token1,\\n        uint256 amount0Desired,\\n        uint256 amount1Desired\\n    )\\n        internal\\n        returns (uint256 tokenId, uint128 liquidity, uint256 amount0, uint256 amount1)\\n    {\\n        uint24 feeTier = activeFeeTier;\\n        int24 tickSpacing = _getTickSpacingInternal(feeTier);\\n\\n        int24 tickLower = (TickMath.MIN_TICK / tickSpacing) * tickSpacing;\\n        int24 tickUpper = (TickMath.MAX_TICK / tickSpacing) * tickSpacing;\\n\\n        INonfungiblePositionManager.MintParams memory params = INonfungiblePositionManager\\n            .MintParams({\\n            token0: token0,\\n            token1: token1,\\n            fee: feeTier,\\n            tickLower: tickLower,\\n            tickUpper: tickUpper,\\n            amount0Desired: amount0Desired,\\n            amount1Desired: amount1Desired,\\n            amount0Min: 0,\\n            amount1Min: 0,\\n            recipient: address(this),\\n            deadline: block.timestamp + 300\\n        });\\n\\n        return nonfungiblePositionManager.mint(params);\\n    }\\n\\n    /**\\n     * @notice Internal tick spacing calculation\\n     */\\n    function _getTickSpacingInternal(uint24 fee) internal pure returns (int24 tickSpacing) {\\n        if (fee == FEE_LOW) {\\n            // 500 = 0.05%\\n            return 10;\\n        } else if (fee == FEE_MEDIUM) {\\n            // 3000 = 0.3%\\n            return 60;\\n        } else if (fee == FEE_HIGH) {\\n            // 10000 = 1%\\n            return 200;\\n        } else {\\n            revert InvalidInput();\\n        }\\n    }\\n\\n    /**\\n     * @notice Calculate the initial sqrt price for a V3 pool\\n     * @param amount0Desired Amount of token0 to be paired\\n     * @param amount1Desired Amount of token1 to be paired\\n     * @return sqrtPriceX96 The sqrt price in Q64.96 format for pool initialization\\n     */\\n    function _calculateInitialSqrtPrice(\\n        uint256 amount0Desired,\\n        uint256 amount1Desired\\n    )\\n        internal\\n        pure\\n        returns (uint160 sqrtPriceX96)\\n    {\\n        uint256 priceX96 =\\n            FullMath.mulDivRoundingUp(PRICE_SCALE_192, amount1Desired, amount0Desired);\\n        return uint160(sqrt(priceX96));\\n    }\\n\\n    /**\\n     * @notice Babylonian sqrt implementation\\n     * @param y The value to calculate sqrt for\\n     * @return z The square root of y\\n     */\\n    function sqrt(uint256 y) internal pure returns (uint256 z) {\\n        if (y > 3) {\\n            z = y;\\n            uint256 x = y / 2 + 1;\\n            while (x < z) {\\n                z = x;\\n                x = (y / x + x) / 2;\\n            }\\n        } else if (y != 0) {\\n            z = 1;\\n        }\\n        // else z = 0 (default value)\\n    }\\n\\n    /**\\n     * @notice Handle NFT transfers (required for IERC721Receiver)\\n     */\\n    function onERC721Received(\\n        address,\\n        address,\\n        uint256,\\n        bytes calldata\\n    )\\n        external\\n        view\\n        override\\n        returns (bytes4)\\n    {\\n        if (msg.sender != address(nonfungiblePositionManager)) {\\n            revert Unauthorized();\\n        }\\n        return this.onERC721Received.selector;\\n    }\\n\\n    /**\\n     * @notice Receive function to accept ETH\\n     */\\n    receive() external payable { }\\n}\\n\",\"keccak256\":\"0x29ad3f2f2b48e8f1019d667939d1a198d67ac6625b1688fb56401d097e04af58\",\"license\":\"MIT\"},\"contracts/MoonbagsStake.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/math/Math.sol\\\";\\n\\n/**\\n * @title Moonbags Staking\\n */\\ncontract MoonbagsStake is Initializable, ReentrancyGuardUpgradeable, OwnableUpgradeable {\\n    using SafeERC20 for IERC20;\\n    using Math for uint256;\\n\\n    // === Constants ===\\n    uint256 private constant MULTIPLIER = 1e18;\\n    uint256 public constant DEFAULT_DENY_UNSTAKE_DURATION = 1 hours;\\n\\n    // === Structs ===\\n    struct Configuration {\\n        address admin;\\n        uint256 denyUnstakeDuration; // Duration in seconds users must wait before unstaking\\n    }\\n\\n    struct StakingPool {\\n        address initializer;\\n        uint256 totalSupply;\\n        uint256 rewardIndex;\\n        uint256 pendingInitialRewards;\\n        uint256 totalRewards; // Total HYPE rewards in pool\\n    }\\n\\n    struct CreatorPool {\\n        address initializer;\\n        address creator;\\n        uint256 totalRewards; // Total HYPE rewards for creator\\n    }\\n\\n    struct StakingAccount {\\n        address staker;\\n        uint256 balance;\\n        uint256 rewardIndex;\\n        uint256 earned;\\n        uint256 unstakeDeadline;\\n    }\\n\\n    // === State Variables ===\\n    Configuration public config;\\n\\n    // Mapping from token address to staking pool\\n    mapping(address => StakingPool) public stakingPools;\\n\\n    // Mapping from token address to creator pool\\n    mapping(address => CreatorPool) public creatorPools;\\n\\n    // Mapping from token address to user address to staking account\\n    mapping(address => mapping(address => StakingAccount)) public stakingAccounts;\\n\\n    // === Events ===\\n    event InitializeStakingPoolEvent(\\n        address indexed tokenAddress, address indexed initializer, uint256 timestamp\\n    );\\n\\n    event InitializeCreatorPoolEvent(\\n        address indexed tokenAddress,\\n        address indexed initializer,\\n        address indexed creator,\\n        uint256 timestamp\\n    );\\n\\n    event StakeEvent(\\n        address indexed tokenAddress, address indexed staker, uint256 amount, uint256 timestamp\\n    );\\n\\n    event UnstakeEvent(\\n        address indexed tokenAddress,\\n        address indexed unstaker,\\n        uint256 amount,\\n        bool isStakingAccountDeleted,\\n        uint256 timestamp\\n    );\\n\\n    event UpdateRewardIndexEvent(\\n        address indexed tokenAddress,\\n        address indexed rewardUpdater,\\n        uint256 reward,\\n        bool isInitialRewards,\\n        uint256 timestamp\\n    );\\n\\n    event DepositPoolCreatorEvent(\\n        address indexed tokenAddress, address indexed depositor, uint256 amount, uint256 timestamp\\n    );\\n\\n    event ClaimStakingPoolEvent(\\n        address indexed tokenAddress,\\n        address indexed claimer,\\n        uint256 reward,\\n        bool isStakingAccountDeleted,\\n        uint256 timestamp\\n    );\\n\\n    event ClaimCreatorPoolEvent(\\n        address indexed tokenAddress, address indexed claimer, uint256 reward, uint256 timestamp\\n    );\\n\\n    // === Custom Errors ===\\n    error StakingPoolNotExist();\\n    error StakingCreatorNotExist();\\n    error StakingAccountNotExist();\\n    error AccountBalanceNotEnough();\\n    error InvalidCreator();\\n    error InvalidAmount();\\n    error RewardToClaimNotValid();\\n    error UnstakeDeadlineNotAllow();\\n    error NotUpgrade();\\n    error ZeroAddress();\\n    error StakingPoolAlreadyExists();\\n    error CreatorPoolAlreadyExists();\\n    error InvalidTokenAddress();\\n    error OnlyAdmin();\\n\\n    // === Modifiers ===\\n    modifier onlyAdmin() {\\n        if (msg.sender != config.admin) revert OnlyAdmin();\\n        _;\\n    }\\n\\n    // === Initialize Function ===\\n    /**\\n     * @notice Initialize the contract\\n     */\\n    function initialize() public initializer {\\n        __ReentrancyGuard_init();\\n        __Ownable_init(msg.sender);\\n\\n        config =\\n            Configuration({ admin: msg.sender, denyUnstakeDuration: DEFAULT_DENY_UNSTAKE_DURATION });\\n    }\\n\\n    // === Public Functions ===\\n\\n    /**\\n     * @notice Initializes a new staking pool for a specific token\\n     * @param stakingToken The ERC20 token that will be staked in this pool\\n     */\\n    function initializeStakingPool(address stakingToken) external {\\n        if (stakingToken == address(0)) revert InvalidTokenAddress();\\n\\n        if (stakingPoolExists(stakingToken)) {\\n            return;\\n        }\\n\\n        stakingPools[stakingToken] = StakingPool({\\n            initializer: msg.sender,\\n            totalSupply: 0,\\n            rewardIndex: 0,\\n            pendingInitialRewards: 0,\\n            totalRewards: 0\\n        });\\n\\n        emit InitializeStakingPoolEvent(stakingToken, msg.sender, block.timestamp);\\n    }\\n\\n    /**\\n     * @notice Initializes a creator pool for a specific token\\n     * @param stakingToken The ERC20 token associated with this creator pool\\n     * @param creator Address of the creator for this pool\\n     */\\n    function initializeCreatorPool(address stakingToken, address creator) external {\\n        if (stakingToken == address(0)) revert InvalidTokenAddress();\\n        if (creator == address(0)) revert ZeroAddress();\\n\\n        if (creatorPoolExists(stakingToken)) {\\n            return;\\n        }\\n\\n        creatorPools[stakingToken] =\\n            CreatorPool({ initializer: msg.sender, creator: creator, totalRewards: 0 });\\n\\n        emit InitializeCreatorPoolEvent(stakingToken, msg.sender, creator, block.timestamp);\\n    }\\n\\n    /**\\n     * @notice Updates the reward index of a staking pool by adding new rewards\\n     * @param stakingToken The token associated with the staking pool\\n     */\\n    function updateRewardIndex(address stakingToken) external payable {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (msg.value == 0) revert InvalidAmount();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        uint256 rewardAmount = msg.value;\\n\\n        // No stakers - add to pending initial rewards\\n        if (pool.totalSupply == 0) {\\n            pool.pendingInitialRewards += rewardAmount;\\n            pool.totalRewards += rewardAmount;\\n\\n            emit UpdateRewardIndexEvent(\\n                stakingToken,\\n                msg.sender,\\n                rewardAmount,\\n                true, // is initial rewards\\n                block.timestamp\\n            );\\n            return;\\n        }\\n\\n        // Update reward index\\n        pool.rewardIndex += (rewardAmount * MULTIPLIER) / pool.totalSupply;\\n        pool.totalRewards += rewardAmount;\\n\\n        emit UpdateRewardIndexEvent(\\n            stakingToken,\\n            msg.sender,\\n            rewardAmount,\\n            false, // not initial rewards\\n            block.timestamp\\n        );\\n    }\\n\\n    /**\\n     * @notice Deposits HYPE rewards into a creator pool\\n     * @param stakingToken The token associated with the creator pool\\n     */\\n    function depositCreatorPool(address stakingToken) external payable {\\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\\n        if (msg.value == 0) revert InvalidAmount();\\n\\n        CreatorPool storage pool = creatorPools[stakingToken];\\n        pool.totalRewards += msg.value;\\n\\n        emit DepositPoolCreatorEvent(stakingToken, msg.sender, msg.value, block.timestamp);\\n    }\\n\\n    /**\\n     * @notice Stakes tokens in a staking pool\\n     * @param stakingToken The token to stake\\n     * @param amount Amount of tokens to stake\\n     */\\n    function stake(address stakingToken, uint256 amount) external nonReentrant {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (amount == 0) revert InvalidAmount();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n\\n        IERC20(stakingToken).safeTransferFrom(msg.sender, address(this), amount);\\n\\n        // Initialize staking account if first time staking\\n        if (!stakingAccountExists(stakingToken, msg.sender)) {\\n            stakingAccounts[stakingToken][msg.sender] = StakingAccount({\\n                staker: msg.sender,\\n                balance: 0,\\n                rewardIndex: 0,\\n                earned: pool.pendingInitialRewards,\\n                unstakeDeadline: 0\\n            });\\n\\n            // Reset pending initial rewards since first staker gets them\\n            pool.pendingInitialRewards = 0;\\n        }\\n\\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\\n\\n        // Update rewards before staking\\n        _updateRewards(pool.rewardIndex, account);\\n\\n        // Update staking account\\n        uint256 currentTime = block.timestamp;\\n        account.unstakeDeadline = currentTime + config.denyUnstakeDuration;\\n        account.balance += amount;\\n        pool.totalSupply += amount;\\n\\n        emit StakeEvent(stakingToken, msg.sender, amount, currentTime);\\n    }\\n\\n    /**\\n     * @notice Unstakes tokens from a staking pool\\n     * @param stakingToken The token to unstake\\n     * @param unstakeAmount Amount of tokens to unstake\\n     */\\n    function unstake(address stakingToken, uint256 unstakeAmount) external nonReentrant {\\n        if (unstakeAmount == 0) revert InvalidAmount();\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\\n\\n        uint256 currentTime = block.timestamp;\\n        if (currentTime < account.unstakeDeadline) revert UnstakeDeadlineNotAllow();\\n\\n        // Update rewards before unstaking\\n        _updateRewards(pool.rewardIndex, account);\\n\\n        if (account.balance < unstakeAmount) revert AccountBalanceNotEnough();\\n\\n        // Update balances\\n        account.balance -= unstakeAmount;\\n        pool.totalSupply -= unstakeAmount;\\n\\n        IERC20(stakingToken).safeTransfer(msg.sender, unstakeAmount);\\n\\n        bool isAccountDeleted = _tryCleanupEmptyAccount(stakingToken, msg.sender);\\n\\n        emit UnstakeEvent(stakingToken, msg.sender, unstakeAmount, isAccountDeleted, currentTime);\\n    }\\n\\n    /**\\n     * @notice Claims rewards from a staking pool\\n     * @param stakingToken The token associated with the staking pool\\n     * @return rewardAmount The amount of HYPE claimed as rewards\\n     */\\n    function claimStakingPool(address stakingToken)\\n        external\\n        nonReentrant\\n        returns (uint256 rewardAmount)\\n    {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\\n\\n        // Update rewards before claiming\\n        _updateRewards(pool.rewardIndex, account);\\n\\n        rewardAmount = account.earned;\\n        if (rewardAmount == 0) revert RewardToClaimNotValid();\\n\\n        account.earned = 0;\\n        pool.totalRewards -= rewardAmount;\\n\\n        payable(msg.sender).transfer(rewardAmount);\\n\\n        bool isAccountDeleted = _tryCleanupEmptyAccount(stakingToken, msg.sender);\\n\\n        emit ClaimStakingPoolEvent(\\n            stakingToken, msg.sender, rewardAmount, isAccountDeleted, block.timestamp\\n        );\\n\\n        return rewardAmount;\\n    }\\n\\n    /**\\n     * @notice Claims rewards from a creator pool\\n     * @param stakingToken The token associated with the creator pool\\n     * @return rewardAmount The amount of HYPE claimed from the creator pool\\n     */\\n    function claimCreatorPool(address stakingToken)\\n        external\\n        nonReentrant\\n        returns (uint256 rewardAmount)\\n    {\\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\\n\\n        CreatorPool storage pool = creatorPools[stakingToken];\\n        if (pool.creator != msg.sender) revert InvalidCreator();\\n\\n        rewardAmount = pool.totalRewards;\\n        if (rewardAmount == 0) revert RewardToClaimNotValid();\\n\\n        pool.totalRewards = 0;\\n\\n        payable(msg.sender).transfer(rewardAmount);\\n\\n        emit ClaimCreatorPoolEvent(stakingToken, msg.sender, rewardAmount, block.timestamp);\\n\\n        return rewardAmount;\\n    }\\n\\n    // === View Functions ===\\n\\n    /**\\n     * @notice Check if a staking account exists for a user\\n     * @param token The token address\\n     * @param user The user address\\n     * @return exists True if the staking account exists\\n     */\\n    function stakingAccountExists(address token, address user) public view returns (bool exists) {\\n        return stakingAccounts[token][user].staker != address(0);\\n    }\\n\\n    /**\\n     * @notice Check if a staking pool exists for a token\\n     * @param token The token address\\n     * @return exists True if the staking pool exists\\n     */\\n    function stakingPoolExists(address token) public view returns (bool exists) {\\n        return stakingPools[token].initializer != address(0);\\n    }\\n\\n    /**\\n     * @notice Check if a creator pool exists for a token\\n     * @param token The token address\\n     * @return exists True if the creator pool exists\\n     */\\n    function creatorPoolExists(address token) public view returns (bool exists) {\\n        return creatorPools[token].initializer != address(0);\\n    }\\n\\n    /**\\n     * @notice Calculates the rewards earned by the caller for staking tokens\\n     * @param stakingToken The token associated with the staking pool\\n     * @return totalEarned The total amount of rewards earned\\n     */\\n    function calculateRewardsEarned(address stakingToken)\\n        external\\n        view\\n        returns (uint256 totalEarned)\\n    {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];\\n\\n        uint256 pendingRewards = _calculateRewards(pool.rewardIndex, account);\\n        return account.earned + pendingRewards;\\n    }\\n\\n    /**\\n     * @notice Get staking pool information\\n     * @param stakingToken The token associated with the staking pool\\n     * @return initializer Address that initialized the pool\\n     * @return totalSupply Total amount of tokens staked\\n     * @return rewardIndex Current reward index\\n     * @return pendingInitialRewards Pending initial rewards\\n     * @return totalRewards Total HYPE rewards in pool\\n     */\\n    function getStakingPoolInfo(address stakingToken)\\n        external\\n        view\\n        returns (\\n            address initializer,\\n            uint256 totalSupply,\\n            uint256 rewardIndex,\\n            uint256 pendingInitialRewards,\\n            uint256 totalRewards\\n        )\\n    {\\n        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();\\n\\n        StakingPool storage pool = stakingPools[stakingToken];\\n        return (\\n            pool.initializer,\\n            pool.totalSupply,\\n            pool.rewardIndex,\\n            pool.pendingInitialRewards,\\n            pool.totalRewards\\n        );\\n    }\\n\\n    /**\\n     * @notice Get creator pool information\\n     * @param stakingToken The token associated with the creator pool\\n     * @return initializer Address that initialized the pool\\n     * @return creator Address of the creator\\n     * @return totalRewards Total HYPE rewards for creator\\n     */\\n    function getCreatorPoolInfo(address stakingToken)\\n        external\\n        view\\n        returns (address initializer, address creator, uint256 totalRewards)\\n    {\\n        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();\\n\\n        CreatorPool storage pool = creatorPools[stakingToken];\\n        return (pool.initializer, pool.creator, pool.totalRewards);\\n    }\\n\\n    /**\\n     * @notice Get staking account information\\n     * @param stakingToken The token associated with the staking pool\\n     * @param staker Address of the staker\\n     * @return balance Staked token balance\\n     * @return rewardIndex Last reward index when rewards were updated\\n     * @return earned Earned rewards ready to claim\\n     * @return unstakeDeadline Timestamp when unstaking is allowed\\n     */\\n    function getStakingAccountInfo(\\n        address stakingToken,\\n        address staker\\n    )\\n        external\\n        view\\n        returns (uint256 balance, uint256 rewardIndex, uint256 earned, uint256 unstakeDeadline)\\n    {\\n        if (!stakingAccountExists(stakingToken, staker)) revert StakingAccountNotExist();\\n\\n        StakingAccount storage account = stakingAccounts[stakingToken][staker];\\n        return (account.balance, account.rewardIndex, account.earned, account.unstakeDeadline);\\n    }\\n\\n    // === Admin Functions ===\\n\\n    /**\\n     * @notice Update configuration\\n     * @param newDenyUnstakeDuration New deny unstake duration\\n     */\\n    function updateConfig(address newAdmin, uint256 newDenyUnstakeDuration) external onlyAdmin {\\n        config.admin = newAdmin;\\n        config.denyUnstakeDuration = newDenyUnstakeDuration;\\n    }\\n\\n    // === Private Functions ===\\n\\n    /**\\n     * @notice Calculates the pending rewards for a staking account\\n     * @param stakingPoolRewardIndex Current reward index of the staking pool\\n     * @param account The staking account to calculate rewards for\\n     * @return reward The amount of pending rewards\\n     */\\n    function _calculateRewards(\\n        uint256 stakingPoolRewardIndex,\\n        StakingAccount storage account\\n    )\\n        private\\n        view\\n        returns (uint256 reward)\\n    {\\n        if (account.balance == 0) {\\n            return 0;\\n        }\\n\\n        uint256 rewardDiff = stakingPoolRewardIndex - account.rewardIndex;\\n        reward = (account.balance * rewardDiff) / MULTIPLIER;\\n\\n        return reward;\\n    }\\n\\n    /**\\n     * @notice Updates the rewards earned by a staking account based on the current reward index\\n     * @param stakingPoolRewardIndex Current reward index of the staking pool\\n     * @param account The staking account to update rewards for\\n     */\\n    function _updateRewards(\\n        uint256 stakingPoolRewardIndex,\\n        StakingAccount storage account\\n    )\\n        private\\n    {\\n        uint256 pendingRewards = _calculateRewards(stakingPoolRewardIndex, account);\\n        account.earned += pendingRewards;\\n        account.rewardIndex = stakingPoolRewardIndex;\\n    }\\n\\n    /**\\n     * @notice Attempts to clean up a staking account if it has zero balance and zero earned rewards\\n     * @param stakingToken The token associated with the staking pool\\n     * @param staker The address of the staker whose account should be checked\\n     * @return isDeleted Whether the account was successfully deleted\\n     */\\n    function _tryCleanupEmptyAccount(\\n        address stakingToken,\\n        address staker\\n    )\\n        private\\n        returns (bool isDeleted)\\n    {\\n        if (!stakingAccountExists(stakingToken, staker)) {\\n            return false;\\n        }\\n\\n        StakingAccount storage account = stakingAccounts[stakingToken][staker];\\n\\n        if (account.balance == 0 && account.earned == 0) {\\n            delete stakingAccounts[stakingToken][staker];\\n            return true;\\n        }\\n\\n        return false;\\n    }\\n}\\n\",\"keccak256\":\"0xe0d2b89433d56c12d28bf09b6e0a4a296b585723b6eaf43f0d3b2784181d7d3e\",\"license\":\"MIT\"},\"contracts/TokenLock.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\nimport \\\"@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\\\";\\n\\n/**\\n * @title TokenLock\\n */\\ncontract TokenLock is Initializable, ReentrancyGuardUpgradeable, OwnableUpgradeable {\\n    using SafeERC20 for IERC20;\\n\\n    struct Configuration {\\n        address admin;\\n    }\\n\\n    Configuration public config;\\n\\n    struct LockContract {\\n        address token;\\n        uint256 amount;\\n        uint256 startTime;\\n        uint256 endTime;\\n        address recipient;\\n        address locker;\\n        bool closed;\\n    }\\n\\n    mapping(uint256 => LockContract) public locks;\\n    uint256 public nextLockId;\\n\\n    // Events\\n    event LockCreated(\\n        uint256 indexed lockId,\\n        address indexed locker,\\n        address indexed recipient,\\n        address tokenAddress,\\n        uint256 amount,\\n        uint256 startTime,\\n        uint256 endTime\\n    );\\n\\n    event TokensWithdrawn(\\n        uint256 indexed lockId, address indexed sender, address indexed recipient, uint256 amount\\n    );\\n\\n    event ConfigUpdated(address oldAdmin, address newAdmin);\\n\\n    // Errors\\n    error InvalidParams();\\n    error Unauthorized();\\n    error ContractClosed();\\n    error InvalidLockId();\\n\\n    /**\\n     * @notice Initialize the contract\\n     */\\n    function initialize() external initializer {\\n        __ReentrancyGuard_init();\\n        __Ownable_init(msg.sender);\\n\\n        config = Configuration({ admin: msg.sender });\\n        nextLockId = 1;\\n    }\\n\\n    /**\\n     * @notice Create a new time-locked token contract\\n     * @param token Address of the ERC20 token to lock\\n     * @param amount Amount of tokens to lock\\n     * @param endTime End time of the lock in seconds\\n     * @param recipient Address that will be able to claim the tokens after the lock period\\n     * @return lockId The ID of the created lock\\n     */\\n    function createLock(\\n        address token,\\n        uint256 amount,\\n        uint256 endTime,\\n        address recipient\\n    )\\n        external\\n        nonReentrant\\n        returns (uint256 lockId)\\n    {\\n        uint256 startTime = block.timestamp;\\n\\n        if (endTime <= startTime) revert InvalidParams();\\n        if (token == address(0) || recipient == address(0)) revert InvalidParams();\\n\\n        lockId = nextLockId++;\\n        locks[lockId] = LockContract({\\n            token: token,\\n            amount: amount,\\n            startTime: startTime,\\n            endTime: endTime,\\n            recipient: recipient,\\n            locker: msg.sender,\\n            closed: false\\n        });\\n\\n        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);\\n\\n        emit LockCreated(lockId, msg.sender, recipient, token, amount, startTime, endTime);\\n\\n        return lockId;\\n    }\\n\\n    /**\\n     * @notice Withdraw tokens from a lock after the lock period has ended\\n     * @param lockId ID of the lock to withdraw from\\n     */\\n    function withdraw(uint256 lockId) external nonReentrant {\\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\\n\\n        LockContract storage lockContract = locks[lockId];\\n        address sender = msg.sender;\\n\\n        if (\\n            sender != config.admin && sender != lockContract.recipient\\n                && sender != lockContract.locker\\n        ) {\\n            revert Unauthorized();\\n        }\\n        if (lockContract.closed) revert ContractClosed();\\n        if (block.timestamp < lockContract.endTime) revert Unauthorized();\\n\\n        lockContract.closed = true;\\n        IERC20(lockContract.token).safeTransfer(lockContract.recipient, lockContract.amount);\\n\\n        emit TokensWithdrawn(lockId, sender, lockContract.recipient, lockContract.amount);\\n    }\\n\\n    /**\\n     * @notice Extend the lock duration of an existing time-locked token contract\\n     * @param lockId ID of the lock to extend\\n     * @param newEndTime New end time for the lock\\n     */\\n    function extendLock(uint256 lockId, uint256 newEndTime) external {\\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\\n\\n        LockContract storage lockContract = locks[lockId];\\n        address sender = msg.sender;\\n\\n        if (sender != lockContract.locker) revert Unauthorized();\\n        if (lockContract.closed) revert ContractClosed();\\n\\n        uint256 currentTime = block.timestamp;\\n        if (newEndTime <= currentTime || newEndTime <= lockContract.endTime) {\\n            revert InvalidParams();\\n        }\\n\\n        lockContract.endTime = newEndTime;\\n\\n        emit LockCreated(\\n            lockId,\\n            lockContract.locker,\\n            lockContract.recipient,\\n            lockContract.token,\\n            lockContract.amount,\\n            lockContract.startTime,\\n            lockContract.endTime\\n        );\\n    }\\n\\n    /**\\n     * @notice Update configuration\\n     * @param newAdmin New admin address\\n     */\\n    function updateConfig(address newAdmin) external onlyOwner {\\n        if (newAdmin == address(0)) revert InvalidParams();\\n\\n        address oldAdmin = config.admin;\\n\\n        config.admin = newAdmin;\\n\\n        emit ConfigUpdated(oldAdmin, newAdmin);\\n    }\\n\\n    /**\\n     * @notice Get lock information\\n     * @param lockId ID of the lock\\n     * @return lockContract The lock contract details\\n     */\\n    function getLock(uint256 lockId) external view returns (LockContract memory lockContract) {\\n        if (lockId == 0 || lockId >= nextLockId) revert InvalidLockId();\\n        return locks[lockId];\\n    }\\n\\n    /**\\n     * @notice Check if a lock is withdrawable\\n     * @param lockId ID of the lock\\n     * @return withdrawable True if the lock can be withdrawn\\n     */\\n    function isWithdrawable(uint256 lockId) external view returns (bool withdrawable) {\\n        if (lockId == 0 || lockId >= nextLockId) return false;\\n\\n        LockContract memory lockContract = locks[lockId];\\n        return !lockContract.closed && block.timestamp >= lockContract.endTime;\\n    }\\n\\n    /**\\n     * @notice Get the number of locks created\\n     * @return count Total number of locks\\n     */\\n    function getLockCount() external view returns (uint256 count) {\\n        return nextLockId - 1;\\n    }\\n\\n    /**\\n     * @notice Get current configuration\\n     * @return admin Current admin address\\n     */\\n    function getConfig() external view returns (address admin) {\\n        return config.admin;\\n    }\\n\\n    /**\\n     * @notice Emergency withdraw function (admin only)\\n     * @param token Token address to withdraw\\n     * @param amount Amount to withdraw\\n     */\\n    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {\\n        IERC20(token).safeTransfer(config.admin, amount);\\n    }\\n}\\n\",\"keccak256\":\"0xb16fdd788b49ab321fb26fc15572e783f467e2124d61d92224cbd1f79770ffae\",\"license\":\"MIT\"},\"contracts/interfaces/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\",\"keccak256\":\"0x4f3d0518462c8510e745fe7dd7004026a904a781aef6e103d9ec8dd7a987d275\",\"license\":\"MIT\"},\"contracts/interfaces/IERC721.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport \\\"./IERC165.sol\\\";\\n\\n/**\\n * @dev Required interface of an ERC721 compliant contract.\\n */\\ninterface IERC721 is IERC165 {\\n    /**\\n     * @dev Emitted when `tokenId` token is transferred from `from` to `to`.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 indexed tokenId);\\n\\n    /**\\n     * @dev Emitted when `owner` enables `approved` to manage the `tokenId` token.\\n     */\\n    event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId);\\n\\n    /**\\n     * @dev Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its\\n     * assets.\\n     */\\n    event ApprovalForAll(address indexed owner, address indexed operator, bool approved);\\n\\n    /**\\n     * @dev Returns the number of tokens in ``owner``'s account.\\n     */\\n    function balanceOf(address owner) external view returns (uint256 balance);\\n\\n    /**\\n     * @dev Returns the owner of the `tokenId` token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function ownerOf(uint256 tokenId) external view returns (address owner);\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If the caller is not `from`, it must have been allowed to move this token by either\\n     * {approve} or {setApprovalForAll}.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received},\\n     * which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes calldata data\\n    )\\n        external;\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract\\n     * recipients\\n     * are aware of the ERC721 protocol to prevent tokens from being forever locked.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If the caller is not `from`, it must have been allowed to move this token by either\\n     * {approve} or {setApprovalForAll}.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received},\\n     * which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function safeTransferFrom(address from, address to, uint256 tokenId) external;\\n\\n    /**\\n     * @dev Transfers `tokenId` token from `from` to `to`.\\n     *\\n     * WARNING: Note that the caller is responsible to confirm that the recipient is capable of\\n     * receiving ERC721\\n     * or else they may be permanently lost. Usage of {safeTransferFrom} prevents loss, though the\\n     * caller must\\n     * understand this adds an external call which potentially creates a reentrancy vulnerability.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must be owned by `from`.\\n     * - If the caller is not `from`, it must be approved to move this token by either {approve} or\\n     * {setApprovalForAll}.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 tokenId) external;\\n\\n    /**\\n     * @dev Gives permission to `to` to transfer `tokenId` token to another account.\\n     * The approval is cleared when the token is transferred.\\n     *\\n     * Only a single account can be approved at a time, so approving the zero address clears\\n     * previous approvals.\\n     *\\n     * Requirements:\\n     *\\n     * - The caller must own the token or be an approved operator.\\n     * - `tokenId` must exist.\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address to, uint256 tokenId) external;\\n\\n    /**\\n     * @dev Approve or remove `operator` as an operator for the caller.\\n     * Operators can call {transferFrom} or {safeTransferFrom} for any token owned by the caller.\\n     *\\n     * Requirements:\\n     *\\n     * - The `operator` cannot be the caller.\\n     *\\n     * Emits an {ApprovalForAll} event.\\n     */\\n    function setApprovalForAll(address operator, bool approved) external;\\n\\n    /**\\n     * @dev Returns the account approved for `tokenId` token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function getApproved(uint256 tokenId) external view returns (address operator);\\n\\n    /**\\n     * @dev Returns if the `operator` is allowed to manage all of the assets of `owner`.\\n     *\\n     * See {setApprovalForAll}\\n     */\\n    function isApprovedForAll(address owner, address operator) external view returns (bool);\\n}\\n\",\"keccak256\":\"0xd92befeaa23be95ee09181fb55606783d5b05f6d45835c748b552eff2b0c4a20\",\"license\":\"MIT\"},\"contracts/interfaces/IERC721Enumerable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport \\\"./IERC721.sol\\\";\\n\\n/**\\n * @title ERC-721 Non-Fungible Token Standard, optional enumeration extension\\n * @dev See https://eips.ethereum.org/EIPS/eip-721\\n */\\ninterface IERC721Enumerable is IERC721 {\\n    /**\\n     * @dev Returns the total amount of tokens stored by the contract.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns a token ID owned by `owner` at a given `index` of its token list.\\n     * Use along with {balanceOf} to enumerate all of ``owner``'s tokens.\\n     */\\n    function tokenOfOwnerByIndex(address owner, uint256 index) external view returns (uint256);\\n\\n    /**\\n     * @dev Returns a token ID at a given `index` of all the tokens stored by the contract.\\n     * Use along with {totalSupply} to enumerate all tokens.\\n     */\\n    function tokenByIndex(uint256 index) external view returns (uint256);\\n}\\n\",\"keccak256\":\"0xe6a554a24cc1516737591176979ef0c50797da97b68835ded2700abd0ec10d5f\",\"license\":\"MIT\"},\"contracts/interfaces/IERC721Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\nimport \\\"./IERC721.sol\\\";\\n\\n/**\\n * @title ERC-721 Non-Fungible Token Standard, optional metadata extension\\n * @dev See https://eips.ethereum.org/EIPS/eip-721\\n */\\ninterface IERC721Metadata is IERC721 {\\n    /**\\n     * @dev Returns the token collection name.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the token collection symbol.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the Uniform Resource Identifier (URI) for `tokenId` token.\\n     */\\n    function tokenURI(uint256 tokenId) external view returns (string memory);\\n}\\n\",\"keccak256\":\"0x7a06b2d14b8517db59bd504a9ae206137606b49b7a379b63266aa1c2653beb53\",\"license\":\"MIT\"},\"contracts/interfaces/IERC721Permit.sol\":{\"content\":\"// SPDX-License-Identifier: GPL-2.0-or-later\\npragma solidity ^0.8.23;\\n\\nimport \\\"./IERC721.sol\\\";\\n\\n/// @title ERC721 with permit\\n/// @notice Extension to ERC721 that includes a permit function for signature based approvals\\ninterface IERC721Permit is IERC721 {\\n    /// @notice The permit typehash used in the permit signature\\n    /// @return The typehash for the permit\\n    function PERMIT_TYPEHASH() external pure returns (bytes32);\\n\\n    /// @notice The domain separator used in the permit signature\\n    /// @return The domain seperator used in encoding of permit signature\\n    function DOMAIN_SEPARATOR() external view returns (bytes32);\\n\\n    /// @notice Approve of a specific token ID for spending by spender via signature\\n    /// @param spender The account that is being approved\\n    /// @param tokenId The ID of the token that is being approved for spending\\n    /// @param deadline The deadline timestamp by which the call must be mined for the approve to\\n    /// work\\n    /// @param v Must produce valid secp256k1 signature from the holder along with `r` and `s`\\n    /// @param r Must produce valid secp256k1 signature from the holder along with `v` and `s`\\n    /// @param s Must produce valid secp256k1 signature from the holder along with `r` and `v`\\n    function permit(\\n        address spender,\\n        uint256 tokenId,\\n        uint256 deadline,\\n        uint8 v,\\n        bytes32 r,\\n        bytes32 s\\n    )\\n        external\\n        payable;\\n}\\n\",\"keccak256\":\"0x3536daf1dc1878a7d4f2457f69838c59381872407dabc12bdd3008e6a48892de\",\"license\":\"GPL-2.0-or-later\"},\"contracts/interfaces/INonfungiblePositionManager.sol\":{\"content\":\"// SPDX-License-Identifier: GPL-2.0-or-later\\npragma solidity ^0.8.23;\\npragma abicoder v2;\\n\\nimport { IERC721Metadata } from \\\"./IERC721Metadata.sol\\\";\\nimport { IERC721Enumerable } from \\\"./IERC721Enumerable.sol\\\";\\n\\nimport { IERC721Permit } from \\\"./IERC721Permit.sol\\\";\\nimport { IPoolInitializer } from \\\"./IPoolInitializer.sol\\\";\\nimport { IPeripheryPayments } from \\\"./IPeripheryPayments.sol\\\";\\nimport { IPeripheryImmutableState } from \\\"./IPeripheryImmutableState.sol\\\";\\n\\n/// @title Non-fungible token for positions\\n/// @notice Wraps Uniswap V3 positions in a non-fungible token interface which allows for them to be\\n/// transferred\\n/// and authorized.\\ninterface INonfungiblePositionManager is\\n    IPoolInitializer,\\n    IPeripheryPayments,\\n    IPeripheryImmutableState,\\n    IERC721Metadata,\\n    IERC721Enumerable,\\n    IERC721Permit\\n{\\n    /// @notice Emitted when liquidity is increased for a position NFT\\n    /// @dev Also emitted when a token is minted\\n    /// @param tokenId The ID of the token for which liquidity was increased\\n    /// @param liquidity The amount by which liquidity for the NFT position was increased\\n    /// @param amount0 The amount of token0 that was paid for the increase in liquidity\\n    /// @param amount1 The amount of token1 that was paid for the increase in liquidity\\n    event IncreaseLiquidity(\\n        uint256 indexed tokenId, uint128 liquidity, uint256 amount0, uint256 amount1\\n    );\\n    /// @notice Emitted when liquidity is decreased for a position NFT\\n    /// @param tokenId The ID of the token for which liquidity was decreased\\n    /// @param liquidity The amount by which liquidity for the NFT position was decreased\\n    /// @param amount0 The amount of token0 that was accounted for the decrease in liquidity\\n    /// @param amount1 The amount of token1 that was accounted for the decrease in liquidity\\n    event DecreaseLiquidity(\\n        uint256 indexed tokenId, uint128 liquidity, uint256 amount0, uint256 amount1\\n    );\\n    /// @notice Emitted when tokens are collected for a position NFT\\n    /// @dev The amounts reported may not be exactly equivalent to the amounts transferred, due to\\n    /// rounding behavior\\n    /// @param tokenId The ID of the token for which underlying tokens were collected\\n    /// @param recipient The address of the account that received the collected tokens\\n    /// @param amount0 The amount of token0 owed to the position that was collected\\n    /// @param amount1 The amount of token1 owed to the position that was collected\\n    event Collect(uint256 indexed tokenId, address recipient, uint256 amount0, uint256 amount1);\\n\\n    /// @notice Returns the position information associated with a given token ID.\\n    /// @dev Throws if the token ID is not valid.\\n    /// @param tokenId The ID of the token that represents the position\\n    /// @return nonce The nonce for permits\\n    /// @return operator The address that is approved for spending\\n    /// @return token0 The address of the token0 for a specific pool\\n    /// @return token1 The address of the token1 for a specific pool\\n    /// @return fee The fee associated with the pool\\n    /// @return tickLower The lower end of the tick range for the position\\n    /// @return tickUpper The higher end of the tick range for the position\\n    /// @return liquidity The liquidity of the position\\n    /// @return feeGrowthInside0LastX128 The fee growth of token0 as of the last action on the\\n    /// individual position\\n    /// @return feeGrowthInside1LastX128 The fee growth of token1 as of the last action on the\\n    /// individual position\\n    /// @return tokensOwed0 The uncollected amount of token0 owed to the position as of the last\\n    /// computation\\n    /// @return tokensOwed1 The uncollected amount of token1 owed to the position as of the last\\n    /// computation\\n    function positions(uint256 tokenId)\\n        external\\n        view\\n        returns (\\n            uint96 nonce,\\n            address operator,\\n            address token0,\\n            address token1,\\n            uint24 fee,\\n            int24 tickLower,\\n            int24 tickUpper,\\n            uint128 liquidity,\\n            uint256 feeGrowthInside0LastX128,\\n            uint256 feeGrowthInside1LastX128,\\n            uint128 tokensOwed0,\\n            uint128 tokensOwed1\\n        );\\n\\n    struct MintParams {\\n        address token0;\\n        address token1;\\n        uint24 fee;\\n        int24 tickLower;\\n        int24 tickUpper;\\n        uint256 amount0Desired;\\n        uint256 amount1Desired;\\n        uint256 amount0Min;\\n        uint256 amount1Min;\\n        address recipient;\\n        uint256 deadline;\\n    }\\n\\n    /// @notice Creates a new position wrapped in a NFT\\n    /// @dev Call this when the pool does exist and is initialized. Note that if the pool is created\\n    /// but not initialized\\n    /// a method does not exist, i.e. the pool is assumed to be initialized.\\n    /// @param params The params necessary to mint a position, encoded as `MintParams` in calldata\\n    /// @return tokenId The ID of the token that represents the minted position\\n    /// @return liquidity The amount of liquidity for this position\\n    /// @return amount0 The amount of token0\\n    /// @return amount1 The amount of token1\\n    function mint(MintParams calldata params)\\n        external\\n        payable\\n        returns (uint256 tokenId, uint128 liquidity, uint256 amount0, uint256 amount1);\\n\\n    struct IncreaseLiquidityParams {\\n        uint256 tokenId;\\n        uint256 amount0Desired;\\n        uint256 amount1Desired;\\n        uint256 amount0Min;\\n        uint256 amount1Min;\\n        uint256 deadline;\\n    }\\n\\n    /// @notice Increases the amount of liquidity in a position, with tokens paid by the\\n    /// `msg.sender`\\n    /// @param params tokenId The ID of the token for which liquidity is being increased,\\n    /// amount0Desired The desired amount of token0 to be spent,\\n    /// amount1Desired The desired amount of token1 to be spent,\\n    /// amount0Min The minimum amount of token0 to spend, which serves as a slippage check,\\n    /// amount1Min The minimum amount of token1 to spend, which serves as a slippage check,\\n    /// deadline The time by which the transaction must be included to effect the change\\n    /// @return liquidity The new liquidity amount as a result of the increase\\n    /// @return amount0 The amount of token0 to acheive resulting liquidity\\n    /// @return amount1 The amount of token1 to acheive resulting liquidity\\n    function increaseLiquidity(IncreaseLiquidityParams calldata params)\\n        external\\n        payable\\n        returns (uint128 liquidity, uint256 amount0, uint256 amount1);\\n\\n    struct DecreaseLiquidityParams {\\n        uint256 tokenId;\\n        uint128 liquidity;\\n        uint256 amount0Min;\\n        uint256 amount1Min;\\n        uint256 deadline;\\n    }\\n\\n    /// @notice Decreases the amount of liquidity in a position and accounts it to the position\\n    /// @param params tokenId The ID of the token for which liquidity is being decreased,\\n    /// amount The amount by which liquidity will be decreased,\\n    /// amount0Min The minimum amount of token0 that should be accounted for the burned liquidity,\\n    /// amount1Min The minimum amount of token1 that should be accounted for the burned liquidity,\\n    /// deadline The time by which the transaction must be included to effect the change\\n    /// @return amount0 The amount of token0 accounted to the position's tokens owed\\n    /// @return amount1 The amount of token1 accounted to the position's tokens owed\\n    function decreaseLiquidity(DecreaseLiquidityParams calldata params)\\n        external\\n        payable\\n        returns (uint256 amount0, uint256 amount1);\\n\\n    struct CollectParams {\\n        uint256 tokenId;\\n        address recipient;\\n        uint128 amount0Max;\\n        uint128 amount1Max;\\n    }\\n\\n    /// @notice Collects up to a maximum amount of fees owed to a specific position to the recipient\\n    /// @param params tokenId The ID of the NFT for which tokens are being collected,\\n    /// recipient The account that should receive the tokens,\\n    /// amount0Max The maximum amount of token0 to collect,\\n    /// amount1Max The maximum amount of token1 to collect\\n    /// @return amount0 The amount of fees collected in token0\\n    /// @return amount1 The amount of fees collected in token1\\n    function collect(CollectParams calldata params)\\n        external\\n        payable\\n        returns (uint256 amount0, uint256 amount1);\\n\\n    /// @notice Burns a token ID, which deletes it from the NFT contract. The token must have 0\\n    /// liquidity and all tokens\\n    /// must be collected first.\\n    /// @param tokenId The ID of the token that is being burned\\n    function burn(uint256 tokenId) external payable;\\n}\\n\",\"keccak256\":\"0x5adfea02faeec1437376b6e9d323443ef40a79ec80d611bb49147b2ec4aada17\",\"license\":\"GPL-2.0-or-later\"},\"contracts/interfaces/IPeripheryImmutableState.sol\":{\"content\":\"// SPDX-License-Identifier: GPL-2.0-or-later\\npragma solidity ^0.8.23;\\n\\n/// @title Immutable state\\n/// @notice Functions that return immutable state of the router\\ninterface IPeripheryImmutableState {\\n    /// @return Returns the address of the Uniswap V3 factory\\n    function factory() external view returns (address);\\n\\n    /// @return Returns the address of WETH9\\n    function WETH9() external view returns (address);\\n}\\n\",\"keccak256\":\"0xc5a7a49f97cf2ea37edfe49004a9f5109a7b97e4c3409cd23e76c78d7ea4d83f\",\"license\":\"GPL-2.0-or-later\"},\"contracts/interfaces/IPeripheryPayments.sol\":{\"content\":\"// SPDX-License-Identifier: GPL-2.0-or-later\\npragma solidity ^0.8.23;\\n\\n/// @title Periphery Payments\\n/// @notice Functions to ease deposits and withdrawals of ETH\\ninterface IPeripheryPayments {\\n    /// @notice Unwraps the contract's WETH9 balance and sends it to recipient as ETH.\\n    /// @dev The amountMinimum parameter prevents malicious contracts from stealing WETH9 from\\n    /// users.\\n    /// @param amountMinimum The minimum amount of WETH9 to unwrap\\n    /// @param recipient The address receiving ETH\\n    function unwrapWETH9(uint256 amountMinimum, address recipient) external payable;\\n\\n    /// @notice Refunds any ETH balance held by this contract to the `msg.sender`\\n    /// @dev Useful for bundling with mint or increase liquidity that uses ether, or exact output\\n    /// swaps\\n    /// that use ether for the input amount\\n    function refundETH() external payable;\\n\\n    /// @notice Transfers the full amount of a token held by this contract to recipient\\n    /// @dev The amountMinimum parameter prevents malicious contracts from stealing the token from\\n    /// users\\n    /// @param token The contract address of the token which will be transferred to `recipient`\\n    /// @param amountMinimum The minimum amount of token required for a transfer\\n    /// @param recipient The destination address of the token\\n    function sweepToken(address token, uint256 amountMinimum, address recipient) external payable;\\n}\\n\",\"keccak256\":\"0xc9a2b02e4b56e90146a9ccbd668ef1019dc63686b572cf227a0ac38b2a60c76d\",\"license\":\"GPL-2.0-or-later\"},\"contracts/interfaces/IPoolInitializer.sol\":{\"content\":\"// SPDX-License-Identifier: GPL-2.0-or-later\\npragma solidity ^0.8.23;\\npragma abicoder v2;\\n\\n/// @title Creates and initializes V3 Pools\\n/// @notice Provides a method for creating and initializing a pool, if necessary, for bundling with\\n/// other methods that\\n/// require the pool to exist.\\ninterface IPoolInitializer {\\n    /// @notice Creates a new pool if it does not exist, then initializes if not initialized\\n    /// @dev This method can be bundled with others via IMulticall for the first action (e.g. mint)\\n    /// performed against a pool\\n    /// @param token0 The contract address of token0 of the pool\\n    /// @param token1 The contract address of token1 of the pool\\n    /// @param fee The fee amount of the v3 pool for the specified token pair\\n    /// @param sqrtPriceX96 The initial square root price of the pool as a Q64.96 value\\n    /// @return pool Returns the pool address based on the pair of tokens and fee, will return the\\n    /// newly created pool address if necessary\\n    function createAndInitializePoolIfNecessary(\\n        address token0,\\n        address token1,\\n        uint24 fee,\\n        uint160 sqrtPriceX96\\n    )\\n        external\\n        payable\\n        returns (address pool);\\n}\\n\",\"keccak256\":\"0xadc9782216caffd826450c43c19630506fd660247d3eeb79beeeca5921b93541\",\"license\":\"GPL-2.0-or-later\"},\"contracts/interfaces/IWETH9.sol\":{\"content\":\"// SPDX-License-Identifier: GPL-2.0-or-later\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\n\\n/// @title Interface for WETH9\\ninterface IWETH9 is IERC20 {\\n    /// @notice Deposit ether to get wrapped ether\\n    function deposit() external payable;\\n\\n    /// @notice Withdraw wrapped ether to get ether\\n    function withdraw(uint256) external;\\n}\\n\",\"keccak256\":\"0x94d62a831a22c2f6236250edb6b126b063f3433e53b528ce5b34456bb28882d7\",\"license\":\"GPL-2.0-or-later\"},\"contracts/libraries/BondingCurveMath.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\n/**\\n * @title BondingCurveMath\\n * @notice Mathematical functions for bonding curve calculations\\n * @dev Implements constant product formula: x * y = k\\n */\\nlibrary BondingCurveMath {\\n    error InsufficientReserves();\\n    error InvalidInput();\\n    error MathOverflow();\\n\\n    /**\\n     * @notice Calculate the cost in base currency to get exact amount of tokens\\n     * @param baseReserves Current base currency reserves\\n     * @param tokenReserves Current token reserves\\n     * @param tokenAmountOut Exact amount of tokens desired\\n     * @return baseCost Amount of base currency needed\\n     */\\n    function calculateBaseCostForExactTokens(\\n        uint256 baseReserves,\\n        uint256 tokenReserves,\\n        uint256 tokenAmountOut\\n    )\\n        internal\\n        pure\\n        returns (uint256 baseCost)\\n    {\\n        if (tokenAmountOut == 0) return 0;\\n        if (tokenAmountOut >= tokenReserves) revert InsufficientReserves();\\n\\n        uint256 remainingTokenReserves = tokenReserves - tokenAmountOut;\\n        uint256 newBaseReserves = (baseReserves * tokenReserves) / remainingTokenReserves;\\n\\n        if (newBaseReserves <= baseReserves) revert InvalidInput();\\n\\n        baseCost = newBaseReserves - baseReserves;\\n    }\\n\\n    /**\\n     * @notice Calculate tokens received for exact base currency input\\n     * @param baseReserves Current base currency reserves\\n     * @param tokenReserves Current token reserves\\n     * @param baseAmountIn Exact amount of base currency to spend\\n     * @return tokenAmountOut Amount of tokens that will be received\\n     */\\n    function calculateTokensForExactBase(\\n        uint256 baseReserves,\\n        uint256 tokenReserves,\\n        uint256 baseAmountIn\\n    )\\n        internal\\n        pure\\n        returns (uint256 tokenAmountOut)\\n    {\\n        if (baseAmountIn == 0) return 0;\\n\\n        uint256 newBaseReserves = baseReserves + baseAmountIn;\\n        uint256 newTokenReserves = (baseReserves * tokenReserves) / newBaseReserves;\\n\\n        if (newTokenReserves >= tokenReserves) revert InvalidInput();\\n\\n        tokenAmountOut = tokenReserves - newTokenReserves;\\n    }\\n\\n    /**\\n     * @notice Calculate base currency received for exact token input (selling)\\n     * @param baseReserves Current base currency reserves\\n     * @param tokenReserves Current token reserves\\n     * @param tokenAmountIn Exact amount of tokens to sell\\n     * @return baseAmountOut Amount of base currency that will be received\\n     */\\n    function calculateBaseForExactTokens(\\n        uint256 baseReserves,\\n        uint256 tokenReserves,\\n        uint256 tokenAmountIn\\n    )\\n        internal\\n        pure\\n        returns (uint256 baseAmountOut)\\n    {\\n        if (tokenAmountIn == 0) return 0;\\n\\n        uint256 newTokenReserves = tokenReserves + tokenAmountIn;\\n        uint256 newBaseReserves = (baseReserves * tokenReserves) / newTokenReserves;\\n\\n        if (newBaseReserves >= baseReserves) revert InvalidInput();\\n\\n        baseAmountOut = baseReserves - newBaseReserves;\\n    }\\n}\\n\",\"keccak256\":\"0x7556799910f73383215712e6d45bfbbabbf162b4de0e2959788e69aa12bb8e9b\",\"license\":\"MIT\"},\"contracts/libraries/uniswap/FullMath.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.23;\\n\\n/// @title Contains 512-bit math functions\\n/// @notice Facilitates multiplication and division that can have overflow of an intermediate value\\n/// without any loss of precision\\n/// @dev Handles \\\"phantom overflow\\\" i.e., allows multiplication and division where an intermediate\\n/// value overflows 256 bits\\nlibrary FullMath {\\n    /// @notice Calculates floor(a\\u00d7b\\u00f7denominator) with full precision. Throws if result overflows\\n    /// a uint256 or denominator == 0\\n    /// @param a The multiplicand\\n    /// @param b The multiplier\\n    /// @param denominator The divisor\\n    /// @return result The 256-bit result\\n    /// @dev Credit to Remco Bloemen under MIT license https://xn--2-umb.com/21/muldiv\\n    function mulDiv(\\n        uint256 a,\\n        uint256 b,\\n        uint256 denominator\\n    )\\n        internal\\n        pure\\n        returns (uint256 result)\\n    {\\n        // 512-bit multiply [prod1 prod0] = a * b\\n        // Compute the product mod 2**256 and mod 2**256 - 1\\n        // then use the Chinese Remainder Theorem to reconstruct\\n        // the 512 bit result. The result is stored in two 256\\n        // variables such that product = prod1 * 2**256 + prod0\\n        uint256 prod0; // Least significant 256 bits of the product\\n        uint256 prod1; // Most significant 256 bits of the product\\n        assembly {\\n            let mm := mulmod(a, b, not(0))\\n            prod0 := mul(a, b)\\n            prod1 := sub(sub(mm, prod0), lt(mm, prod0))\\n        }\\n\\n        // Handle non-overflow cases, 256 by 256 division\\n        if (prod1 == 0) {\\n            require(denominator > 0);\\n            assembly {\\n                result := div(prod0, denominator)\\n            }\\n            return result;\\n        }\\n\\n        // Make sure the result is less than 2**256.\\n        // Also prevents denominator == 0\\n        require(denominator > prod1);\\n\\n        ///////////////////////////////////////////////\\n        // 512 by 256 division.\\n        ///////////////////////////////////////////////\\n\\n        // Make division exact by subtracting the remainder from [prod1 prod0]\\n        // Compute remainder using mulmod\\n        uint256 remainder;\\n        assembly {\\n            remainder := mulmod(a, b, denominator)\\n        }\\n        // Subtract 256 bit number from 512 bit number\\n        assembly {\\n            prod1 := sub(prod1, gt(remainder, prod0))\\n            prod0 := sub(prod0, remainder)\\n        }\\n\\n        // Factor powers of two out of denominator\\n        // Compute largest power of two divisor of denominator.\\n        // Always >= 1.\\n        uint256 twos = denominator & (~denominator + 1);\\n        // Divide denominator by power of two\\n        assembly {\\n            denominator := div(denominator, twos)\\n        }\\n\\n        // Divide [prod1 prod0] by the factors of two\\n        assembly {\\n            prod0 := div(prod0, twos)\\n        }\\n        // Shift in bits from prod1 into prod0. For this we need\\n        // to flip `twos` such that it is 2**256 / twos.\\n        // If twos is zero, then it becomes one\\n        assembly {\\n            twos := add(div(sub(0, twos), twos), 1)\\n        }\\n        prod0 |= prod1 * twos;\\n\\n        // Invert denominator mod 2**256\\n        // Now that denominator is an odd number, it has an inverse\\n        // modulo 2**256 such that denominator * inv = 1 mod 2**256.\\n        // Compute the inverse by starting with a seed that is correct\\n        // correct for four bits. That is, denominator * inv = 1 mod 2**4\\n        uint256 inv = (3 * denominator) ^ 2;\\n        // Now use Newton-Raphson iteration to improve the precision.\\n        // Thanks to Hensel's lifting lemma, this also works in modular\\n        // arithmetic, doubling the correct bits in each step.\\n        inv *= 2 - denominator * inv; // inverse mod 2**8\\n        inv *= 2 - denominator * inv; // inverse mod 2**16\\n        inv *= 2 - denominator * inv; // inverse mod 2**32\\n        inv *= 2 - denominator * inv; // inverse mod 2**64\\n        inv *= 2 - denominator * inv; // inverse mod 2**128\\n        inv *= 2 - denominator * inv; // inverse mod 2**256\\n\\n        // Because the division is now exact we can divide by multiplying\\n        // with the modular inverse of denominator. This will give us the\\n        // correct result modulo 2**256. Since the precoditions guarantee\\n        // that the outcome is less than 2**256, this is the final result.\\n        // We don't need to compute the high bits of the result and prod1\\n        // is no longer required.\\n        result = prod0 * inv;\\n        return result;\\n    }\\n\\n    /// @notice Calculates ceil(a\\u00d7b\\u00f7denominator) with full precision. Throws if result overflows a\\n    /// uint256 or denominator == 0\\n    /// @param a The multiplicand\\n    /// @param b The multiplier\\n    /// @param denominator The divisor\\n    /// @return result The 256-bit result\\n    function mulDivRoundingUp(\\n        uint256 a,\\n        uint256 b,\\n        uint256 denominator\\n    )\\n        internal\\n        pure\\n        returns (uint256 result)\\n    {\\n        result = mulDiv(a, b, denominator);\\n        if (mulmod(a, b, denominator) > 0) {\\n            require(result < type(uint256).max);\\n            result++;\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xe9bdf058700cff8dcdddbf1c2a54bba7c5dfedc1415fb849ca38ce3bedd69f9f\",\"license\":\"MIT\"},\"contracts/libraries/uniswap/TickMath.sol\":{\"content\":\"// SPDX-License-Identifier: GPL-2.0-or-later\\npragma solidity ^0.8.23;\\n\\n/// @title Math library for computing sqrt prices from ticks and vice versa\\n/// @notice Computes sqrt price for ticks of size 1.0001, i.e. sqrt(1.0001^tick) as fixed point\\n/// Q64.96 numbers. Supports\\n/// prices between 2**-128 and 2**128\\nlibrary TickMath {\\n    /// @dev The minimum tick that may be passed to #getSqrtRatioAtTick computed from log base\\n    /// 1.0001 of 2**-128\\n    int24 internal constant MIN_TICK = -887_272;\\n    /// @dev The maximum tick that may be passed to #getSqrtRatioAtTick computed from log base\\n    /// 1.0001 of 2**128\\n    int24 internal constant MAX_TICK = -MIN_TICK;\\n\\n    /// @dev The minimum value that can be returned from #getSqrtRatioAtTick. Equivalent to\\n    /// getSqrtRatioAtTick(MIN_TICK)\\n    uint160 internal constant MIN_SQRT_RATIO = 4_295_128_739;\\n    /// @dev The maximum value that can be returned from #getSqrtRatioAtTick. Equivalent to\\n    /// getSqrtRatioAtTick(MAX_TICK)\\n    uint160 internal constant MAX_SQRT_RATIO =\\n        1_461_446_703_485_210_103_287_273_052_203_988_822_378_723_970_342;\\n\\n    function getSqrtRatioAtTicks(\\n        int24 tickLower,\\n        int24 tickUpper\\n    )\\n        public\\n        pure\\n        returns (uint160 sqrtPriceAX96, uint160 sqrtPriceBX96)\\n    {\\n        sqrtPriceAX96 = getSqrtRatioAtTick(tickLower);\\n        sqrtPriceBX96 = getSqrtRatioAtTick(tickUpper);\\n    }\\n\\n    /// @notice Calculates sqrt(1.0001^tick) * 2^96\\n    /// @dev Throws if |tick| > max tick\\n    /// @param tick The input tick for the above formula\\n    /// @return sqrtPriceX96 A Fixed point Q64.96 number representing the sqrt of the ratio of the\\n    /// two assets (token1/token0)\\n    /// at the given tick\\n    function getSqrtRatioAtTick(int24 tick) public pure returns (uint160 sqrtPriceX96) {\\n        uint256 absTick = tick < 0 ? uint256(-int256(tick)) : uint256(int256(tick));\\n        require(absTick <= uint256(int256(MAX_TICK)), \\\"T\\\");\\n\\n        uint256 ratio = absTick & 0x1 != 0\\n            ? 0xfffcb933bd6fad37aa2d162d1a594001\\n            : 0x100000000000000000000000000000000;\\n        if (absTick & 0x2 != 0) ratio = (ratio * 0xfff97272373d413259a46990580e213a) >> 128;\\n        if (absTick & 0x4 != 0) ratio = (ratio * 0xfff2e50f5f656932ef12357cf3c7fdcc) >> 128;\\n        if (absTick & 0x8 != 0) ratio = (ratio * 0xffe5caca7e10e4e61c3624eaa0941cd0) >> 128;\\n        if (absTick & 0x10 != 0) ratio = (ratio * 0xffcb9843d60f6159c9db58835c926644) >> 128;\\n        if (absTick & 0x20 != 0) ratio = (ratio * 0xff973b41fa98c081472e6896dfb254c0) >> 128;\\n        if (absTick & 0x40 != 0) ratio = (ratio * 0xff2ea16466c96a3843ec78b326b52861) >> 128;\\n        if (absTick & 0x80 != 0) ratio = (ratio * 0xfe5dee046a99a2a811c461f1969c3053) >> 128;\\n        if (absTick & 0x100 != 0) ratio = (ratio * 0xfcbe86c7900a88aedcffc83b479aa3a4) >> 128;\\n        if (absTick & 0x200 != 0) ratio = (ratio * 0xf987a7253ac413176f2b074cf7815e54) >> 128;\\n        if (absTick & 0x400 != 0) ratio = (ratio * 0xf3392b0822b70005940c7a398e4b70f3) >> 128;\\n        if (absTick & 0x800 != 0) ratio = (ratio * 0xe7159475a2c29b7443b29c7fa6e889d9) >> 128;\\n        if (absTick & 0x1000 != 0) ratio = (ratio * 0xd097f3bdfd2022b8845ad8f792aa5825) >> 128;\\n        if (absTick & 0x2000 != 0) ratio = (ratio * 0xa9f746462d870fdf8a65dc1f90e061e5) >> 128;\\n        if (absTick & 0x4000 != 0) ratio = (ratio * 0x70d869a156d2a1b890bb3df62baf32f7) >> 128;\\n        if (absTick & 0x8000 != 0) ratio = (ratio * 0x31be135f97d08fd981231505542fcfa6) >> 128;\\n        if (absTick & 0x10000 != 0) ratio = (ratio * 0x9aa508b5b7a84e1c677de54f3e99bc9) >> 128;\\n        if (absTick & 0x20000 != 0) ratio = (ratio * 0x5d6af8dedb81196699c329225ee604) >> 128;\\n        if (absTick & 0x40000 != 0) ratio = (ratio * 0x2216e584f5fa1ea926041bedfe98) >> 128;\\n        if (absTick & 0x80000 != 0) ratio = (ratio * 0x48a170391f7dc42444e8fa2) >> 128;\\n\\n        if (tick > 0) ratio = type(uint256).max / ratio;\\n\\n        // this divides by 1<<32 rounding up to go from a Q128.128 to a Q128.96.\\n        // we then downcast because we know the result always fits within 160 bits due to our tick\\n        // input constraint\\n        // we round up in the division so getTickAtSqrtRatio of the output price is always\\n        // consistent\\n        sqrtPriceX96 = uint160((ratio >> 32) + (ratio % (1 << 32) == 0 ? 0 : 1));\\n    }\\n\\n    /// @notice Calculates the greatest tick value such that getRatioAtTick(tick) <= ratio\\n    /// @dev Throws in case sqrtPriceX96 < MIN_SQRT_RATIO, as MIN_SQRT_RATIO is the lowest value\\n    /// getRatioAtTick may\\n    /// ever return.\\n    /// @param sqrtPriceX96 The sqrt ratio for which to compute the tick as a Q64.96\\n    /// @return tick The greatest tick for which the ratio is less than or equal to the input ratio\\n    function getTickAtSqrtRatio(uint160 sqrtPriceX96) public pure returns (int24 tick) {\\n        // second inequality must be < because the price can never reach the price at the max tick\\n        require(sqrtPriceX96 >= MIN_SQRT_RATIO && sqrtPriceX96 < MAX_SQRT_RATIO, \\\"R\\\");\\n        uint256 ratio = uint256(sqrtPriceX96) << 32;\\n\\n        uint256 r = ratio;\\n        uint256 msb = 0;\\n\\n        assembly {\\n            let f := shl(7, gt(r, 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF))\\n            msb := or(msb, f)\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            let f := shl(6, gt(r, 0xFFFFFFFFFFFFFFFF))\\n            msb := or(msb, f)\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            let f := shl(5, gt(r, 0xFFFFFFFF))\\n            msb := or(msb, f)\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            let f := shl(4, gt(r, 0xFFFF))\\n            msb := or(msb, f)\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            let f := shl(3, gt(r, 0xFF))\\n            msb := or(msb, f)\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            let f := shl(2, gt(r, 0xF))\\n            msb := or(msb, f)\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            let f := shl(1, gt(r, 0x3))\\n            msb := or(msb, f)\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            let f := gt(r, 0x1)\\n            msb := or(msb, f)\\n        }\\n\\n        if (msb >= 128) r = ratio >> (msb - 127);\\n        else r = ratio << (127 - msb);\\n\\n        int256 log_2 = (int256(msb) - 128) << 64;\\n\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(63, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(62, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(61, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(60, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(59, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(58, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(57, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(56, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(55, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(54, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(53, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(52, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(51, f))\\n            r := shr(f, r)\\n        }\\n        assembly {\\n            r := shr(127, mul(r, r))\\n            let f := shr(128, r)\\n            log_2 := or(log_2, shl(50, f))\\n        }\\n\\n        int256 log_sqrt10001 = log_2 * 255_738_958_999_603_826_347_141; // 128.128 number\\n\\n        int24 tickLow =\\n            int24((log_sqrt10001 - 3_402_992_956_809_132_418_596_140_100_660_247_210) >> 128);\\n        int24 tickHi =\\n            int24((log_sqrt10001 + 291_339_464_771_989_622_907_027_621_153_398_088_495) >> 128);\\n\\n        tick = tickLow == tickHi\\n            ? tickLow\\n            : getSqrtRatioAtTick(tickHi) <= sqrtPriceX96 ? tickHi : tickLow;\\n    }\\n}\\n\",\"keccak256\":\"0x024612301ebb661d230f0e5b6e7e8fd64d661eafed938a7121c97c6003427f38\",\"license\":\"GPL-2.0-or-later\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"errors": {"InvalidInitialization()": [{"details": "The contract is already initialized."}], "NotInitializing()": [{"details": "The contract is not initializing."}], "OwnableInvalidOwner(address)": [{"details": "The owner is not a valid owner account. (eg. `address(0)`)"}], "OwnableUnauthorizedAccount(address)": [{"details": "The caller account is not authorized to perform an operation."}], "ReentrancyGuardReentrantCall()": [{"details": "Unauthorized reentrant call."}], "SafeERC20FailedOperation(address)": [{"details": "An operation with an ERC-20 token failed."}]}, "events": {"Initialized(uint64)": {"details": "Triggered when the contract has been initialized or reinitialized."}}, "kind": "dev", "methods": {"buyExactIn(address,uint256,uint256)": {"params": {"amountIn": "Exact amount of ETH to spend", "amountOutMin": "Minimum amount of tokens to receive", "token": "Token address"}}, "collectHyperSwapFees(address)": {"params": {"token": "Token address associated with the position"}, "returns": {"amount0": "The amount of fees collected in token0", "amount1": "The amount of fees collected in token1"}}, "createAndLockFirstBuy(string,string,string,string,string,string,string,uint256,uint256,uint256)": {"params": {"amountOut": "Amount of tokens to buy and lock", "customThreshold": "Custom threshold (0 for default)", "description": "Token description (max 1000 characters)", "lockDuration": "Duration to lock the purchased tokens (minimum 1 hour)", "name": "Token name", "symbol": "Token symbol", "telegram": "Telegram link (max 500 characters)", "twitter": "Twitter handle (max 500 characters)", "uri": "Token metadata URI (max 300 characters)", "website": "Website URL (max 500 characters)"}}, "createPool(string,string,string,string,string,string,string,uint256)": {"params": {"customThreshold": "Custom threshold (0 for default)", "description": "Token description (max 1000 characters)", "name": "Token name", "symbol": "Token symbol", "telegram": "Telegram link (max 500 characters)", "twitter": "Twitter handle (max 500 characters)", "uri": "Token metadata URI (max 300 characters)", "website": "Website URL (max 500 characters)"}}, "distributeBondingCurveFees(address)": {"params": {"token": "Token address"}}, "initialize(address,address,uint24,address,address,address)": {"params": {"_moonbagsStake": "MoonbagsStake contract address", "_nonfungiblePositionManager": "Position Manager address", "_platformTokenAddress": "Platform token address", "_poolFee": "Pool fee (e.g., 3000 for 0.3%)", "_weth9": "Weth9 address"}}, "isToken(address)": {"params": {"token": "The token address to check"}, "returns": {"exists": "True if the token exists"}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "setMoonbagsStake(address)": {"params": {"_moonbagsStake": "Address of the MoonbagsStake contract"}}, "setTokenLock(address)": {"params": {"_tokenLock": "Address of the new TokenLock contract"}}, "skim(address)": {"params": {"token": "Token address to skim"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "updateActiveFeeTier(uint24)": {"params": {"newFeeTier": "The new fee tier (500, 3000, or 10000)"}}, "updateInitialVirtualTokenReserves(uint256)": {"params": {"_initialVirtualTokenReserves": "New initial virtual token reserves"}}}, "title": "Moonbags Launchpad", "version": 1}, "userdoc": {"kind": "user", "methods": {"buyExactIn(address,uint256,uint256)": {"notice": "Buy exact amount in and lock tokens for specified duration"}, "collectHyperSwapFees(address)": {"notice": "Collect all fees from a HyperSwap V3 position"}, "createAndLockFirstBuy(string,string,string,string,string,string,string,uint256,uint256,uint256)": {"notice": "Create a new pool with bonding curve and lock the first buy"}, "createPool(string,string,string,string,string,string,string,uint256)": {"notice": "Create a new pool with bonding curve"}, "distributeBondingCurveFees(address)": {"notice": "Distribute accumulated bonding curve fees for a token"}, "initialize(address,address,uint24,address,address,address)": {"notice": "Initialize the contract"}, "isToken(address)": {"notice": "Check if a token exists (has been created through the launchpad)"}, "onERC721Received(address,address,uint256,bytes)": {"notice": "Handle NFT transfers (required for IERC721Receiver)"}, "setMoonbagsStake(address)": {"notice": "Set the MoonbagsStake contract address (only owner)"}, "setTokenLock(address)": {"notice": "Set TokenLock contract (only owner)"}, "skim(address)": {"notice": "skim"}, "updateActiveFeeTier(uint24)": {"notice": "Update the active fee tier for new pools (only owner)"}, "updateConfigWithdrawFee(uint16,uint16,uint16,uint16)": {"notice": "Update the initial withdraw fees (only owner)"}, "updateConfiguration(uint256,uint256,uint256,uint8,uint16,uint16,uint16,uint16,address)": {"notice": "Update configuration (only owner)"}, "updateFeeRecipients(address,address)": {"notice": "Update fee recipients (only owner)"}, "updateInitialVirtualTokenReserves(uint256)": {"notice": "Update the initial virtual token reserves (only owner)"}}, "version": 1}, "storageLayout": {"storage": [{"astId": 5972, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "config", "offset": 0, "slot": "0", "type": "t_struct(Configuration)5935_storage"}, {"astId": 5975, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "thresholdConfig", "offset": 0, "slot": "7", "type": "t_struct(ThresholdConfig)5969_storage"}, {"astId": 5978, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "tokenLock", "offset": 0, "slot": "8", "type": "t_contract(TokenLock)11100"}, {"astId": 5981, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "moonbagsStake", "offset": 0, "slot": "9", "type": "t_contract(MoonbagsStake)10580"}, {"astId": 5986, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "pools", "offset": 0, "slot": "10", "type": "t_mapping(t_address,t_struct(Pool)5966_storage)"}, {"astId": 5990, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "tokenToPositionId", "offset": 0, "slot": "11", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 5993, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "nonfungiblePositionManager", "offset": 0, "slot": "12", "type": "t_contract(INonfungiblePositionManager)11612"}, {"astId": 5995, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "weth9", "offset": 0, "slot": "13", "type": "t_address"}, {"astId": 5997, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "activeFeeTier", "offset": 20, "slot": "13", "type": "t_uint24"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_contract(INonfungiblePositionManager)11612": {"encoding": "inplace", "label": "contract INonfungiblePositionManager", "numberOfBytes": "20"}, "t_contract(MoonbagsStake)10580": {"encoding": "inplace", "label": "contract MoonbagsStake", "numberOfBytes": "20"}, "t_contract(TokenLock)11100": {"encoding": "inplace", "label": "contract TokenLock", "numberOfBytes": "20"}, "t_mapping(t_address,t_struct(Pool)5966_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => struct MoonbagsLaunchpad.Pool)", "numberOfBytes": "32", "value": "t_struct(Pool)5966_storage"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_struct(Configuration)5935_storage": {"encoding": "inplace", "label": "struct MoonbagsLaunchpad.Configuration", "members": [{"astId": 5912, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "admin", "offset": 0, "slot": "0", "type": "t_address"}, {"astId": 5914, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "treasury", "offset": 0, "slot": "1", "type": "t_address"}, {"astId": 5916, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "feePlatformRecipient", "offset": 0, "slot": "2", "type": "t_address"}, {"astId": 5918, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "platformFee", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 5920, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "initialVirtualTokenReserves", "offset": 0, "slot": "4", "type": "t_uint256"}, {"astId": 5922, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "remainTokenReserves", "offset": 0, "slot": "5", "type": "t_uint256"}, {"astId": 5924, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "tokenDecimals", "offset": 0, "slot": "6", "type": "t_uint8"}, {"astId": 5926, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "initPlatformFeeWithdraw", "offset": 1, "slot": "6", "type": "t_uint16"}, {"astId": 5928, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "initCreatorFeeWithdraw", "offset": 3, "slot": "6", "type": "t_uint16"}, {"astId": 5930, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "initStakeFeeWithdraw", "offset": 5, "slot": "6", "type": "t_uint16"}, {"astId": 5932, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "initPlatformStakeFeeWithdraw", "offset": 7, "slot": "6", "type": "t_uint16"}, {"astId": 5934, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "platformTokenAddress", "offset": 9, "slot": "6", "type": "t_address"}], "numberOfBytes": "224"}, "t_struct(Pool)5966_storage": {"encoding": "inplace", "label": "struct MoonbagsLaunchpad.Pool", "members": [{"astId": 5937, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "realHypeReserves", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 5939, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "realTokenReserves", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 5941, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "virtualTokenReserves", "offset": 0, "slot": "2", "type": "t_uint256"}, {"astId": 5943, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "virtualHypeReserves", "offset": 0, "slot": "3", "type": "t_uint256"}, {"astId": 5945, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "remainTokenReserves", "offset": 0, "slot": "4", "type": "t_uint256"}, {"astId": 5947, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "virtualRemainTokenReserves", "offset": 0, "slot": "5", "type": "t_uint256"}, {"astId": 5949, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "feeRecipient", "offset": 0, "slot": "6", "type": "t_uint256"}, {"astId": 5951, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "isCompleted", "offset": 0, "slot": "7", "type": "t_bool"}, {"astId": 5953, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "threshold", "offset": 0, "slot": "8", "type": "t_uint256"}, {"astId": 5955, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "platformFeeWithdraw", "offset": 0, "slot": "9", "type": "t_uint16"}, {"astId": 5957, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 2, "slot": "9", "type": "t_uint16"}, {"astId": 5959, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "stakeFeeWithdraw", "offset": 4, "slot": "9", "type": "t_uint16"}, {"astId": 5961, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "platformStakeFeeWithdraw", "offset": 6, "slot": "9", "type": "t_uint16"}, {"astId": 5963, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "creationTimestamp", "offset": 0, "slot": "10", "type": "t_uint256"}, {"astId": 5965, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "feeDistributionUnlockTime", "offset": 0, "slot": "11", "type": "t_uint256"}], "numberOfBytes": "384"}, "t_struct(ThresholdConfig)5969_storage": {"encoding": "inplace", "label": "struct MoonbagsLaunchpad.ThresholdConfig", "members": [{"astId": 5968, "contract": "contracts/MoonbagsLaunchpad.sol:MoonbagsLaunchpad", "label": "threshold", "offset": 0, "slot": "0", "type": "t_uint256"}], "numberOfBytes": "32"}, "t_uint16": {"encoding": "inplace", "label": "uint16", "numberOfBytes": "2"}, "t_uint24": {"encoding": "inplace", "label": "uint24", "numberOfBytes": "3"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"encoding": "inplace", "label": "uint8", "numberOfBytes": "1"}}}}
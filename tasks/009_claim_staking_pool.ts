import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { IERC20Metadata, MoonbagsStake } from "../types";
import { getStakeAddress } from "./utils/deployments";

//Example: npx hardhat claim-staking-pool --network hyperevm-testnet --token ******************************************
task("claim-staking-pool", "Claim HYPE rewards from a staking pool")
    .addParam("token", "The token address of the staking pool")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [claimer] = await ethers.getSigners();

        console.log("MoonbagsStake - Claim Staking Pool Rewards");
        console.log("============================================");
        console.log("Claimer account:", claimer.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        try {
            const STAKE_ADDRESS = getStakeAddress(network.config);

            const moonbagsStake = (await ethers.getContractAt(
                "MoonbagsStake",
                STAKE_ADDRESS
            )) as MoonbagsStake;

            const stakingToken = (await ethers.getContractAt(
                "IERC20Metadata",
                tokenAddress
            )) as IERC20Metadata;

            // Check if staking pool exists
            const stakingPoolExists = await moonbagsStake.stakingPoolExists(tokenAddress);
            if (!stakingPoolExists) {
                throw new Error(
                    "Staking pool does not exist for this token. Please initialize it first."
                );
            }

            // Check if staking account exists
            const accountExists = await moonbagsStake.stakingAccountExists(
                tokenAddress,
                claimer.address
            );
            if (!accountExists) {
                throw new Error(
                    "No staking account found for this address. Please stake tokens first."
                );
            }

            console.log("\nPre-claim Status:");

            // Check current HYPE balance
            const hyeBalance = await ethers.provider.getBalance(claimer.address);
            console.log("  Current HYPE Balance:", ethers.formatEther(hyeBalance), "HYPE");

            // Get token decimals for display
            const decimals = await stakingToken.decimals();

            // Get current staking account info
            const accountInfo = await moonbagsStake.getStakingAccountInfo(
                tokenAddress,
                claimer.address
            );
            console.log(
                "  Staked Balance:",
                ethers.formatUnits(accountInfo.balance, decimals),
                "tokens"
            );
            console.log(
                "  Unstake Deadline:",
                new Date(Number(accountInfo.unstakeDeadline) * 1000).toISOString()
            );

            // Calculate total rewards including pending
            const totalEarned = await moonbagsStake.calculateRewardsEarned(tokenAddress);
            console.log(
                "  Total Rewards (including pending):",
                ethers.formatEther(totalEarned),
                "HYPE"
            );

            if (totalEarned === 0n) {
                console.log("⚠️  No rewards available to claim");
                return;
            }

            // Get pool info
            const poolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
            console.log(
                "  Pool Total Supply:",
                ethers.formatUnits(poolInfo.totalSupply, decimals),
                "tokens"
            );
            console.log("  Pool Total Rewards:", ethers.formatEther(poolInfo.totalRewards), "HYPE");

            console.log("\n⏳ Claiming rewards...");
            const claimTx = await moonbagsStake.claimStakingPool(tokenAddress);
            console.log("⏳ Claim transaction submitted:", claimTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const claimReceipt = await claimTx.wait();
            console.log("✅ Claim transaction confirmed in block:", claimReceipt?.blockNumber);

            // Check for ClaimStakingPoolEvent
            const claimEvent = claimReceipt?.logs.find((log) => {
                try {
                    const parsed = moonbagsStake.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "ClaimStakingPoolEvent";
                } catch {
                    return false;
                }
            });

            let claimedAmount = 0n;
            let isAccountDeleted = false;

            if (claimEvent) {
                const parsed = moonbagsStake.interface.parseLog({
                    topics: claimEvent.topics,
                    data: claimEvent.data,
                });
                claimedAmount = parsed?.args[2];
                isAccountDeleted = parsed?.args[3];

                console.log("Rewards Claimed Successfully!");
                console.log("  Token:", parsed?.args[0]);
                console.log("  Claimer:", parsed?.args[1]);
                console.log("  Claimed Amount:", ethers.formatEther(claimedAmount), "HYPE");
                console.log("  Account Deleted:", isAccountDeleted);
                console.log("  Timestamp:", new Date(Number(parsed?.args[4]) * 1000).toISOString());
            }

            // Display post-claim status
            console.log("\nPost-claim Status:");

            const newHypeBalance = await ethers.provider.getBalance(claimer.address);
            console.log("  New HYPE Balance:", ethers.formatEther(newHypeBalance), "HYPE");
            console.log("  HYPE Gained:", ethers.formatEther(newHypeBalance - hyeBalance), "HYPE");

            // Check if account still exists
            const newAccountExists = await moonbagsStake.stakingAccountExists(
                tokenAddress,
                claimer.address
            );

            if (newAccountExists) {
                const newAccountInfo = await moonbagsStake.getStakingAccountInfo(
                    tokenAddress,
                    claimer.address
                );
                console.log(
                    "  Remaining Staked Balance:",
                    ethers.formatUnits(newAccountInfo.balance, decimals),
                    "tokens"
                );
                console.log(
                    "  Unstake Deadline:",
                    new Date(Number(newAccountInfo.unstakeDeadline) * 1000).toISOString()
                );
            } else {
                console.log("  Staking account was deleted (no remaining balance or rewards)");
            }

            const newPoolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
            console.log(
                "  Pool Total Supply:",
                ethers.formatUnits(newPoolInfo.totalSupply, decimals),
                "tokens"
            );
            console.log(
                "  Pool Remaining Rewards:",
                ethers.formatEther(newPoolInfo.totalRewards),
                "HYPE"
            );

            console.log("\n✅ Reward claiming completed successfully!");
        } catch (error) {
            console.error("❌ Error claiming rewards:", error);
        }
    });

export {};

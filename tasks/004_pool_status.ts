import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { uniswapV3Addresses } from "../config/uniswapV3Addresses";
import { MoonbagsLaunchpad } from "../types";
import { getLaunchpadAddress } from "./utils/deployments";

//Example: npx hardhat pool-status --network hyperevm-testnet --token ******************************************
task("pool-status", "Check the status of a pool in MoonbagsLaunchpad by token address")
    .addParam("token", "The token address to check")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [signer] = await ethers.getSigners();

        console.log("MoonbagsLaunchpad - Pool Status Check");
        console.log("=====================================");
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);
        console.log("Checking token:", taskArgs.token);

        const tokenAddress = taskArgs.token;

        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        try {
            const LAUNCHPAD_ADDRESS = getLaunchpadAddress(network.config);
            console.log("Launchpad address:", LAUNCHPAD_ADDRESS);

            const launchpad = (await ethers.getContractAt(
                "MoonbagsLaunchpad",
                LAUNCHPAD_ADDRESS
            )) as MoonbagsLaunchpad;

            // Check if token exists in launchpad
            const isValidToken = await launchpad.isToken(tokenAddress);
            if (!isValidToken) {
                console.log("❌ Token not found in MoonbagsLaunchpad");
                console.log("This token was not created through the launchpad or doesn't exist.");
                return;
            }

            console.log("✅ Token found in launchpad");

            // Get token contract and basic info
            const tokenContract = await ethers.getContractAt("IERC20Extended", tokenAddress);
            const tokenDecimals = Number(await tokenContract.decimals());
            const tokenName = await tokenContract.name();
            const tokenSymbol = await tokenContract.symbol();

            console.log("\nToken Information:");
            console.log("  Name:", tokenName);
            console.log("  Symbol:", tokenSymbol);
            console.log("  Decimals:", tokenDecimals);
            console.log("  Address:", tokenAddress);

            // Get pool data
            const pool = await launchpad.getPool(tokenAddress);

            console.log("\nPool Status:");
            console.log("  Is Completed:", pool.isCompleted ? "✅ YES" : "❌ NO");
            console.log("  Threshold:", ethers.formatEther(pool.threshold), "ETH");

            console.log("\nPool Reserves:");
            console.log("  Real HYPE Reserves:", ethers.formatEther(pool.realHypeReserves), "ETH");
            console.log(
                "  Real Token Reserves:",
                ethers.formatUnits(pool.realTokenReserves, tokenDecimals)
            );
            console.log(
                "  Virtual HYPE Reserves:",
                ethers.formatEther(pool.virtualHypeReserves),
                "ETH"
            );
            console.log(
                "  Virtual Token Reserves:",
                ethers.formatUnits(pool.virtualTokenReserves, tokenDecimals)
            );
            console.log(
                "  Remain Token Reserves:",
                ethers.formatUnits(pool.remainTokenReserves, tokenDecimals)
            );
            console.log(
                "  Virtual Remain Token Reserves:",
                ethers.formatUnits(pool.virtualRemainTokenReserves, tokenDecimals)
            );

            // Calculate available tokens for trading
            const availableTokens = pool.realTokenReserves;
            const realHypeReserves = pool.realHypeReserves;

            console.log("\nTrading Information:");
            console.log(
                "  Available Tokens for Trading:",
                ethers.formatUnits(availableTokens, tokenDecimals)
            );
            console.log(
                "  Trading Progress:",
                availableTokens === 0n
                    ? "100%"
                    : `${((realHypeReserves * 100n) / pool.threshold).toString()}%`
            );

            console.log("\nFee Information:");
            console.log("  Accumulated Fees:", ethers.formatEther(pool.feeRecipient), "ETH");
            console.log(
                "  Platform Fee Withdraw:",
                (Number(pool.platformFeeWithdraw) / 100).toString() + "%"
            );
            console.log(
                "  Creator Fee Withdraw:",
                (Number(pool.creatorFeeWithdraw) / 100).toString() + "%"
            );
            console.log(
                "  Stake Fee Withdraw:",
                (Number(pool.stakeFeeWithdraw) / 100).toString() + "%"
            );
            console.log(
                "  Platform Stake Fee Withdraw:",
                (Number(pool.platformStakeFeeWithdraw) / 100).toString() + "%"
            );

            // Get V3 pool information if completed
            if (pool.isCompleted) {
                const positionId = await launchpad.tokenToPositionId(tokenAddress);

                console.log("\nHyperSwap V3 Information:");
                console.log("  Position NFT ID:", positionId.toString());

                if (positionId > 0) {
                    try {
                        const positionManager = await ethers.getContractAt(
                            "INonfungiblePositionManager",
                            await launchpad.nonfungiblePositionManager()
                        );
                        const position = await positionManager.positions(positionId);
                        console.log("  Liquidity:", position.liquidity.toString());
                        console.log("  Tick Lower:", position.tickLower.toString());
                        console.log("  Tick Upper:", position.tickUpper.toString());
                        console.log("  Token 0:", position.token0);
                        console.log("  Token 1:", position.token1);
                        console.log("  Fee:", position.fee.toString());
                        console.log(
                            "  Fee Growth Inside 0:",
                            position.feeGrowthInside0LastX128.toString()
                        );
                        console.log(
                            "  Fee Growth Inside 1:",
                            position.feeGrowthInside1LastX128.toString()
                        );

                        const factoryAddress = uniswapV3Addresses[network.name]?.factory;
                        if (factoryAddress) {
                            const factory = await ethers.getContractAt(
                                "IUniswapV3Factory",
                                factoryAddress
                            );
                            const poolAddress = await factory.getPool(
                                position.token0,
                                position.token1,
                                position.fee
                            );
                            console.log("  V3 Pool Address:", poolAddress);
                        }
                    } catch (error) {
                        console.log("Error fetching V3 position details:", error);
                    }
                }
            }

            // Price estimation (if pool is active)
            if (!pool.isCompleted && availableTokens > 0) {
                console.log("\nPrice Estimation (for 0.01 ETH):");

                try {
                    const testAmount = ethers.parseEther("0.01");
                    const estimatedTokens = await launchpad.estimateBuyExactInTokens(
                        tokenAddress,
                        testAmount
                    );
                    const estimatedCost = await launchpad.estimateBuyExactInCost(
                        tokenAddress,
                        testAmount
                    );

                    if (estimatedTokens > 0) {
                        const pricePerToken =
                            (testAmount * BigInt(10 ** tokenDecimals)) / estimatedTokens;
                        console.log(
                            "  Estimated tokens for 0.01 ETH:",
                            ethers.formatUnits(estimatedTokens, tokenDecimals)
                        );
                        console.log(
                            "  Total cost (with fees):",
                            ethers.formatEther(estimatedCost),
                            "ETH"
                        );
                        console.log("  Price per token:", ethers.formatEther(pricePerToken), "ETH");
                    }
                } catch (error) {
                    console.log("  ⚠️  Could not estimate price");
                }
            }

            // Get user's token balance if they have any
            try {
                const userBalance = await tokenContract.balanceOf(signer.address);
                if (userBalance > 0) {
                    console.log("\nYour Token Balance:");
                    console.log(
                        "  Balance:",
                        ethers.formatUnits(userBalance, tokenDecimals),
                        tokenSymbol
                    );

                    if (!pool.isCompleted) {
                        try {
                            const sellEstimate = await launchpad.estimateSellTokens(
                                tokenAddress,
                                userBalance
                            );
                            console.log(
                                "  Estimated ETH if sold:",
                                ethers.formatEther(sellEstimate),
                                "ETH"
                            );
                        } catch (error) {
                            console.log("  ⚠️  Could not estimate sell value");
                        }
                    }
                }
            } catch (error) {
                console.log("  ⚠️  Could not fetch your token balance");
            }

            console.log("\n" + "=".repeat(50));

            if (pool.isCompleted) {
                console.log(
                    "This token has completed its bonding curve and migrated to HyperSwap V3!"
                );
                console.log("You can now trade it on HyperSwap DEX.");
            } else {
                console.log("This token is still in the bonding curve phase.");
                console.log("You can buy/sell tokens until the threshold is reached.");
            }
        } catch (error) {
            console.error("❌ Error checking pool status:", error);
        }
    });

export {};

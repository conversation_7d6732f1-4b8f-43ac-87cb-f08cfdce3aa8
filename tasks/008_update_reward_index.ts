import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsStake } from "../types";
import { getStakeAddress } from "./utils/deployments";

task("update-reward-index", "Update reward index by adding HYPE rewards to a staking pool")
    .addParam("token", "The token address of the staking pool")
    .addParam("reward", "Amount of HYPE rewards to add (in HYPE units)")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [rewardProvider] = await ethers.getSigners();

        console.log("💰 MoonbagsStake - Update Reward Index");
        console.log("=====================================");
        console.log("Reward Provider account:", rewardProvider.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;
        const rewardAmount = taskArgs.reward;

        console.log("\n🎯 Parameters:");
        console.log("  Token Address:", tokenAddress);
        console.log("  Reward Amount:", rewardAmount, "HYPE");

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        // Validate reward amount
        if (!rewardAmount || parseFloat(rewardAmount) <= 0) {
            throw new Error("Invalid reward amount provided");
        }

        try {
            const STAKE_ADDRESS = getStakeAddress(network.config);

            const moonbagsStake = (await ethers.getContractAt(
                "MoonbagsStake",
                STAKE_ADDRESS
            )) as MoonbagsStake;

            // Check if staking pool exists
            const stakingPoolExists = await moonbagsStake.stakingPoolExists(tokenAddress);
            if (!stakingPoolExists) {
                throw new Error(
                    "Staking pool does not exist for this token. Please initialize it first."
                );
            }

            // Convert reward amount to wei
            const rewardAmountWei = ethers.parseEther(rewardAmount);

            console.log("\n📋 Pre-update Status:");

            // Check HYPE balance
            const hyeBalance = await ethers.provider.getBalance(rewardProvider.address);
            console.log("  HYPE Balance:", ethers.formatEther(hyeBalance), "HYPE");

            if (hyeBalance < rewardAmountWei) {
                throw new Error("Insufficient HYPE balance for reward update");
            }

            // Get current pool info
            const poolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
            console.log("  Pool Total Supply:", ethers.formatEther(poolInfo.totalSupply), "tokens");
            console.log("  Current Reward Index:", poolInfo.rewardIndex.toString());
            console.log(
                "  Pending Initial Rewards:",
                ethers.formatEther(poolInfo.pendingInitialRewards),
                "HYPE"
            );
            console.log(
                "  Current Total Rewards:",
                ethers.formatEther(poolInfo.totalRewards),
                "HYPE"
            );

            // Determine if this will be initial rewards or regular rewards
            const isInitialRewards = poolInfo.totalSupply === 0n;
            console.log("  Will be Initial Rewards:", isInitialRewards);

            console.log("\n⏳ Updating reward index...");
            const updateTx = await moonbagsStake.updateRewardIndex(tokenAddress, {
                value: rewardAmountWei,
            });
            console.log("⏳ Update transaction submitted:", updateTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const updateReceipt = await updateTx.wait();
            console.log("✅ Update transaction confirmed in block:", updateReceipt?.blockNumber);

            // Check for UpdateRewardIndexEvent
            const updateEvent = updateReceipt?.logs.find((log) => {
                try {
                    const parsed = moonbagsStake.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "UpdateRewardIndexEvent";
                } catch {
                    return false;
                }
            });

            if (updateEvent) {
                const parsed = moonbagsStake.interface.parseLog({
                    topics: updateEvent.topics,
                    data: updateEvent.data,
                });
                console.log("🎉 Reward Index Updated Successfully!");
                console.log("  Token:", parsed?.args[0]);
                console.log("  Reward Updater:", parsed?.args[1]);
                console.log("  Reward Amount:", ethers.formatEther(parsed?.args[2]), "HYPE");
                console.log("  Is Initial Rewards:", parsed?.args[3]);
                console.log("  Timestamp:", new Date(Number(parsed?.args[4]) * 1000).toISOString());
            }

            // Display post-update status
            console.log("\n📊 Post-update Status:");
            const newPoolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);

            console.log(
                "  Pool Total Supply:",
                ethers.formatEther(newPoolInfo.totalSupply),
                "tokens"
            );
            console.log("  New Reward Index:", newPoolInfo.rewardIndex.toString());
            console.log(
                "  New Pending Initial Rewards:",
                ethers.formatEther(newPoolInfo.pendingInitialRewards),
                "HYPE"
            );
            console.log(
                "  New Total Rewards:",
                ethers.formatEther(newPoolInfo.totalRewards),
                "HYPE"
            );

            const newHypeBalance = await ethers.provider.getBalance(rewardProvider.address);
            console.log("  Remaining HYPE Balance:", ethers.formatEther(newHypeBalance), "HYPE");

            // Calculate reward index change
            if (!isInitialRewards && poolInfo.totalSupply > 0n) {
                const rewardIndexIncrease = newPoolInfo.rewardIndex - poolInfo.rewardIndex;
                console.log("  Reward Index Increase:", rewardIndexIncrease.toString());

                // Calculate rewards per token
                const rewardsPerToken =
                    (rewardAmountWei * ethers.parseUnits("1", 16)) / poolInfo.totalSupply;
                console.log(
                    "  Rewards per Token:",
                    ethers.formatEther(rewardsPerToken),
                    "HYPE per token"
                );
            }

            console.log("\n✅ Reward index update completed successfully!");
        } catch (error) {
            console.error("❌ Error updating reward index:", error);
        }
    });

export {};

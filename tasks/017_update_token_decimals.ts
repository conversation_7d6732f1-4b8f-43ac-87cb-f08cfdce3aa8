import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsLaunchpad } from "../types";
import { getLaunchpadAddress } from "./utils/deployments";

// Example: npx hardhat update-token-decimals --network hyperevm-testnet
task("update-token-decimals", "Update token decimals configuration to 18").setAction(
    async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [owner] = await ethers.getSigners();

        console.log("🔧 MoonbagsLaunchpad - Update Token Decimals");
        console.log("============================================");
        console.log("Owner account:", owner.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        try {
            const LAUNCHPAD_ADDRESS = getLaunchpadAddress(network.config);

            const launchpad = (await ethers.getContractAt(
                "MoonbagsLaunchpad",
                LAUNCHPAD_ADDRESS
            )) as MoonbagsLaunchpad;

            // Check if the caller is the owner
            const contractOwner = await launchpad.owner();
            if (owner.address.toLowerCase() !== contractOwner.toLowerCase()) {
                throw new Error(
                    `Only the contract owner can update configuration. Contract owner: ${contractOwner}, Your address: ${owner.address}`
                );
            }

            // Get current configuration
            const currentConfig = await launchpad.config();
            console.log("\n📊 Current Configuration:");
            console.log("  Platform Fee:", currentConfig.platformFee.toString());
            console.log(
                "  Initial Virtual Token Reserves:",
                ethers.formatUnits(
                    currentConfig.initialVirtualTokenReserves,
                    currentConfig.tokenDecimals
                )
            );
            console.log(
                "  Remain Token Reserves:",
                ethers.formatUnits(currentConfig.remainTokenReserves, currentConfig.tokenDecimals)
            );
            console.log("  Current Token Decimals:", currentConfig.tokenDecimals.toString());
            console.log(
                "  Platform Fee Withdraw:",
                currentConfig.initPlatformFeeWithdraw.toString()
            );
            console.log("  Creator Fee Withdraw:", currentConfig.initCreatorFeeWithdraw.toString());
            console.log("  Stake Fee Withdraw:", currentConfig.initStakeFeeWithdraw.toString());
            console.log(
                "  Platform Stake Fee Withdraw:",
                currentConfig.initPlatformStakeFeeWithdraw.toString()
            );
            console.log("  Platform Token Address:", currentConfig.platformTokenAddress);

            if (currentConfig.tokenDecimals === 18n) {
                console.log("\n✅ Token decimals is already set to 18. No update needed.");
                return;
            }

            console.log("\n⏳ Updating token decimals to 18...");

            // Call the updateConfiguration function with current values but tokenDecimals = 18
            const tx = await launchpad.updateConfiguration(
                currentConfig.platformFee,
                currentConfig.initialVirtualTokenReserves,
                currentConfig.remainTokenReserves,
                18, // Set tokenDecimals to 18
                currentConfig.initPlatformFeeWithdraw,
                currentConfig.initCreatorFeeWithdraw,
                currentConfig.initStakeFeeWithdraw,
                currentConfig.initPlatformStakeFeeWithdraw,
                currentConfig.platformTokenAddress
            );

            console.log("⏳ Transaction submitted:", tx.hash);
            console.log("⏳ Waiting for confirmation...");

            const receipt = await tx.wait();
            console.log("✅ Transaction confirmed in block:", receipt?.blockNumber);

            // Check for ConfigurationUpdated event
            const configUpdatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "ConfigurationUpdated";
                } catch {
                    return false;
                }
            });

            if (configUpdatedEvent) {
                const parsed = launchpad.interface.parseLog({
                    topics: configUpdatedEvent.topics,
                    data: configUpdatedEvent.data,
                });

                console.log("\n🎉 Configuration Updated Successfully!");
                console.log("  Platform Fee:", parsed?.args[0].toString());
                console.log(
                    "  Initial Virtual Token Reserves:",
                    ethers.formatUnits(parsed?.args[1], 18)
                );
                console.log("  Remain Token Reserves:", ethers.formatUnits(parsed?.args[2], 18));
                console.log("  New Token Decimals:", parsed?.args[3].toString());
                console.log("  Platform Fee Withdraw:", parsed?.args[4].toString());
                console.log("  Creator Fee Withdraw:", parsed?.args[5].toString());
                console.log("  Stake Fee Withdraw:", parsed?.args[6].toString());
                console.log("  Platform Stake Fee Withdraw:", parsed?.args[7].toString());
                console.log("  Platform Token Address:", parsed?.args[8]);
                console.log("  Timestamp:", new Date(Number(parsed?.args[9]) * 1000).toISOString());
            }

            // Verify the update
            const updatedConfig = await launchpad.config();
            console.log("\n📊 Updated Configuration:");
            console.log("  Token Decimals:", updatedConfig.tokenDecimals.toString());

            if (updatedConfig.tokenDecimals === 18n) {
                console.log("\n✅ Token decimals successfully updated to 18!");
            } else {
                console.log(
                    "\n❌ Token decimals update failed. Current value:",
                    updatedConfig.tokenDecimals.toString()
                );
            }

            console.log("\n✅ Update token decimals task completed successfully!");
        } catch (error) {
            console.error("❌ Error updating token decimals:", error);
        }
    }
);

export {};

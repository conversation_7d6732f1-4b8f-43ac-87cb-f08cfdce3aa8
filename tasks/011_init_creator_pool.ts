import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsStake } from "../types";
import { getStakeAddress } from "./utils/deployments";

task("init-creator-pool", "Initialize creator pool for a token")
    .addParam("token", "The token address to initialize creator pool for")
    .addParam("creator", "The creator address for the creator pool")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [signer] = await ethers.getSigners();

        console.log("MoonbagsStake - Initialize Creator Pool");
        console.log("===========================================");
        console.log("Signer account:", signer.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;
        const creatorAddress = taskArgs.creator;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);
        console.log("  Creator Address:", creatorAddress);

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        // Validate creator address
        if (!ethers.isAddress(creatorAddress)) {
            throw new Error("Invalid creator address provided");
        }

        try {
            const STAKE_ADDRESS = getStakeAddress(network.config);

            const moonbagsStake = (await ethers.getContractAt(
                "MoonbagsStake",
                STAKE_ADDRESS
            )) as MoonbagsStake;

            console.log("\nCurrent Status:");

            // Check if creator pool already exists
            const creatorPoolExists = await moonbagsStake.creatorPoolExists(tokenAddress);
            console.log("  Creator Pool Exists:", creatorPoolExists);

            if (creatorPoolExists) {
                const creatorInfo = await moonbagsStake.getCreatorPoolInfo(tokenAddress);
                console.log("  Existing Creator:", creatorInfo.creator);
                console.log(
                    "  Existing Total Rewards:",
                    ethers.formatEther(creatorInfo.totalRewards),
                    "HYPE"
                );

                if (creatorInfo.creator.toLowerCase() !== creatorAddress.toLowerCase()) {
                    throw new Error(
                        `Creator pool already exists with different creator: ${creatorInfo.creator}`
                    );
                }

                console.log(
                    "ℹ️  Creator pool already exists with same creator, skipping initialization"
                );
            } else {
                console.log("\n⏳ Initializing creator pool...");
                const initTx = await moonbagsStake.initializeCreatorPool(
                    tokenAddress,
                    creatorAddress
                );
                console.log("⏳ Creator pool transaction submitted:", initTx.hash);
                console.log("⏳ Waiting for confirmation...");

                const initReceipt = await initTx.wait();
                console.log("✅ Creator pool initialized in block:", initReceipt?.blockNumber);

                // Check for InitializeCreatorPoolEvent
                const creatorEvent = initReceipt?.logs.find((log) => {
                    try {
                        const parsed = moonbagsStake.interface.parseLog({
                            topics: log.topics,
                            data: log.data,
                        });
                        return parsed?.name === "InitializeCreatorPoolEvent";
                    } catch {
                        return false;
                    }
                });

                if (creatorEvent) {
                    const parsed = moonbagsStake.interface.parseLog({
                        topics: creatorEvent.topics,
                        data: creatorEvent.data,
                    });
                    console.log("Creator Pool Initialized Successfully!");
                    console.log("  Token:", parsed?.args[0]);
                    console.log("  Initializer:", parsed?.args[1]);
                    console.log("  Creator:", parsed?.args[2]);
                    console.log(
                        "  Timestamp:",
                        new Date(Number(parsed?.args[3]) * 1000).toISOString()
                    );
                }
            }

            // Display final status
            console.log("\nFinal Creator Pool Status:");
            const finalCreatorExists = await moonbagsStake.creatorPoolExists(tokenAddress);
            console.log(
                "  Creator Pool:",
                finalCreatorExists ? "✅ Initialized" : "❌ Not initialized"
            );

            if (finalCreatorExists) {
                const creatorInfo = await moonbagsStake.getCreatorPoolInfo(tokenAddress);
                console.log("\nCreator Pool Info:");
                console.log("  Creator:", creatorInfo.creator);
                console.log(
                    "  Total Rewards:",
                    ethers.formatEther(creatorInfo.totalRewards),
                    "HYPE"
                );

                // Check if creator is the signer
                if (creatorInfo.creator.toLowerCase() === signer.address.toLowerCase()) {
                    console.log("  ✅ You are the creator of this pool");
                } else {
                    console.log("  ℹ️  You are not the creator of this pool");
                }
            }

            // Also check if staking pool exists
            const stakingPoolExists = await moonbagsStake.stakingPoolExists(tokenAddress);
            console.log("  Staking Pool:", stakingPoolExists ? "✅ Exists" : "❌ Not initialized");

            if (!stakingPoolExists) {
                console.log(
                    "\nTip: You may also want to initialize the staking pool for this token using:"
                );
                console.log(
                    `    npx hardhat init-staking-pool --token ${tokenAddress} --network ${network.name}`
                );
            }

            console.log("\n✅ Creator pool initialization completed successfully!");
        } catch (error) {
            console.error("❌ Error initializing creator pool:", error);
            throw error;
        }
    });

export {};

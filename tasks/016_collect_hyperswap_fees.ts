import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsLaunchpad } from "../types";
import { getLaunchpadAddress } from "./utils/deployments";

//Example: npx hardhat collect-hyperswap-fees --network hyperevm-testnet --token ******************************************
task("collect-hyperswap-fees", "Collect all fees from a HyperSwap V3 position")
    .addParam("token", "The token address associated with the HyperSwap position")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [collector] = await ethers.getSigners();

        console.log("MoonbagsLaunchpad - Collect HyperSwap Fees");
        console.log("============================================");
        console.log("Collector account:", collector.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        try {
            const LAUNCHPAD_ADDRESS = getLaunchpadAddress(network.config);

            const launchpad = (await ethers.getContractAt(
                "MoonbagsLaunchpad",
                LAUNCHPAD_ADDRESS
            )) as MoonbagsLaunchpad;

            // Check if token exists
            const isToken = await launchpad.isToken(tokenAddress);
            if (!isToken) {
                throw new Error("Token does not exist in the launchpad. Please create it first.");
            }

            console.log("\nPre-collection Status:");

            // Get pool information
            const poolInfo = await launchpad.pools(tokenAddress);
            console.log("  Pool Completed:", poolInfo.isCompleted);

            if (!poolInfo.isCompleted) {
                throw new Error(
                    "Pool is not completed yet. HyperSwap position is only created after pool completion."
                );
            }

            // Get position ID
            const positionId = await launchpad.tokenToPositionId(tokenAddress);
            console.log("  Position ID:", positionId.toString());

            if (positionId === 0n) {
                throw new Error(
                    "No HyperSwap position found for this token. Position may not have been created yet."
                );
            }

            // Get current HYPE balance
            const hyeBalance = await ethers.provider.getBalance(collector.address);
            console.log("  Current HYPE Balance:", ethers.formatEther(hyeBalance), "HYPE");

            // Get current token balance
            const tokenContract = await ethers.getContractAt("IERC20Metadata", tokenAddress);
            const tokenBalance = await tokenContract.balanceOf(collector.address);
            const decimals = await tokenContract.decimals();
            console.log(
                "  Current Token Balance:",
                ethers.formatUnits(tokenBalance, decimals),
                "tokens"
            );

            console.log("\n⏳ Collecting HyperSwap fees...");
            const collectTx = await launchpad.collectHyperSwapFees(tokenAddress);
            console.log("⏳ Collection transaction submitted:", collectTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const collectReceipt = await collectTx.wait();
            console.log(
                "✅ Collection transaction confirmed in block:",
                collectReceipt?.blockNumber
            );
        } catch (error) {
            console.error("❌ Error collecting HyperSwap fees:", error);
            throw error;
        }
    });

export {};

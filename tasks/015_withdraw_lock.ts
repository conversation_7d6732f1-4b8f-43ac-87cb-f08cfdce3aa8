import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { IERC20Metadata, TokenLock } from "../types";
import { getTokenLockAddress } from "./utils/deployments";

//Example: npx hardhat withdraw-lock --network hyperevm-testnet --lockid 1
task("withdraw-lock", "Withdraw tokens from a lock after the lock period has ended")
    .addParam("lockid", "The lock ID to withdraw from")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [withdrawer] = await ethers.getSigners();

        console.log("TokenLock - Withdraw Lock");
        console.log("===========================");
        console.log("Withdrawer account:", withdrawer.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const lockId = taskArgs.lockid;

        console.log("\nParameters:");
        console.log("  Lock ID:", lockId);

        // Validate lock ID
        if (!lockId || parseInt(lockId) <= 0) {
            throw new Error("Invalid lock ID provided");
        }

        try {
            const TOKEN_LOCK_ADDRESS = getTokenLockAddress(network.config);

            const tokenLock = (await ethers.getContractAt(
                "TokenLock",
                TOKEN_LOCK_ADDRESS
            )) as TokenLock;

            // Check if lock exists
            const nextLockId = await tokenLock.nextLockId();
            if (parseInt(lockId) >= Number(nextLockId)) {
                throw new Error(`Lock ID ${lockId} does not exist. Next lock ID is ${nextLockId}`);
            }

            console.log("\nPre-withdrawal Status:");

            // Get lock details
            const lockDetails = await tokenLock.getLock(lockId);
            console.log("  Lock ID:", lockId);
            console.log("  Token:", lockDetails.token);
            console.log("  Locker:", lockDetails.locker);
            console.log("  Recipient:", lockDetails.recipient);
            console.log(
                "  Start Time:",
                new Date(Number(lockDetails.startTime) * 1000).toISOString()
            );
            console.log("  End Time:", new Date(Number(lockDetails.endTime) * 1000).toISOString());
            console.log("  Closed:", lockDetails.closed);

            // Check if lock is already closed
            if (lockDetails.closed) {
                throw new Error("Lock has already been withdrawn");
            }

            // Get token contract for decimals and balance checking
            const token = (await ethers.getContractAt(
                "IERC20Metadata",
                lockDetails.token
            )) as IERC20Metadata;

            const decimals = await token.decimals();
            console.log("  Amount:", ethers.formatUnits(lockDetails.amount, decimals), "tokens");

            // Check if withdrawable
            const isWithdrawable = await tokenLock.isWithdrawable(lockId);
            console.log("  Currently Withdrawable:", isWithdrawable);

            if (!isWithdrawable) {
                const currentTime = Math.floor(Date.now() / 1000);
                if (currentTime < Number(lockDetails.endTime)) {
                    const timeRemaining = Number(lockDetails.endTime) - currentTime;
                    const hoursRemaining = Math.ceil(timeRemaining / 3600);
                    throw new Error(
                        `Lock period has not ended yet. Must wait ${hoursRemaining} more hour(s) until ${new Date(Number(lockDetails.endTime) * 1000).toISOString()}`
                    );
                }
            }

            // Check authorization
            const config = await tokenLock.config();
            const isAuthorized =
                withdrawer.address.toLowerCase() === lockDetails.recipient.toLowerCase() ||
                withdrawer.address.toLowerCase() === lockDetails.locker.toLowerCase() ||
                withdrawer.address.toLowerCase() === config.admin.toLowerCase();

            if (!isAuthorized) {
                throw new Error(
                    `Unauthorized to withdraw this lock. Only the recipient (${lockDetails.recipient}), locker (${lockDetails.locker}), or admin (${config.admin}) can withdraw.`
                );
            }

            console.log("  Authorization:", "✅ Authorized");
            console.log(
                "  Your Role:",
                withdrawer.address.toLowerCase() === lockDetails.recipient.toLowerCase()
                    ? "Recipient"
                    : withdrawer.address.toLowerCase() === lockDetails.locker.toLowerCase()
                      ? "Locker"
                      : withdrawer.address.toLowerCase() === config.admin.toLowerCase()
                        ? "Admin"
                        : "Unknown"
            );

            // Check recipient's current token balance
            const recipientBalance = await token.balanceOf(lockDetails.recipient);
            console.log(
                "  Recipient Current Balance:",
                ethers.formatUnits(recipientBalance, decimals),
                "tokens"
            );

            // Check TokenLock contract balance
            const contractBalance = await token.balanceOf(TOKEN_LOCK_ADDRESS);
            console.log(
                "  Contract Token Balance:",
                ethers.formatUnits(contractBalance, decimals),
                "tokens"
            );

            if (contractBalance < lockDetails.amount) {
                throw new Error(
                    `Insufficient tokens in lock contract. Expected ${ethers.formatUnits(lockDetails.amount, decimals)}, but contract has ${ethers.formatUnits(contractBalance, decimals)}`
                );
            }

            console.log("\n⏳ Withdrawing locked tokens...");
            const withdrawTx = await tokenLock.withdraw(lockId);
            console.log("⏳ Withdraw transaction submitted:", withdrawTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const withdrawReceipt = await withdrawTx.wait();
            console.log(
                "✅ Withdraw transaction confirmed in block:",
                withdrawReceipt?.blockNumber
            );

            // Check for TokensWithdrawn event
            const withdrawEvent = withdrawReceipt?.logs.find((log) => {
                try {
                    const parsed = tokenLock.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "TokensWithdrawn";
                } catch {
                    return false;
                }
            });

            if (withdrawEvent) {
                const parsed = tokenLock.interface.parseLog({
                    topics: withdrawEvent.topics,
                    data: withdrawEvent.data,
                });

                console.log("Tokens Withdrawn Successfully!");
                console.log("  Lock ID:", parsed?.args[0].toString());
                console.log("  Withdrawer:", parsed?.args[1]);
                console.log("  Recipient:", parsed?.args[2]);
                console.log("  Amount:", ethers.formatUnits(parsed?.args[3], decimals), "tokens");
            }

            // Display post-withdrawal status
            console.log("\nPost-withdrawal Status:");

            const newRecipientBalance = await token.balanceOf(lockDetails.recipient);
            console.log(
                "  Recipient New Balance:",
                ethers.formatUnits(newRecipientBalance, decimals),
                "tokens"
            );
            console.log(
                "  Tokens Received:",
                ethers.formatUnits(newRecipientBalance - recipientBalance, decimals),
                "tokens"
            );

            // Verify lock is now closed
            const updatedLockDetails = await tokenLock.getLock(lockId);
            console.log(
                "  Lock Status:",
                updatedLockDetails.closed ? "✅ Closed" : "❌ Still Open"
            );

            // Check if still withdrawable (should be false now)
            const stillWithdrawable = await tokenLock.isWithdrawable(lockId);
            console.log("  Still Withdrawable:", stillWithdrawable ? "❌ Yes (Error)" : "✅ No");

            const newContractBalance = await token.balanceOf(TOKEN_LOCK_ADDRESS);
            console.log(
                "  Contract Remaining Balance:",
                ethers.formatUnits(newContractBalance, decimals),
                "tokens"
            );

            // Show gas cost information
            const gasUsed = withdrawReceipt?.gasUsed || 0n;
            const gasPrice = withdrawReceipt?.gasPrice || 0n;
            const gasCost = gasUsed * gasPrice;
            console.log("\nTransaction Costs:");
            console.log("  Gas Used:", gasUsed.toString());
            console.log("  Gas Price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");
            console.log("  Gas Cost:", ethers.formatEther(gasCost), "HYPE");

            console.log("\n✅ Token lock withdrawal completed successfully!");

            if (withdrawer.address.toLowerCase() !== lockDetails.recipient.toLowerCase()) {
                console.log(
                    `\nNote: Tokens were sent to the recipient (${lockDetails.recipient}), not the withdrawer.`
                );
            }
        } catch (error) {
            console.error("❌ Error withdrawing from lock:", error);
            throw error;
        }
    });

export {};

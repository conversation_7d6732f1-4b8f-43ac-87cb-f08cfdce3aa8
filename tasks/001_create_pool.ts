import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsLaunchpad } from "../types";
import { getLaunchpadAddress, saveTokenToDeployments } from "./utils/deployments";

//Example: npx hardhat create-pool --name "TestToken99" --symbol "TT99" --network hyperevm-testnet
task("create-pool", "Create a new pool on MoonbagsLaunchpad")
    .addParam("name", "The token name")
    .addParam("symbol", "The token symbol")
    .addOptionalParam("description", "Token description", "A token created via MoonbagsLaunchpad")
    .addOptionalParam("metadata", "Token metadata URI", "https://example.com/metadata.json")
    .addOptionalParam("twitter", "Twitter handle", "")
    .addOptionalParam("telegram", "Telegram link", "")
    .addOptionalParam("website", "Website URL", "")
    .addOptionalParam("threshold", "Custom threshold (0 for default)", "0")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [creator] = await ethers.getSigners();

        console.log("🚀 MoonbagsLaunchpad - Create Token");
        console.log("==================================");
        console.log("Creator account:", creator.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenName = taskArgs.name;
        const tokenSymbol = taskArgs.symbol;
        const description = taskArgs.description;
        const tokenMetadata = taskArgs.metadata;
        const twitter = taskArgs.twitter;
        const telegram = taskArgs.telegram;
        const website = taskArgs.website;
        const customThreshold = taskArgs.threshold;

        console.log("\nToken Parameters:");
        console.log("  Name:", tokenName);
        console.log("  Symbol:", tokenSymbol);
        console.log("  Description:", description);
        console.log("  Metadata URI:", tokenMetadata);
        console.log("  Custom Threshold:", customThreshold);

        try {
            const LAUNCHPAD_ADDRESS = getLaunchpadAddress(network.config);

            const launchpad = (await ethers.getContractAt(
                "MoonbagsLaunchpad",
                LAUNCHPAD_ADDRESS
            )) as MoonbagsLaunchpad;

            const config = await launchpad.config();
            console.log(`Configuration: ${config}`);

            // Get the required pool creation fee
            const POOL_CREATION_FEE = await launchpad.POOL_CREATION_FEE();
            console.log("Pool Creation Fee:", ethers.formatEther(POOL_CREATION_FEE), "HYPE");

            // Check creator balance
            const creatorBalance = await ethers.provider.getBalance(creator.address);
            console.log("Creator Balance:", ethers.formatEther(creatorBalance), "HYPE");

            if (creatorBalance < POOL_CREATION_FEE) {
                throw new Error(
                    `Insufficient balance. Need at least ${ethers.formatEther(POOL_CREATION_FEE)} HYPE for pool creation fee.`
                );
            }

            console.log("\n⏳ Creating pool...");
            const tx = await launchpad.createPool(
                tokenName,
                tokenSymbol,
                tokenMetadata,
                description,
                twitter,
                telegram,
                website,
                customThreshold,
                { value: POOL_CREATION_FEE }
            );

            console.log("⏳ Transaction submitted:", tx.hash);
            console.log("⏳ Waiting for confirmation...");

            const receipt = await tx.wait();
            console.log("✅ Transaction confirmed in block:", receipt?.blockNumber);

            const tokenCreatedEvent = receipt?.logs.find((log) => {
                try {
                    const parsed = launchpad.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "TokenCreated";
                } catch {
                    return false;
                }
            });

            if (tokenCreatedEvent) {
                const parsed = launchpad.interface.parseLog({
                    topics: tokenCreatedEvent.topics,
                    data: tokenCreatedEvent.data,
                });
                const tokenAddress = parsed?.args[0];
                const timestamp = parsed?.args[parsed?.args.length - 1];

                console.log("\n🎉 Token Created Successfully!");
                console.log("  Token Address:", tokenAddress);
                console.log("  Creator:", creator.address);
                console.log("  Name:", tokenName);
                console.log("  Symbol:", tokenSymbol);
                console.log("  Timestamp:", new Date(Number(timestamp) * 1000).toISOString());

                if (tokenAddress) {
                    await saveTokenToDeployments(
                        tokenAddress,
                        tokenName,
                        tokenSymbol,
                        creator.address,
                        LAUNCHPAD_ADDRESS,
                        new Date(Number(timestamp) * 1000),
                        { name: network.name, chainId: network.config.chainId }
                    );
                }

                console.log("\n✅ Token creation completed successfully!");
            }
        } catch (error) {
            console.error("❌ Error creating token:", error);
        }
    });

export {};

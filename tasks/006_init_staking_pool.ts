import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { MoonbagsStake } from "../types";

function getStakeAddress(networkConfig?: { chainId?: number }): string {
    const { existsSync, readFileSync } = require("fs");
    const { join } = require("path");

    const deploymentsFile = join(process.cwd(), "deployments", "hardhat_contracts.json");

    if (!existsSync(deploymentsFile)) {
        throw new Error("Deployments file not found. Please deploy the contract first.");
    }

    const deployments = JSON.parse(readFileSync(deploymentsFile, "utf8"));
    const chainId = networkConfig?.chainId?.toString();

    if (!chainId || !deployments[chainId]) {
        throw new Error(`No deployments found for network chain ID: ${chainId}`);
    }

    const networkDeployments = deployments[chainId];
    let stakeAddress = "";

    for (const networkData of networkDeployments) {
        if (networkData.contracts && networkData.contracts.MoonbagsStake) {
            stakeAddress = networkData.contracts.MoonbagsStake.address;
            console.log(`Found MoonbagsStake at: ${stakeAddress} (network: ${networkData.name})`);
            break;
        }
    }

    if (!stakeAddress) {
        throw new Error("MoonbagsStake contract not found in deployments. Please deploy it first.");
    }

    return stakeAddress;
}

// Example: npx hardhat init-staking-pool --token 0xYourTokenAddress --network hyperevm-testnet
task("init-staking-pool", "Initialize staking pool for a token")
    .addParam("token", "The token address to initialize staking pool for")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [signer] = await ethers.getSigners();

        console.log("MoonbagsStake - Initialize Staking Pool");
        console.log("==========================================");
        console.log("Signer account:", signer.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);

        // Validate token address
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        try {
            const STAKE_ADDRESS = getStakeAddress(network.config);

            const moonbagsStake = (await ethers.getContractAt(
                "MoonbagsStake",
                STAKE_ADDRESS
            )) as MoonbagsStake;

            console.log("\nCurrent Pool Status:");

            // Check if staking pool already exists
            const stakingPoolExists = await moonbagsStake.stakingPoolExists(tokenAddress);
            console.log("  Staking Pool Exists:", stakingPoolExists);

            let stakingPoolTx;

            // Initialize staking pool
            if (!stakingPoolExists) {
                console.log("\n⏳ Initializing staking pool...");
                stakingPoolTx = await moonbagsStake.initializeStakingPool(tokenAddress);
                console.log("⏳ Staking pool transaction submitted:", stakingPoolTx.hash);
                console.log("⏳ Waiting for confirmation...");

                const stakingReceipt = await stakingPoolTx.wait();
                console.log("✅ Staking pool initialized in block:", stakingReceipt?.blockNumber);

                // Check for InitializeStakingPoolEvent
                const stakingEvent = stakingReceipt?.logs.find((log) => {
                    try {
                        const parsed = moonbagsStake.interface.parseLog({
                            topics: log.topics,
                            data: log.data,
                        });
                        return parsed?.name === "InitializeStakingPoolEvent";
                    } catch {
                        return false;
                    }
                });

                if (stakingEvent) {
                    const parsed = moonbagsStake.interface.parseLog({
                        topics: stakingEvent.topics,
                        data: stakingEvent.data,
                    });
                    console.log("Staking Pool Initialized Successfully!");
                    console.log("  Token:", parsed?.args[0]);
                    console.log("  Initializer:", parsed?.args[1]);
                    console.log(
                        "  Timestamp:",
                        new Date(Number(parsed?.args[2]) * 1000).toISOString()
                    );
                }
            } else {
                console.log("ℹStaking pool already exists, skipping initialization");
            }

            // Display final status
            console.log("\nFinal Pool Status:");
            const finalStakingExists = await moonbagsStake.stakingPoolExists(tokenAddress);

            console.log(
                "  Staking Pool:",
                finalStakingExists ? "✅ Initialized" : "❌ Not initialized"
            );

            if (finalStakingExists) {
                const poolInfo = await moonbagsStake.getStakingPoolInfo(tokenAddress);
                console.log("\nStaking Pool Info:");
                console.log("  Total Supply:", ethers.formatEther(poolInfo.totalSupply), "tokens");
                console.log("  Reward Index:", poolInfo.rewardIndex.toString());
                console.log(
                    "  Pending Initial Rewards:",
                    ethers.formatEther(poolInfo.pendingInitialRewards),
                    "HYPE"
                );
                console.log("  Total Rewards:", ethers.formatEther(poolInfo.totalRewards), "HYPE");
            }

            console.log("\n✅ Staking pool initialization completed successfully!");
        } catch (error) {
            console.error("❌ Error initializing staking pool:", error);
        }
    });

export {};

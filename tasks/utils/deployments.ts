import { existsSync, readFileSync, writeFileSync } from "fs";
import { join } from "path";

export function getLaunchpadAddress(networkConfig?: { chainId?: number }): string {
    const deploymentsFile = join(process.cwd(), "deployments", "hardhat_contracts.json");

    if (!existsSync(deploymentsFile)) {
        throw new Error("Deployments file not found. Please deploy the contract first.");
    }

    const deployments = JSON.parse(readFileSync(deploymentsFile, "utf8"));
    const chainId = networkConfig?.chainId?.toString();

    if (!chainId || !deployments[chainId]) {
        throw new Error(`No deployments found for network chain ID: ${chainId}`);
    }

    const networkDeployments = deployments[chainId];
    let launchpadAddress = "";

    for (const networkData of networkDeployments) {
        if (networkData.contracts && networkData.contracts.MoonbagsLaunchpad) {
            launchpadAddress = networkData.contracts.MoonbagsLaunchpad.address;
            console.log(
                `Found MoonbagsLaunchpad at: ${launchpadAddress} (network: ${networkData.name})`
            );
            break;
        }
    }

    if (!launchpadAddress) {
        throw new Error(
            "MoonbagsLaunchpad contract not found in deployments. Please deploy it first."
        );
    }

    return launchpadAddress;
}

export function getNetworkInfo(networkConfig: { name: string; chainId?: number }) {
    return {
        name: networkConfig.name,
        chainId: networkConfig.chainId,
    };
}

export async function saveTokenToDeployments(
    tokenAddress: string,
    name: string,
    symbol: string,
    creator: string,
    launchpadAddress: string,
    createdAt: Date,
    networkConfig: { name: string; chainId?: number },
    purchaseInfo?: any
) {
    try {
        const deploymentsDir = join(process.cwd(), "deployments");
        const tokensFile = join(deploymentsDir, "created_tokens.json");

        let tokens: any[] = [];
        if (existsSync(tokensFile)) {
            const content = readFileSync(tokensFile, "utf8");
            tokens = JSON.parse(content);
        }

        const newToken = {
            address: tokenAddress,
            name,
            symbol,
            creator,
            createdAt,
            network: networkConfig.name,
            chainId: networkConfig.chainId,
            launchpadAddress: launchpadAddress,
            ...purchaseInfo,
        };

        tokens.push(newToken);

        writeFileSync(tokensFile, JSON.stringify(tokens, null, 2));
        console.log("\nToken saved to deployments/created_tokens.json");
        console.log("  File contains", tokens.length, "tokens");
    } catch (error) {
        console.error("⚠️  Failed to save token to deployments file:", error);
    }
}

export function getStakeAddress(networkConfig?: { chainId?: number }): string {
    const deploymentsFile = join(process.cwd(), "deployments", "hardhat_contracts.json");

    if (!existsSync(deploymentsFile)) {
        throw new Error("Deployments file not found. Please deploy the contract first.");
    }

    const deployments = JSON.parse(readFileSync(deploymentsFile, "utf8"));
    const chainId = networkConfig?.chainId?.toString();

    if (!chainId || !deployments[chainId]) {
        throw new Error(`No deployments found for network chain ID: ${chainId}`);
    }

    const networkDeployments = deployments[chainId];
    let stakeAddress = "";

    for (const networkData of networkDeployments) {
        if (networkData.contracts && networkData.contracts.MoonbagsStake) {
            stakeAddress = networkData.contracts.MoonbagsStake.address;
            console.log(`Found MoonbagsStake at: ${stakeAddress} (network: ${networkData.name})`);
            break;
        }
    }

    if (!stakeAddress) {
        throw new Error("MoonbagsStake contract not found in deployments. Please deploy it first.");
    }

    return stakeAddress;
}

export function getTokenLockAddress(networkConfig?: { chainId?: number }): string {
    const deploymentsFile = join(process.cwd(), "deployments", "hardhat_contracts.json");

    if (!existsSync(deploymentsFile)) {
        throw new Error("Deployments file not found. Please deploy the contract first.");
    }

    const deployments = JSON.parse(readFileSync(deploymentsFile, "utf8"));
    const chainId = networkConfig?.chainId?.toString();

    if (!chainId || !deployments[chainId]) {
        throw new Error(`No deployments found for network chain ID: ${chainId}`);
    }

    const networkDeployments = deployments[chainId];
    let tokenLockAddress = "";

    for (const networkData of networkDeployments) {
        if (networkData.contracts && networkData.contracts.TokenLock) {
            tokenLockAddress = networkData.contracts.TokenLock.address;
            console.log(`Found TokenLock at: ${tokenLockAddress} (network: ${networkData.name})`);
            break;
        }
    }

    if (!tokenLockAddress) {
        throw new Error("TokenLock contract not found in deployments. Please deploy it first.");
    }

    return tokenLockAddress;
}

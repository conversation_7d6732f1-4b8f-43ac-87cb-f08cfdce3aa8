import { task } from "hardhat/config";
import { HardhatRuntimeEnvironment } from "hardhat/types";

import { IERC20Metadata, TokenLock } from "../types";
import { getTokenLockAddress } from "./utils/deployments";

task("create-lock", "Create a time-locked token contract")
    .addParam("token", "The token address to lock")
    .addParam("amount", "Amount of tokens to lock (in token units)")
    .addParam("duration", "Lock duration in hours")
    .addParam("recipient", "Address that will be able to claim the tokens after the lock period")
    .setAction(async (taskArgs, hre: HardhatRuntimeEnvironment) => {
        const { ethers, network } = hre;
        const [locker] = await ethers.getSigners();

        console.log("TokenLock - Create Lock");
        console.log("=========================");
        console.log("Locker account:", locker.address);
        console.log("Network:", network.name, "Chain ID:", network.config.chainId);

        const tokenAddress = taskArgs.token;
        const lockAmount = taskArgs.amount;
        const lockDurationHours = taskArgs.duration;
        const recipientAddress = taskArgs.recipient;

        console.log("\nParameters:");
        console.log("  Token Address:", tokenAddress);
        console.log("  Lock Amount:", lockAmount, "tokens");
        console.log("  Lock Duration:", lockDurationHours, "hours");
        console.log("  Recipient Address:", recipientAddress);

        // Validate inputs
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("Invalid token address provided");
        }

        if (!ethers.isAddress(recipientAddress)) {
            throw new Error("Invalid recipient address provided");
        }

        if (!lockAmount || parseFloat(lockAmount) <= 0) {
            throw new Error("Invalid lock amount provided");
        }

        if (!lockDurationHours || parseFloat(lockDurationHours) < 1) {
            throw new Error("Invalid lock duration provided. Minimum is 1 hour.");
        }

        try {
            const TOKEN_LOCK_ADDRESS = getTokenLockAddress(network.config);

            const tokenLock = (await ethers.getContractAt(
                "TokenLock",
                TOKEN_LOCK_ADDRESS
            )) as TokenLock;

            const token = (await ethers.getContractAt(
                "IERC20Metadata",
                tokenAddress
            )) as IERC20Metadata;

            // Get token decimals and convert amount
            const decimals = await token.decimals();
            const lockAmountWei = ethers.parseUnits(lockAmount, decimals);

            // Calculate end time
            const lockDurationSeconds = Math.floor(parseFloat(lockDurationHours) * 3600);
            const currentTime = Math.floor(Date.now() / 1000);
            const endTime = currentTime + lockDurationSeconds;

            console.log("\nPre-lock Status:");

            // Check token balance
            const tokenBalance = await token.balanceOf(locker.address);
            console.log("  Token Balance:", ethers.formatUnits(tokenBalance, decimals), "tokens");

            if (tokenBalance < lockAmountWei) {
                throw new Error("Insufficient token balance for locking");
            }

            // Get lock configuration
            const config = await tokenLock.config();
            console.log("  Lock Fee:", (Number(config.lockFee) / 100).toFixed(2) + "%");
            console.log("  Lock Admin:", config.admin);

            // Calculate fee
            const FEE_DENOMINATOR = 10000n;
            const fee = (lockAmountWei * config.lockFee) / FEE_DENOMINATOR;
            const totalRequired = lockAmountWei + fee;

            console.log("  Lock Fee Amount:", ethers.formatUnits(fee, decimals), "tokens");
            console.log("  Total Required:", ethers.formatUnits(totalRequired, decimals), "tokens");

            if (tokenBalance < totalRequired) {
                throw new Error(
                    `Insufficient token balance. Need ${ethers.formatUnits(totalRequired, decimals)} tokens (including fee)`
                );
            }

            // Check allowance
            const allowance = await token.allowance(locker.address, TOKEN_LOCK_ADDRESS);
            console.log("  Current Allowance:", ethers.formatUnits(allowance, decimals), "tokens");

            // Transfer tokens to TokenLock contract first
            if (allowance < totalRequired) {
                console.log("\n⏳ Approving tokens for lock contract...");
                const approveTx = await token.approve(TOKEN_LOCK_ADDRESS, totalRequired);
                console.log("⏳ Approval transaction submitted:", approveTx.hash);
                console.log("⏳ Waiting for approval confirmation...");

                const approveReceipt = await approveTx.wait();
                console.log("✅ Approval confirmed in block:", approveReceipt?.blockNumber);
            }

            console.log("\n⏳ Transferring tokens to lock contract...");
            const transferTx = await token.transfer(TOKEN_LOCK_ADDRESS, totalRequired);
            console.log("⏳ Transfer transaction submitted:", transferTx.hash);
            console.log("⏳ Waiting for transfer confirmation...");

            const transferReceipt = await transferTx.wait();
            console.log("✅ Transfer confirmed in block:", transferReceipt?.blockNumber);

            // Get current lock count for reference
            const currentLockCount = await tokenLock.nextLockId();
            console.log("  Next Lock ID:", currentLockCount.toString());

            console.log("\nLock Schedule:");
            console.log("  Start Time:", new Date(currentTime * 1000).toISOString());
            console.log("  End Time:", new Date(endTime * 1000).toISOString());
            console.log("  Duration:", lockDurationHours, "hours");

            console.log("\n⏳ Creating lock...");
            const createLockTx = await tokenLock.createLock(
                tokenAddress,
                lockAmountWei,
                endTime,
                recipientAddress
            );
            console.log("⏳ Create lock transaction submitted:", createLockTx.hash);
            console.log("⏳ Waiting for confirmation...");

            const createLockReceipt = await createLockTx.wait();
            console.log(
                "✅ Create lock transaction confirmed in block:",
                createLockReceipt?.blockNumber
            );

            // Check for LockCreated event
            const lockEvent = createLockReceipt?.logs.find((log) => {
                try {
                    const parsed = tokenLock.interface.parseLog({
                        topics: log.topics,
                        data: log.data,
                    });
                    return parsed?.name === "LockCreated";
                } catch {
                    return false;
                }
            });

            let lockId = currentLockCount;

            if (lockEvent) {
                const parsed = tokenLock.interface.parseLog({
                    topics: lockEvent.topics,
                    data: lockEvent.data,
                });
                lockId = parsed?.args[0];

                console.log("Lock Created Successfully!");
                console.log("  Lock ID:", lockId.toString());
                console.log("  Locker:", parsed?.args[1]);
                console.log("  Recipient:", parsed?.args[2]);
                console.log("  Token Address:", parsed?.args[3]);
                console.log("  Amount:", ethers.formatUnits(parsed?.args[4], decimals), "tokens");
                console.log("  Fee:", ethers.formatUnits(parsed?.args[5], decimals), "tokens");
                console.log(
                    "  Start Time:",
                    new Date(Number(parsed?.args[6]) * 1000).toISOString()
                );
                console.log("  End Time:", new Date(Number(parsed?.args[7]) * 1000).toISOString());
            }

            // Display post-lock status
            console.log("\nPost-lock Status:");

            const newTokenBalance = await token.balanceOf(locker.address);
            console.log(
                "  New Token Balance:",
                ethers.formatUnits(newTokenBalance, decimals),
                "tokens"
            );
            console.log(
                "  Tokens Locked:",
                ethers.formatUnits(tokenBalance - newTokenBalance, decimals),
                "tokens"
            );

            // Get lock details
            const lockDetails = await tokenLock.getLock(lockId);
            console.log("\nLock Details:");
            console.log("  Lock ID:", lockId.toString());
            console.log("  Token:", lockDetails.token);
            console.log("  Amount:", ethers.formatUnits(lockDetails.amount, decimals), "tokens");
            console.log("  Locker:", lockDetails.locker);
            console.log("  Recipient:", lockDetails.recipient);
            console.log(
                "  Start Time:",
                new Date(Number(lockDetails.startTime) * 1000).toISOString()
            );
            console.log("  End Time:", new Date(Number(lockDetails.endTime) * 1000).toISOString());
            console.log("  Closed:", lockDetails.closed);

            // Check if withdrawable
            const isWithdrawable = await tokenLock.isWithdrawable(lockId);
            console.log("  Currently Withdrawable:", isWithdrawable);

            // Show gas cost information
            const gasUsed = createLockReceipt?.gasUsed || 0n;
            const gasPrice = createLockReceipt?.gasPrice || 0n;
            const gasCost = gasUsed * gasPrice;
            console.log("\nTransaction Costs:");
            console.log("  Gas Used:", gasUsed.toString());
            console.log("  Gas Price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");
            console.log("  Gas Cost:", ethers.formatEther(gasCost), "HYPE");

            console.log("\n✅ Token lock creation completed successfully!");
            console.log(
                `\nTo withdraw the locked tokens after ${new Date(endTime * 1000).toISOString()}, use:`
            );
            console.log(
                `    npx hardhat withdraw-lock --lockid ${lockId} --network ${network.name}`
            );
        } catch (error) {
            console.error("❌ Error creating lock:", error);
            throw error;
        }
    });

export {};

# Full reference https://github.com/foundry-rs/foundry/tree/master/config

[profile.default]
  auto_detect_solc = false
  bytecode_hash = "none"
  cbor_metadata = false
  evm_version = "paris"      # See https://www.evmdiff.com/features?name=PUSH0&kind=opcode
  gas_reports = ["*"]
  libs = ["node_modules"]
  optimizer = true
  optimizer_runs = 200
  out = "out"
  script = "scripts/foundry"
  solc = "0.8.23"
  src = "contracts"
  test = "test/foundry"
  via_ir = true

  [profile.default.fuzz]
    max_test_rejects = 1_000_000 # Number of times `vm.assume` can fail
    runs = 1_000

  [profile.default.invariant]
    call_override = false # Override unsafe external calls to perform reentrancy checks
    depth = 15            # Number of calls executed in one run
    fail_on_revert = true
    runs = 256

# Speed up compilation and tests during development
[profile.lite]
  optimizer = false

[profile.ci]
  fuzz = { runs = 10_000 }
  verbosity = 4

[doc]
  ignore = ["**/*.t.sol"]
  out = "docs"

[etherscan]
  # ETHEREUM
  goerli = { key = "${ETHERSCAN_API_KEY}" }
  mainnet = { key = "${ETHERSCAN_API_KEY}" }
  sepolia = { key = "${ETHERSCAN_API_KEY}" }

  # BINANCE SMART CHAIN
  bnb_smart_chain = { key = "${BSCSCAN_API_KEY}" }
  bnb_smart_chain_testnet = { key = "${BSCSCAN_API_KEY}" }

  # MATIC/POLYGON
  polygon = { key = "${POLYGONSCAN_API_KEY}" }
  polygon_mumbai = { key = "${POLYGONSCAN_API_KEY}" }

  # OPTIMISM
  optimism = { key = "${OPTIMISM_API_KEY}" }
  optimism_goerli = { key = "${OPTIMISM_API_KEY}" }

  # ARBITRUM
  arbitrum_nova = { key = "${ARBISCAN_API_KEY}" }
  arbitrum_one = { key = "${ARBISCAN_API_KEY}" }
  arbitrum_one_goerli = { key = "${ARBISCAN_API_KEY}" }

  # AVALANCHE
  avalanche = { key = "${SNOWTRACE_API_KEY}" }
  avalanche_fuji = { key = "${SNOWTRACE_API_KEY}" }

  # GNOSIS CHAIN
  gnosis_chain = { key = "${GNOSISSCAN_API_KEY}" }

[fmt]
  bracket_spacing = true
  int_types = "long"
  line_length = 100
  multiline_func_header = "all"
  number_underscore = "thousands"
  quote_style = "double"
  tab_width = 4
  wrap_comments = true

[rpc_endpoints]
  # LOCALHOST
  localhost = "http://localhost:8545"

  # ETHEREUM
  goerli = "https://goerli.infura.io/v3/${INFURA_API_KEY}"
  mainnet = "https://eth-mainnet.g.alchemy.com/v2/${ALCHEMY_API_KEY}"
  sepolia = "https://sepolia.infura.io/v3/${INFURA_API_KEY}"

  # BINANCE SMART CHAIN
  bnb_smart_chain = "https://bsc-dataseed.binance.org"
  bnb_smart_chain_testnet = "https://rpc.ankr.com/bsc_testnet_chapel"

  # MATIC/POLYGON
  polygon = "https://polygon-mainnet.infura.io/v3/${INFURA_API_KEY}"
  polygon_mumbai = "https://polygon-mumbai.infura.io/v3/${INFURA_API_KEY}"

  # OPTIMISM
  optimism = "https://optimism-mainnet.infura.io/v3/${INFURA_API_KEY}"
  optimism_goerli = "https://optimism-goerli.infura.io/v3/${INFURA_API_KEY}"

  # ARBITRUM
  arbitrum_nova = ""
  arbitrum_one = "https://arbitrum-mainnet.infura.io/v3/${INFURA_API_KEY}"
  arbitrum_one_goerli = "https://arbitrum-goerli.infura.io/v3/${INFURA_API_KEY}"

  # AVALANCHE
  avalanche = "https://avalanche-mainnet.infura.io/v3/${INFURA_API_KEY}"
  avalanche_fuji = "https://avalanche-fuji.infura.io/v3/${INFURA_API_KEY}"

  # GNOSIS CHAIN
  gnosis_chain = "https://rpc.gnosischain.com"

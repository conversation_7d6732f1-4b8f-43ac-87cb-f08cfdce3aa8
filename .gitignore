# Ignore all Visual Studio Code settings
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local history for Visual Studio Code
.history

# Dependency directory
node_modules

# dotenv environment variable files
*.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Optional npm cache directory
.npm

# Modern Yarn files
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Hardhat files
flattened
build
artifacts
cache
.deps
types
cache_hardhat
dodoc

# Foundry files
foundry_cache
out
docs
!broadcast
broadcast/*
broadcast/*/31337/

# solidity-coverage directory and output file
coverage
coverage.json
.coverage_artifacts
.coverage_cache
.coverage_contracts

# Ignore flattened files
flattened.sol

# Miscellaneous
lcov.info
package-lock.json
.DS_Store
.vscode/settings.json

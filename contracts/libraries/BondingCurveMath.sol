// SPDX-License-Identifier: MIT
pragma solidity ^0.8.23;

/**
 * @title BondingCurveMath
 * @notice Mathematical functions for bonding curve calculations
 * @dev Implements constant product formula: x * y = k
 */
library BondingCurveMath {
    error InsufficientReserves();
    error InvalidInput();
    error MathOverflow();

    /**
     * @notice Calculate the cost in base currency to get exact amount of tokens
     * @param baseReserves Current base currency reserves
     * @param tokenReserves Current token reserves
     * @param tokenAmountOut Exact amount of tokens desired
     * @return baseCost Amount of base currency needed
     */
    function calculateBaseCostForExactTokens(
        uint256 baseReserves,
        uint256 tokenReserves,
        uint256 tokenAmountOut
    )
        internal
        pure
        returns (uint256 baseCost)
    {
        if (tokenAmountOut == 0) return 0;
        if (tokenAmountOut >= tokenReserves) revert InsufficientReserves();

        uint256 remainingTokenReserves = tokenReserves - tokenAmountOut;
        uint256 newBaseReserves = (baseReserves * tokenReserves) / remainingTokenReserves;

        if (newBaseReserves <= baseReserves) revert InvalidInput();

        baseCost = newBaseReserves - baseReserves;
    }

    /**
     * @notice Calculate tokens received for exact base currency input
     * @param baseReserves Current base currency reserves
     * @param tokenReserves Current token reserves
     * @param baseAmountIn Exact amount of base currency to spend
     * @return tokenAmountOut Amount of tokens that will be received
     */
    function calculateTokensForExactBase(
        uint256 baseReserves,
        uint256 tokenReserves,
        uint256 baseAmountIn
    )
        internal
        pure
        returns (uint256 tokenAmountOut)
    {
        if (baseAmountIn == 0) return 0;

        uint256 newBaseReserves = baseReserves + baseAmountIn;
        uint256 newTokenReserves = (baseReserves * tokenReserves) / newBaseReserves;

        if (newTokenReserves >= tokenReserves) revert InvalidInput();

        tokenAmountOut = tokenReserves - newTokenReserves;
    }

    /**
     * @notice Calculate base currency received for exact token input (selling)
     * @param baseReserves Current base currency reserves
     * @param tokenReserves Current token reserves
     * @param tokenAmountIn Exact amount of tokens to sell
     * @return baseAmountOut Amount of base currency that will be received
     */
    function calculateBaseForExactTokens(
        uint256 baseReserves,
        uint256 tokenReserves,
        uint256 tokenAmountIn
    )
        internal
        pure
        returns (uint256 baseAmountOut)
    {
        if (tokenAmountIn == 0) return 0;

        uint256 newTokenReserves = tokenReserves + tokenAmountIn;
        uint256 newBaseReserves = (baseReserves * tokenReserves) / newTokenReserves;

        if (newBaseReserves >= baseReserves) revert InvalidInput();

        baseAmountOut = baseReserves - newBaseReserves;
    }
}

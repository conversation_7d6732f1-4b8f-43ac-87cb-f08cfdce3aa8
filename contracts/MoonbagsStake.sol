// SPDX-License-Identifier: MIT
pragma solidity ^0.8.23;

import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";

/**
 * @title Moonbags Staking
 */
contract MoonbagsStake is Initializable, ReentrancyGuardUpgradeable, OwnableUpgradeable {
    using SafeERC20 for IERC20;
    using Math for uint256;

    // === Constants ===
    uint256 private constant MULTIPLIER = 1e18;
    uint256 public constant DEFAULT_DENY_UNSTAKE_DURATION = 1 hours;

    // === Structs ===
    struct Configuration {
        address admin;
        uint256 denyUnstakeDuration; // Duration in seconds users must wait before unstaking
    }

    struct StakingPool {
        address initializer;
        uint256 totalSupply;
        uint256 rewardIndex;
        uint256 pendingInitialRewards;
        uint256 totalRewards; // Total HYPE rewards in pool
    }

    struct CreatorPool {
        address initializer;
        address creator;
        uint256 totalRewards; // Total HYPE rewards for creator
    }

    struct StakingAccount {
        address staker;
        uint256 balance;
        uint256 rewardIndex;
        uint256 earned;
        uint256 unstakeDeadline;
    }

    // === State Variables ===
    Configuration public config;

    // Mapping from token address to staking pool
    mapping(address => StakingPool) public stakingPools;

    // Mapping from token address to creator pool
    mapping(address => CreatorPool) public creatorPools;

    // Mapping from token address to user address to staking account
    mapping(address => mapping(address => StakingAccount)) public stakingAccounts;

    // === Events ===
    event InitializeStakingPoolEvent(
        address indexed tokenAddress, address indexed initializer, uint256 timestamp
    );

    event InitializeCreatorPoolEvent(
        address indexed tokenAddress,
        address indexed initializer,
        address indexed creator,
        uint256 timestamp
    );

    event StakeEvent(
        address indexed tokenAddress, address indexed staker, uint256 amount, uint256 timestamp
    );

    event UnstakeEvent(
        address indexed tokenAddress,
        address indexed unstaker,
        uint256 amount,
        bool isStakingAccountDeleted,
        uint256 timestamp
    );

    event UpdateRewardIndexEvent(
        address indexed tokenAddress,
        address indexed rewardUpdater,
        uint256 reward,
        bool isInitialRewards,
        uint256 timestamp
    );

    event DepositPoolCreatorEvent(
        address indexed tokenAddress, address indexed depositor, uint256 amount, uint256 timestamp
    );

    event ClaimStakingPoolEvent(
        address indexed tokenAddress,
        address indexed claimer,
        uint256 reward,
        bool isStakingAccountDeleted,
        uint256 timestamp
    );

    event ClaimCreatorPoolEvent(
        address indexed tokenAddress, address indexed claimer, uint256 reward, uint256 timestamp
    );

    // === Custom Errors ===
    error StakingPoolNotExist();
    error StakingCreatorNotExist();
    error StakingAccountNotExist();
    error AccountBalanceNotEnough();
    error InvalidCreator();
    error InvalidAmount();
    error RewardToClaimNotValid();
    error UnstakeDeadlineNotAllow();
    error NotUpgrade();
    error ZeroAddress();
    error StakingPoolAlreadyExists();
    error CreatorPoolAlreadyExists();
    error InvalidTokenAddress();
    error OnlyAdmin();

    // === Modifiers ===
    modifier onlyAdmin() {
        if (msg.sender != config.admin) revert OnlyAdmin();
        _;
    }

    // === Initialize Function ===
    /**
     * @notice Initialize the contract
     */
    function initialize() public initializer {
        __ReentrancyGuard_init();
        __Ownable_init(msg.sender);

        config =
            Configuration({ admin: msg.sender, denyUnstakeDuration: DEFAULT_DENY_UNSTAKE_DURATION });
    }

    // === Public Functions ===

    /**
     * @notice Initializes a new staking pool for a specific token
     * @param stakingToken The ERC20 token that will be staked in this pool
     */
    function initializeStakingPool(address stakingToken) external {
        if (stakingToken == address(0)) revert InvalidTokenAddress();

        if (stakingPoolExists(stakingToken)) {
            return;
        }

        stakingPools[stakingToken] = StakingPool({
            initializer: msg.sender,
            totalSupply: 0,
            rewardIndex: 0,
            pendingInitialRewards: 0,
            totalRewards: 0
        });

        emit InitializeStakingPoolEvent(stakingToken, msg.sender, block.timestamp);
    }

    /**
     * @notice Initializes a creator pool for a specific token
     * @param stakingToken The ERC20 token associated with this creator pool
     * @param creator Address of the creator for this pool
     */
    function initializeCreatorPool(address stakingToken, address creator) external {
        if (stakingToken == address(0)) revert InvalidTokenAddress();
        if (creator == address(0)) revert ZeroAddress();

        if (creatorPoolExists(stakingToken)) {
            return;
        }

        creatorPools[stakingToken] =
            CreatorPool({ initializer: msg.sender, creator: creator, totalRewards: 0 });

        emit InitializeCreatorPoolEvent(stakingToken, msg.sender, creator, block.timestamp);
    }

    /**
     * @notice Updates the reward index of a staking pool by adding new rewards
     * @param stakingToken The token associated with the staking pool
     */
    function updateRewardIndex(address stakingToken) external payable {
        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();
        if (msg.value == 0) revert InvalidAmount();

        StakingPool storage pool = stakingPools[stakingToken];
        uint256 rewardAmount = msg.value;

        // No stakers - add to pending initial rewards
        if (pool.totalSupply == 0) {
            pool.pendingInitialRewards += rewardAmount;
            pool.totalRewards += rewardAmount;

            emit UpdateRewardIndexEvent(
                stakingToken,
                msg.sender,
                rewardAmount,
                true, // is initial rewards
                block.timestamp
            );
            return;
        }

        // Update reward index
        pool.rewardIndex += (rewardAmount * MULTIPLIER) / pool.totalSupply;
        pool.totalRewards += rewardAmount;

        emit UpdateRewardIndexEvent(
            stakingToken,
            msg.sender,
            rewardAmount,
            false, // not initial rewards
            block.timestamp
        );
    }

    /**
     * @notice Deposits HYPE rewards into a creator pool
     * @param stakingToken The token associated with the creator pool
     */
    function depositCreatorPool(address stakingToken) external payable {
        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();
        if (msg.value == 0) revert InvalidAmount();

        CreatorPool storage pool = creatorPools[stakingToken];
        pool.totalRewards += msg.value;

        emit DepositPoolCreatorEvent(stakingToken, msg.sender, msg.value, block.timestamp);
    }

    /**
     * @notice Stakes tokens in a staking pool
     * @param stakingToken The token to stake
     * @param amount Amount of tokens to stake
     */
    function stake(address stakingToken, uint256 amount) external nonReentrant {
        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();
        if (amount == 0) revert InvalidAmount();

        StakingPool storage pool = stakingPools[stakingToken];

        IERC20(stakingToken).safeTransferFrom(msg.sender, address(this), amount);

        // Initialize staking account if first time staking
        if (!stakingAccountExists(stakingToken, msg.sender)) {
            stakingAccounts[stakingToken][msg.sender] = StakingAccount({
                staker: msg.sender,
                balance: 0,
                rewardIndex: 0,
                earned: pool.pendingInitialRewards,
                unstakeDeadline: 0
            });

            // Reset pending initial rewards since first staker gets them
            pool.pendingInitialRewards = 0;
        }

        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];

        // Update rewards before staking
        _updateRewards(pool.rewardIndex, account);

        // Update staking account
        uint256 currentTime = block.timestamp;
        account.unstakeDeadline = currentTime + config.denyUnstakeDuration;
        account.balance += amount;
        pool.totalSupply += amount;

        emit StakeEvent(stakingToken, msg.sender, amount, currentTime);
    }

    /**
     * @notice Unstakes tokens from a staking pool
     * @param stakingToken The token to unstake
     * @param unstakeAmount Amount of tokens to unstake
     */
    function unstake(address stakingToken, uint256 unstakeAmount) external nonReentrant {
        if (unstakeAmount == 0) revert InvalidAmount();
        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();
        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();

        StakingPool storage pool = stakingPools[stakingToken];
        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];

        uint256 currentTime = block.timestamp;
        if (currentTime < account.unstakeDeadline) revert UnstakeDeadlineNotAllow();

        // Update rewards before unstaking
        _updateRewards(pool.rewardIndex, account);

        if (account.balance < unstakeAmount) revert AccountBalanceNotEnough();

        // Update balances
        account.balance -= unstakeAmount;
        pool.totalSupply -= unstakeAmount;

        IERC20(stakingToken).safeTransfer(msg.sender, unstakeAmount);

        bool isAccountDeleted = _tryCleanupEmptyAccount(stakingToken, msg.sender);

        emit UnstakeEvent(stakingToken, msg.sender, unstakeAmount, isAccountDeleted, currentTime);
    }

    /**
     * @notice Claims rewards from a staking pool
     * @param stakingToken The token associated with the staking pool
     * @return rewardAmount The amount of HYPE claimed as rewards
     */
    function claimStakingPool(address stakingToken)
        external
        nonReentrant
        returns (uint256 rewardAmount)
    {
        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();
        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();

        StakingPool storage pool = stakingPools[stakingToken];
        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];

        // Update rewards before claiming
        _updateRewards(pool.rewardIndex, account);

        rewardAmount = account.earned;
        if (rewardAmount == 0) revert RewardToClaimNotValid();

        account.earned = 0;
        pool.totalRewards -= rewardAmount;

        payable(msg.sender).transfer(rewardAmount);

        bool isAccountDeleted = _tryCleanupEmptyAccount(stakingToken, msg.sender);

        emit ClaimStakingPoolEvent(
            stakingToken, msg.sender, rewardAmount, isAccountDeleted, block.timestamp
        );

        return rewardAmount;
    }

    /**
     * @notice Claims rewards from a creator pool
     * @param stakingToken The token associated with the creator pool
     * @return rewardAmount The amount of HYPE claimed from the creator pool
     */
    function claimCreatorPool(address stakingToken)
        external
        nonReentrant
        returns (uint256 rewardAmount)
    {
        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();

        CreatorPool storage pool = creatorPools[stakingToken];
        if (pool.creator != msg.sender) revert InvalidCreator();

        rewardAmount = pool.totalRewards;
        if (rewardAmount == 0) revert RewardToClaimNotValid();

        pool.totalRewards = 0;

        payable(msg.sender).transfer(rewardAmount);

        emit ClaimCreatorPoolEvent(stakingToken, msg.sender, rewardAmount, block.timestamp);

        return rewardAmount;
    }

    // === View Functions ===

    /**
     * @notice Check if a staking account exists for a user
     * @param token The token address
     * @param user The user address
     * @return exists True if the staking account exists
     */
    function stakingAccountExists(address token, address user) public view returns (bool exists) {
        return stakingAccounts[token][user].staker != address(0);
    }

    /**
     * @notice Check if a staking pool exists for a token
     * @param token The token address
     * @return exists True if the staking pool exists
     */
    function stakingPoolExists(address token) public view returns (bool exists) {
        return stakingPools[token].initializer != address(0);
    }

    /**
     * @notice Check if a creator pool exists for a token
     * @param token The token address
     * @return exists True if the creator pool exists
     */
    function creatorPoolExists(address token) public view returns (bool exists) {
        return creatorPools[token].initializer != address(0);
    }

    /**
     * @notice Calculates the rewards earned by the caller for staking tokens
     * @param stakingToken The token associated with the staking pool
     * @return totalEarned The total amount of rewards earned
     */
    function calculateRewardsEarned(address stakingToken)
        external
        view
        returns (uint256 totalEarned)
    {
        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();
        if (!stakingAccountExists(stakingToken, msg.sender)) revert StakingAccountNotExist();

        StakingPool storage pool = stakingPools[stakingToken];
        StakingAccount storage account = stakingAccounts[stakingToken][msg.sender];

        uint256 pendingRewards = _calculateRewards(pool.rewardIndex, account);
        return account.earned + pendingRewards;
    }

    /**
     * @notice Calculates the rewards earned by a specific user for staking tokens
     * @param stakingToken The token associated with the staking pool
     * @param userAddress The address of the user to calculate rewards for
     * @return totalEarned The total amount of rewards earned
     */
    function calculateRewardsEarnedForUser(address stakingToken, address userAddress)
        external
        view
        returns (uint256 totalEarned)
    {
        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();
        if (!stakingAccountExists(stakingToken, userAddress)) revert StakingAccountNotExist();

        StakingPool storage pool = stakingPools[stakingToken];
        StakingAccount storage account = stakingAccounts[stakingToken][userAddress];

        uint256 pendingRewards = _calculateRewards(pool.rewardIndex, account);
        return account.earned + pendingRewards;
    }

    /**
     * @notice Get staking pool information
     * @param stakingToken The token associated with the staking pool
     * @return initializer Address that initialized the pool
     * @return totalSupply Total amount of tokens staked
     * @return rewardIndex Current reward index
     * @return pendingInitialRewards Pending initial rewards
     * @return totalRewards Total HYPE rewards in pool
     */
    function getStakingPoolInfo(address stakingToken)
        external
        view
        returns (
            address initializer,
            uint256 totalSupply,
            uint256 rewardIndex,
            uint256 pendingInitialRewards,
            uint256 totalRewards
        )
    {
        if (!stakingPoolExists(stakingToken)) revert StakingPoolNotExist();

        StakingPool storage pool = stakingPools[stakingToken];
        return (
            pool.initializer,
            pool.totalSupply,
            pool.rewardIndex,
            pool.pendingInitialRewards,
            pool.totalRewards
        );
    }

    /**
     * @notice Get creator pool information
     * @param stakingToken The token associated with the creator pool
     * @return initializer Address that initialized the pool
     * @return creator Address of the creator
     * @return totalRewards Total HYPE rewards for creator
     */
    function getCreatorPoolInfo(address stakingToken)
        external
        view
        returns (address initializer, address creator, uint256 totalRewards)
    {
        if (!creatorPoolExists(stakingToken)) revert StakingCreatorNotExist();

        CreatorPool storage pool = creatorPools[stakingToken];
        return (pool.initializer, pool.creator, pool.totalRewards);
    }

    /**
     * @notice Get staking account information
     * @param stakingToken The token associated with the staking pool
     * @param staker Address of the staker
     * @return balance Staked token balance
     * @return rewardIndex Last reward index when rewards were updated
     * @return earned Earned rewards ready to claim
     * @return unstakeDeadline Timestamp when unstaking is allowed
     */
    function getStakingAccountInfo(
        address stakingToken,
        address staker
    )
        external
        view
        returns (uint256 balance, uint256 rewardIndex, uint256 earned, uint256 unstakeDeadline)
    {
        if (!stakingAccountExists(stakingToken, staker)) revert StakingAccountNotExist();

        StakingAccount storage account = stakingAccounts[stakingToken][staker];
        return (account.balance, account.rewardIndex, account.earned, account.unstakeDeadline);
    }

    // === Admin Functions ===

    /**
     * @notice Update configuration
     * @param newDenyUnstakeDuration New deny unstake duration
     */
    function updateConfig(address newAdmin, uint256 newDenyUnstakeDuration) external onlyAdmin {
        config.admin = newAdmin;
        config.denyUnstakeDuration = newDenyUnstakeDuration;
    }

    // === Private Functions ===

    /**
     * @notice Calculates the pending rewards for a staking account
     * @param stakingPoolRewardIndex Current reward index of the staking pool
     * @param account The staking account to calculate rewards for
     * @return reward The amount of pending rewards
     */
    function _calculateRewards(
        uint256 stakingPoolRewardIndex,
        StakingAccount storage account
    )
        private
        view
        returns (uint256 reward)
    {
        if (account.balance == 0) {
            return 0;
        }

        uint256 rewardDiff = stakingPoolRewardIndex - account.rewardIndex;
        reward = (account.balance * rewardDiff) / MULTIPLIER;

        return reward;
    }

    /**
     * @notice Updates the rewards earned by a staking account based on the current reward index
     * @param stakingPoolRewardIndex Current reward index of the staking pool
     * @param account The staking account to update rewards for
     */
    function _updateRewards(
        uint256 stakingPoolRewardIndex,
        StakingAccount storage account
    )
        private
    {
        uint256 pendingRewards = _calculateRewards(stakingPoolRewardIndex, account);
        account.earned += pendingRewards;
        account.rewardIndex = stakingPoolRewardIndex;
    }

    /**
     * @notice Attempts to clean up a staking account if it has zero balance and zero earned rewards
     * @param stakingToken The token associated with the staking pool
     * @param staker The address of the staker whose account should be checked
     * @return isDeleted Whether the account was successfully deleted
     */
    function _tryCleanupEmptyAccount(
        address stakingToken,
        address staker
    )
        private
        returns (bool isDeleted)
    {
        if (!stakingAccountExists(stakingToken, staker)) {
            return false;
        }

        StakingAccount storage account = stakingAccounts[stakingToken][staker];

        if (account.balance == 0 && account.earned == 0) {
            delete stakingAccounts[stakingToken][staker];
            return true;
        }

        return false;
    }
}

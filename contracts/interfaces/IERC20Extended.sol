// SPDX-License-Identifier: MIT
pragma solidity ^0.8.23;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title IERC20Extended
 * @dev Interface that extends the standard IERC20 with additional metadata functions
 * This interface includes the optional metadata extension functions that are commonly used
 */
interface IERC20Extended is IERC20 {
    /**
     * @dev Returns the name of the token
     */
    function name() external view returns (string memory);

    /**
     * @dev Returns the symbol of the token
     */
    function symbol() external view returns (string memory);

    /**
     * @dev Returns the number of decimals used to get its user representation
     * For example, if `decimals` equals `2`, a balance of `505` tokens should
     * be displayed to a user as `5.05` (`505 / 10 ** 2`)
     */
    function decimals() external view returns (uint8);

    /**
     * @dev Returns the total amount of tokens in existence
     */
    function totalSupply() external view override returns (uint256);

    /**
     * @dev Returns the amount of tokens owned by `account`
     */
    function balanceOf(address account) external view override returns (uint256);

    /**
     * @dev Moves `amount` tokens from the caller's account to `to`
     *
     * Returns a boolean value indicating whether the operation succeeded
     *
     * Emits a {Transfer} event
     */
    function transfer(address to, uint256 amount) external override returns (bool);

    /**
     * @dev Returns the remaining number of tokens that `spender` will be
     * allowed to spend on behalf of `owner` through {transferFrom}. This is
     * zero by default
     *
     * This value changes when {approve} or {transferFrom} are called
     */
    function allowance(address owner, address spender) external view override returns (uint256);

    /**
     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens
     *
     * Returns a boolean value indicating whether the operation succeeded
     *
     * IMPORTANT: Beware that changing an allowance with this method brings the risk
     * that someone may use both the old and the new allowance by unfortunate
     * transaction ordering. One possible solution to mitigate this race
     * condition is to first reduce the spender's allowance to 0 and set the
     * desired value afterwards:
     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729
     *
     * Emits an {Approval} event
     */
    function approve(address spender, uint256 amount) external override returns (bool);

    /**
     * @dev Moves `amount` tokens from `from` to `to` using the
     * allowance mechanism. `amount` is then deducted from the caller's
     * allowance
     *
     * Returns a boolean value indicating whether the operation succeeded
     *
     * Emits a {Transfer} event
     */
    function transferFrom(
        address from,
        address to,
        uint256 amount
    )
        external
        override
        returns (bool);
}
